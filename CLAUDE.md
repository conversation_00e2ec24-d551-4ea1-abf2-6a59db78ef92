# DreamVet API 项目概览

## 项目简介
这是一个基于 Laravel 12 的**兽医管理系统 API**，专门为宠物医院提供完整的业务管理功能。

## 技术栈
- **框架**: Laravel 12 (PHP 8.2+)
- **搜索**: Meilisearch
- **认证**: Laravel Sanctum
- **数据库**: MySQL (通过 Eloquent ORM)
- **缓存**: Redis
- **并发处理**: Spatie Fork
- **多语言**: 中文/英文支持

## 项目结构
```
app/
├── Enums/          # 枚举定义（状态、类型等）
├── Http/
│   ├── Controllers/V1/  # API控制器
│   └── Middleware/      # 中间件（登录、签名验证）
├── Logics/V1/      # 业务逻辑层
├── Models/         # 数据模型
└── Services/       # 服务层（搜索、会话、雪花ID）
```

## 核心业务模块

### 👤 会员管理 (`/member`)
- 宠物主人信息管理
- 档案管理
- 微信资料管理

### 🐕 宠物管理 (`/pet`)
- 宠物档案管理
- 疫苗驱虫记录
- 体重追踪记录

### 📝 挂号系统 (`/registration`)
- 预约挂号
- 医生分配
- 美容服务预约
- 挂号折扣计算

### 🏥 病历管理 (`/case`)
- 病历记录
- 诊断信息
- 生命体征记录
- 历史病历查询

### 🚪 门诊管理 (`/outpatient`)
- 门诊流程管理
- 状态变更追踪
- 医生在诊患者查询

### 🛏️ 住院管理 (`/inpatient`)
- 住院办理
- 住院状态管理
- 床位管理

### 💊 处方系统 (`/recipe`)
- 药品处方开具
- 处方模板管理
- 用药记录
- 处方编辑和删除

### 🔬 检测中心
- **化验检测** (`/test`): 化验项目管理、结果录入
- **影像检查** (`/image`): 影像检查管理、报告生成

### 🔄 转诊功能 (`/transfer`)
- 院内转诊
- 转院管理
- 转诊状态追踪

### 🖨️ 打印服务 (`/print`)
- 处方打印
- 病历打印
- 检测报告打印

## 认证机制
- **SMS 短信登录**: 通过手机短信验证码登录
- **医院选择验证**: 用户登录后需要选择医院
- **中间件权限控制**: 
  - `api.login:1` - 验证登录，不验证选择医院
  - `api.login` - 验证登录及选择医院
- **登录状态管理**: 实时登录状态检查

## 数据模型概览
项目包含超过50个数据模型，主要分类：

### 基础数据
- 地区信息 (`AddressProvinceModel`, `AddressCityModel`, `AddressAreaModel`)
- 宠物品种 (`PetBreedDictModel`, `PetCategoryDictModel`, `PetColorDictModel`)
- 医院信息 (`HospitalModel`, `HospitalUserModel`)

### 业务数据
- 会员宠物 (`MemberModel`, `MemberPetsModel`)
- 病历处方 (`CasesModel`, `RecipeModel`)
- 检测影像 (`TestModel`, `ImagesModel`)
- 住院门诊 (`InpatientModel`, `OutpatientModel`)

### 系统数据
- 用户权限 (`UsersModel`, `User`)
- 日志记录 (`UserLoginLogModel`, `CasesCompletedChangeLogModel`)

## 开发命令
```bash
# 开发环境启动
composer run dev

# 测试
composer run test

# 数据库迁移
php artisan migrate

# 队列监听
php artisan queue:listen --tries=1

# 日志查看
php artisan pail --timeout=0

# 查看数据库表结构
php artisan tinker
# 然后在交互模式中执行：
# --execute="print_r(DB::select('DESCRIBE table_name'));")
```

## API 版本
- **当前版本**: v1
- **路由前缀**: `/api/v1`
- **控制器命名空间**: `App\Http\Controllers\V1`

## 特色功能
1. **完整的宠物医院管理流程**: 从挂号到出院的全流程数字化管理
2. **灵活的权限控制**: 基于中间件的多级权限验证
3. **强大的搜索功能**: 集成 Meilisearch 提供高性能搜索
4. **并发处理支持**: 使用 Spatie Fork 处理并发任务
5. **多语言支持**: 中文/英文双语言支持
6. **完善的日志系统**: 详细的操作日志和状态变更记录

## 代码约定
- 遵循 Laravel 标准代码规范
- 使用 PSR-4 自动加载
- 业务逻辑层 (Logics) 分离
- 枚举类统一管理常量
- 统一的 API 响应格式
- **详细编码规范请参考 DreamVet-Coding-Standards.md**

# CaseLogic 代码风格参考

## 核心业务逻辑结构
基于 `app/Logics/V1/CaseLogic.php` 作为代码风格和架构设计的参考标准：

### 1. 类结构规范
```php
namespace App\Logics\V1;

use Arr;
use DB;
use Log;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\*;
use App\Models\*;

class CaseLogic extends Logic
{
    // 静态方法实现业务逻辑
    public static function MethodName(params): LogicResult
}
```

### 2. 方法命名规范
- **Get**开头：获取数据方法（如 `GetCaseDetail`, `GetValidCaseById`）
- **Edit**开头：编辑数据方法（如 `EditCaseDiagnose`, `EditCasePetVitalSign`）
- **Search**开头：搜索查询方法（如 `SearchCaseList`）
- **Check**开头：验证方法（如 `CheckEndAbleBySourceType`）

### 3. 参数处理标准
```php
// 参数验证
if (empty($caseId) || empty($publicParams))
{
    return self::Fail('缺少必选参数', 400);
}

// 公共参数提取
$hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
$doctorId   = intval(Arr::get($publicParams, '_userId'));

// 业务参数处理
$keyword = trim(Arr::get($searchParams, 'keyword', ''));
$page    = intval(Arr::get($searchParams, 'page', 1)) ?? PageEnum::DefaultPageIndex->value;
```

### 4. 数据库操作规范
```php
// 查询条件构建
$getWhere = [
    'hospital_id' => $hospitalId,
    'status'      => 1,
    'finished'    => 1
];

// 批量查询
$getCaseRes = CasesModel::getData(
    fields: ['id', 'case_code', 'member_id'],
    where: $getWhere,
    orderBys: ['created_at' => 'desc'],
    pageIndex: $page,
    pageSize: $count
);
```

### 5. 事务处理模式
```php
try {
    DB::beginTransaction();
    
    // 业务操作
    $result = SomeModel::updateOne($id, $data);
    
    // 关联操作
    LogModel::insertOne($logData);
    
    DB::commit();
    return self::Success();
    
} catch (Throwable $throwable) {
    DB::rollBack();
    
    Log::error(__CLASS__ . '::' . __METHOD__ . ' 操作异常', [
        'code'    => $throwable->getCode(),
        'message' => $throwable->getMessage(),
        'file'    => $throwable->getFile(),
        'line'    => $throwable->getLine(),
        'trace'   => $throwable->getTraceAsString(),
    ]);
    
    return self::Fail('操作异常', 错误码);
}
```

### 6. 返回值规范
```php
// 成功返回
return self::Success(['data' => $result, 'total' => $count]);

// 失败返回
return self::Fail('错误信息', 错误码);

// 统一错误码区间
// 38000-38999: 病历相关错误
// 400: 参数错误
```

### 7. 业务验证模式
```php
// 权限验证
if ($getCaseRes['doctor_id'] != $doctorId) {
    return self::Fail('非本医生病历，不可操作', 38002);
}

// 状态验证
if ($getCaseRes['finished'] == 1) {
    return self::Fail('病历已完结，不可操作', 38003);
}

// 数据完整性验证
$mustFields = ['field1', 'field2'];
foreach ($mustFields as $field) {
    if (empty($data[$field])) {
        return self::Fail("缺少必填项：{$field}", 38007);
    }
}
```

### 8. 注释规范
```php
/**
 * 方法功能描述
 *
 * @param int   $caseId       病历ID
 * @param array $params       业务参数
 * @param array $publicParams 公共参数
 * @param bool  $isEdit       是否编辑模式
 *
 * @return LogicResult
 * @throws Throwable
 */
```

### 9. 数据处理规范
```php
// 数据格式化
$returnData = [
    'caseCode'   => $getCaseRes['case_code'],
    'createTime' => formatDisplayDateTime($getCaseRes['created_at']),
    'price'      => formatDisplayNumber($price),
    'weight'     => sprintf("%.2f", $weight),
];

// 枚举使用
$sourceInfo = [
    'id'   => $getCaseRes['source_type'],
    'name' => CaseSourceTypeEnum::getDescription($getCaseRes['source_type']),
];
```

这个CaseLogic类体现了DreamVet项目的核心架构设计理念，包含完整的业务逻辑封装、统一的错误处理、规范的数据验证和事务管理，是项目中其他Logic类的标准参考模板。

# DreamVet 核心编码规范 - 必须严格遵循

## 📋 分页处理规范
- **强制使用**: `PageEnum::DefaultPageIndex->value` (默认页码1)
- **强制使用**: `PageEnum::DefaultPageSize->value` (默认每页15条)
- **引入**: `use App\Enums\PageEnum;`
- **示例**: `$page = intval(Arr::get($searchParams, 'page', PageEnum::DefaultPageIndex->value)) ?: PageEnum::DefaultPageIndex->value;`

## 🔤 字符串处理规范
- **Logic层**: 必须使用 `trimWhitespace()` 函数处理字符串，不使用 `trim()`
- **Model层**: 不进行任何字符串处理，只负责数据查询
- **职责分离**: 参数预处理在Logic层完成，Model层专注数据操作
- **示例**: `$keywords = trimWhitespace(Arr::get($searchParams, 'keywords', ''));`

## 🔍 数据库查询优化规范
- **避免冗余连表**: 优先使用主表字段，避免不必要的JOIN操作
- **条码搜索**: 直接使用 `stock_item_shelf.item_barcode`，不连接 `item_barcode` 表
- **性能优先**: 减少表连接，提高查询效率
- **Model引用**: 只引入实际使用的Model类

## 🏗️ 架构分层规范
- **Logic层职责**: 参数验证、业务逻辑、数据格式化、错误处理
- **Model层职责**: 纯数据查询，不做业务判断和字符串处理
- **分离原则**: Logic处理业务，Model处理数据，职责清晰分离

## ⚠️ 关键提醒事项
1. **分页**: 所有分页必须使用PageEnum枚举
2. **字符串**: Logic层用trimWhitespace，Model层不处理
3. **查询**: 优先主表字段，避免冗余JOIN
4. **职责**: Logic处理业务，Model专注数据
5. **性能**: 简化查询逻辑，提高执行效率

这些规范基于实际开发中的优化经验，必须严格遵循，确保代码质量和性能。
