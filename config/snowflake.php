<?php

return [
    //数据中心ID
    'datacenter_id'     => env('SNOWFLAKE_DATACENTER_ID', 0),
    //设备ID：machine_id_config=config 时生效
    'machine_id'        => env('SNOWFLAKE_MACHINE_ID', 0),
    //设备ID获取配置：default使用默认值'0'/config使用本文件'machine_id'值/hostname使用解析的服务器hostname/auto使用mac地址转换
    'machine_id_config' => env('SNOWFLAKE_MACHINE_ID_CONFIG', 'default'),
    //hostname使用解析正则
    'hostname_regex'    => env('SNOWFLAKE_HOSTNAME_REGEX', '/server-(\d+)/'),
    //序列号获取器：shared_memory共享内存/redis
    'sequence_resolver' => env('SNOWFLAKE_SEQUENCE_RESOLVER'),
    //redis序列号获取器redis链接名称
    'redis_connection'  => env('SNOWFLAKE_REDIS_CONNECTION', 'default'),
];
