<?php

/**
 * StockItemShelfReduceLogic 测试用例
 * 
 * 测试所有出库场景的算法逻辑，不涉及数据库操作
 */

require_once '/opt/webroot/myj/dreamvet_api/app/Support/Stock/StockQuantityConversionHelper.php';

use App\Support\Stock\StockQuantityConversionHelper;

class StockReduceLogicTest
{
    /**
     * 模拟库存分配算法（不连接数据库）
     */
    public static function mockAllocateStockByFIFO(array $stockList, int $requiredTotalBulk, int $bulkRatio): array
    {
        $allocations       = [];
        $remainingRequired = $requiredTotalBulk;

        foreach ($stockList as $stock) {
            if ($remainingRequired <= 0) {
                break;
            }

            $stockId          = $stock['id'];
            $availablePackQty = intval($stock['effective_pack_quantity']);
            $availableBulkQty = intval($stock['effective_bulk_quantity']);

            // 第一步：优先消耗当前行的散装库存
            $useBulkQty        = min($remainingRequired, $availableBulkQty);
            $remainingRequired -= $useBulkQty;

            $usePackQty     = 0;
            $addBackBulkQty = 0; // 整装拆分后需要加回到当前行的散装数量

            // 第二步：散装不够时，拆分当前行的整装库存
            if ($remainingRequired > 0 && $availablePackQty > 0 && $bulkRatio > 1) {
                // 计算需要拆分多少个整装
                $neededPackQty = ceil($remainingRequired / $bulkRatio);
                $usePackQty    = min($neededPackQty, $availablePackQty);

                // 拆分的整装总共能提供多少散装
                $convertedBulkFromPack = $usePackQty * $bulkRatio;

                // 实际需要使用的散装数量
                $actualUsedBulkFromPack = min($remainingRequired, $convertedBulkFromPack);

                // 拆分后剩余的散装需要加回到当前行
                $addBackBulkQty = $convertedBulkFromPack - $actualUsedBulkFromPack;

                $remainingRequired -= $actualUsedBulkFromPack;
            }

            // 记录当前行的分配结果
            if ($useBulkQty > 0 || $usePackQty > 0) {
                $allocations[] = [
                    'stockId'         => $stockId,
                    'shelfCode'       => $stock['shelf_code'],
                    'usedPackQty'     => $usePackQty,      // 消耗的整装数量
                    'usedBulkQty'     => $useBulkQty,      // 消耗的散装数量  
                    'addBackBulkQty'  => $addBackBulkQty,  // 整装拆分后需要加回的散装数量
                    'originalPackQty' => $availablePackQty,
                    'originalBulkQty' => $availableBulkQty,
                    // 计算最终库存
                    'finalPackQty'    => $availablePackQty - $usePackQty,
                    'finalBulkQty'    => $availableBulkQty - $useBulkQty + $addBackBulkQty,
                ];
            }
        }

        return [
            'success' => $remainingRequired <= 0,
            'allocations' => $allocations,
            'remaining' => $remainingRequired
        ];
    }

    /**
     * 运行所有测试用例
     */
    public static function runAllTests()
    {
        echo "=== StockItemShelfReduceLogic 测试用例 ===\n\n";

        // 测试场景1：你提到的具体场景
        self::testCase1_YourScenario();

        // 测试场景2：纯散装出库
        self::testCase2_BulkOnlyOutbound();

        // 测试场景3：纯整装出库
        self::testCase3_PackOnlyOutbound();

        // 测试场景4：跨多行库存出库
        self::testCase4_MultiRowOutbound();

        // 测试场景5：整装完全拆分场景
        self::testCase5_CompletePackConversion();

        // 测试场景6：库存不足场景
        self::testCase6_InsufficientStock();

        // 测试场景7：多种效期混合出库
        self::testCase7_MultiExpireDateOutbound();

        echo "=== 所有测试完成 ===\n";
    }

    /**
     * 测试场景1：你提到的具体场景
     * 记录ID:1，整散比1:2，整装:1、散装:1；出2个散装
     */
    private static function testCase1_YourScenario()
    {
        echo "测试场景1：你的具体场景\n";
        echo "库存：1整装+1散装，整散比1:2，出库2散装\n";

        $stockList = [
            [
                'id' => 1,
                'shelf_code' => 'A001',
                'effective_pack_quantity' => 1,
                'effective_bulk_quantity' => 1,
                'expired_date' => '2025-12-31'
            ]
        ];

        $result = self::mockAllocateStockByFIFO($stockList, 2, 2);

        echo "结果：" . ($result['success'] ? "✅ 成功" : "❌ 失败") . "\n";
        if ($result['success']) {
            $allocation = $result['allocations'][0];
            echo "- 消耗整装：{$allocation['usedPackQty']}\n";
            echo "- 消耗散装：{$allocation['usedBulkQty']}\n";  
            echo "- 加回散装：{$allocation['addBackBulkQty']}\n";
            echo "- 最终库存：{$allocation['finalPackQty']}整装 + {$allocation['finalBulkQty']}散装\n";
        }
        echo "\n";
    }

    /**
     * 测试场景2：纯散装出库
     * 库存有足够散装，不需要拆整装
     */
    private static function testCase2_BulkOnlyOutbound()
    {
        echo "测试场景2：纯散装出库\n";
        echo "库存：2整装+5散装，整散比1:3，出库3散装\n";

        $stockList = [
            [
                'id' => 2,
                'shelf_code' => 'A002',
                'effective_pack_quantity' => 2,
                'effective_bulk_quantity' => 5,
                'expired_date' => '2025-12-31'
            ]
        ];

        $result = self::mockAllocateStockByFIFO($stockList, 3, 3);

        echo "结果：" . ($result['success'] ? "✅ 成功" : "❌ 失败") . "\n";
        if ($result['success']) {
            $allocation = $result['allocations'][0];
            echo "- 消耗整装：{$allocation['usedPackQty']}\n";
            echo "- 消耗散装：{$allocation['usedBulkQty']}\n";
            echo "- 加回散装：{$allocation['addBackBulkQty']}\n";
            echo "- 最终库存：{$allocation['finalPackQty']}整装 + {$allocation['finalBulkQty']}散装\n";
        }
        echo "\n";
    }

    /**
     * 测试场景3：纯整装出库
     * 出库数量正好是整装的倍数
     */
    private static function testCase3_PackOnlyOutbound()
    {
        echo "测试场景3：纯整装出库\n";
        echo "库存：3整装+1散装，整散比1:4，出库8散装(=2整装)\n";

        $stockList = [
            [
                'id' => 3,
                'shelf_code' => 'A003',
                'effective_pack_quantity' => 3,
                'effective_bulk_quantity' => 1,
                'expired_date' => '2025-12-31'
            ]
        ];

        $result = self::mockAllocateStockByFIFO($stockList, 8, 4);

        echo "结果：" . ($result['success'] ? "✅ 成功" : "❌ 失败") . "\n";
        if ($result['success']) {
            $allocation = $result['allocations'][0];
            echo "- 消耗整装：{$allocation['usedPackQty']}\n";
            echo "- 消耗散装：{$allocation['usedBulkQty']}\n";
            echo "- 加回散装：{$allocation['addBackBulkQty']}\n";
            echo "- 最终库存：{$allocation['finalPackQty']}整装 + {$allocation['finalBulkQty']}散装\n";
        }
        echo "\n";
    }

    /**
     * 测试场景4：跨多行库存出库
     * 第一行库存不够，需要使用第二行
     */
    private static function testCase4_MultiRowOutbound()
    {
        echo "测试场景4：跨多行库存出库\n";
        echo "行1：1整装+1散装，行2：2整装+3散装，整散比1:2，出库8散装\n";

        $stockList = [
            [
                'id' => 4,
                'shelf_code' => 'A004',
                'effective_pack_quantity' => 1,
                'effective_bulk_quantity' => 1,
                'expired_date' => '2025-11-30' // 先过期
            ],
            [
                'id' => 5,
                'shelf_code' => 'A005',
                'effective_pack_quantity' => 2,
                'effective_bulk_quantity' => 3,
                'expired_date' => '2025-12-31' // 后过期
            ]
        ];

        $result = self::mockAllocateStockByFIFO($stockList, 8, 2);

        echo "结果：" . ($result['success'] ? "✅ 成功" : "❌ 失败") . "\n";
        if ($result['success']) {
            foreach ($result['allocations'] as $i => $allocation) {
                echo "行" . ($i + 1) . " (ID:{$allocation['stockId']}):\n";
                echo "  - 消耗整装：{$allocation['usedPackQty']}\n";
                echo "  - 消耗散装：{$allocation['usedBulkQty']}\n";
                echo "  - 加回散装：{$allocation['addBackBulkQty']}\n";
                echo "  - 最终库存：{$allocation['finalPackQty']}整装 + {$allocation['finalBulkQty']}散装\n";
            }
        }
        echo "\n";
    }

    /**
     * 测试场景5：整装完全拆分场景
     * 整装全部拆完，散装有剩余
     */
    private static function testCase5_CompletePackConversion()
    {
        echo "测试场景5：整装完全拆分场景\n";
        echo "库存：2整装+0散装，整散比1:5，出库7散装\n";

        $stockList = [
            [
                'id' => 6,
                'shelf_code' => 'A006',
                'effective_pack_quantity' => 2,
                'effective_bulk_quantity' => 0,
                'expired_date' => '2025-12-31'
            ]
        ];

        $result = self::mockAllocateStockByFIFO($stockList, 7, 5);

        echo "结果：" . ($result['success'] ? "✅ 成功" : "❌ 失败") . "\n";
        if ($result['success']) {
            $allocation = $result['allocations'][0];
            echo "- 消耗整装：{$allocation['usedPackQty']}\n";
            echo "- 消耗散装：{$allocation['usedBulkQty']}\n";
            echo "- 加回散装：{$allocation['addBackBulkQty']}\n";
            echo "- 最终库存：{$allocation['finalPackQty']}整装 + {$allocation['finalBulkQty']}散装\n";
        }
        echo "\n";
    }

    /**
     * 测试场景6：库存不足场景
     */
    private static function testCase6_InsufficientStock()
    {
        echo "测试场景6：库存不足场景\n";
        echo "库存：1整装+1散装，整散比1:2，出库5散装（超出库存）\n";

        $stockList = [
            [
                'id' => 7,
                'shelf_code' => 'A007',
                'effective_pack_quantity' => 1,
                'effective_bulk_quantity' => 1,
                'expired_date' => '2025-12-31'
            ]
        ];

        $result = self::mockAllocateStockByFIFO($stockList, 5, 2);

        echo "结果：" . ($result['success'] ? "✅ 成功" : "❌ 失败，剩余需求：{$result['remaining']}") . "\n";
        echo "\n";
    }

    /**
     * 测试场景7：多种效期混合出库
     * 验证按效期优先出库
     */
    private static function testCase7_MultiExpireDateOutbound()
    {
        echo "测试场景7：多种效期混合出库\n";
        echo "3行库存，不同效期，整散比1:3，出库10散装\n";

        $stockList = [
            [
                'id' => 8,
                'shelf_code' => 'A008',
                'effective_pack_quantity' => 1,
                'effective_bulk_quantity' => 2,
                'expired_date' => '2025-10-31' // 最先过期
            ],
            [
                'id' => 9,
                'shelf_code' => 'A009', 
                'effective_pack_quantity' => 2,
                'effective_bulk_quantity' => 1,
                'expired_date' => '2025-11-30' // 中间过期
            ],
            [
                'id' => 10,
                'shelf_code' => 'A010',
                'effective_pack_quantity' => 1,
                'effective_bulk_quantity' => 4,
                'expired_date' => '2025-12-31' // 最后过期
            ]
        ];

        $result = self::mockAllocateStockByFIFO($stockList, 10, 3);

        echo "结果：" . ($result['success'] ? "✅ 成功" : "❌ 失败") . "\n";
        if ($result['success']) {
            foreach ($result['allocations'] as $i => $allocation) {
                echo "行" . ($i + 1) . " (ID:{$allocation['stockId']}):\n";
                echo "  - 消耗整装：{$allocation['usedPackQty']}\n";
                echo "  - 消耗散装：{$allocation['usedBulkQty']}\n";
                echo "  - 加回散装：{$allocation['addBackBulkQty']}\n";
                echo "  - 最终库存：{$allocation['finalPackQty']}整装 + {$allocation['finalBulkQty']}散装\n";
            }
        }
        echo "\n";
    }
}

// 执行测试
StockReduceLogicTest::runAllTests();