#
# ------------------------------------------------------------------------------
# -----------------------------说明【重要】-------------------------------------
# 本文件主要用来配置相关服务及服务器的地址
#
# 上线时，本文件会被删除，使用线上的固定文件
# 新加服务器配置，需要联系运维修改线上的.env文件
# 服务器地址，统一使用此文件配置，并使用例如 env('DB_HOST', '127.0.0.1')来获取
# 其他文件中不允许配置服务器地址
# ------------------------------------------------------------------------------

# 应用配置
# 应用名称|config('app.name')获取
APP_NAME='api'

# 运行环境|local、production、testing、staging|线上为production|config('app.env')获取
APP_ENV=local

# 是否调试模式|线上为false
APP_DEBUG=true

# 应用url,以便在运行Artisan任务时使用它
APP_URL=http://localhost

# 框架运行中用到，用来全局加密用 |config('app.key')获取
APP_KEY=base64:nm6JUs1QjsgSgnraoWC+Q+8xQ2mOPJLSoU81s53fcVw=

# 框架时区
APP_TIMEZONE=PRC

# 区域语言
APP_LOCALE=zh_CN

# 备用语言
APP_FALLBACK_LOCALE=en

# FAKE语言
APP_FAKER_LOCALE=en_US

# 维护模式的驱动程序。默认值为 file|file、database、cache
APP_MAINTENANCE_DRIVER=file

# 确定维护模式数据的存储位置file|file、database
# APP_MAINTENANCE_STORE=database

# CLI模式worker数量
PHP_CLI_SERVER_WORKERS=4

# BCRYPT加密回合
BCRYPT_ROUNDS=12

# ------------------------------------------------------------------------------

# 日志配置
# 默认日志通道  'stack', 'single', 'daily', 'slack' 等 |线上为daily
LOG_CHANNEL=daily

# 默认日志堆栈
LOG_STACK=single

# 作废功能日志通道
LOG_DEPRECATIONS_CHANNEL=null

# 日志轮换天数
LOG_DAILY_DAYS=30

# 日志级别 debug < info < notice < warning < error < critical < alert < emergency|线上为warning
LOG_LEVEL=debug

# ------------------------------------------------------------------------------

# SQL运行记录
SQL_LOG_ENABLE=true

# 数据库配置
# 默认数据库连接
DB_CONNECTION=mysql

# 数据主库连接
DB_MASTER_HOST=*************
DB_MASTER_PORT=3306
DB_MASTER_DATABASE=his
DB_MASTER_USERNAME=his
DB_MASTER_PASSWORD="MyJ@2025#%"

# 数据库从库连接
DB_SLAVER_HOST=*************
DB_SLAVER_PORT=3306
DB_SLAVER_DATABASE=his
DB_SLAVER_USERNAME=his_read
DB_SLAVER_PASSWORD=his_read

# ------------------------------------------------------------------------------

# SESSION配置
# SESSION驱动|database、file
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# ------------------------------------------------------------------------------

# Redis配置
REDIS_CLIENT=phpredis
REDIS_HOST=*************
REDIS_PASSWORD=null
REDIS_PORT=6379

# Memcached配置
#MEMCACHED_HOST=127.0.0.1

# ------------------------------------------------------------------------------

# 缓存配置
# 缓存存储方式|file、database、redis、memcached、dynamodb、mongodb
CACHE_STORE=file
#CACHE_PREFIX=

# ------------------------------------------------------------------------------

# 其他配置
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# ------------------------------------------------------------------------------
#  **重要**
#  **重要**

# ------------------------------------------------------------------------------

# 业务配置
# 系统标识
SYSTEM_PLATFORM_TYPE='api'
SYSTEM_PLATFORM_TYPE_ID=0

# 客户端最大时差
SYSTEM_TIME_MAX_DIFFERENCE=600
SYSTEM_API_SECRET_KEY='1D[nmhvl@k9&$%sNpG%%y!GEAnv#%N^B'

# 用户登录类
# redis驱动连接
SYSTEM_USER_REDIS_CONNECTION='user'

# 会话类
# redis驱动连接
SYSTEM_SESSION_REDIS_CONNECTION='session'
# redis保存token的key前缀
SYSTEM_SESSION_REDIS_TOKEN_KEY_PREFIX='api_token:'
# redis保存uid映射token集合的key前缀
SYSTEM_SESSION_REDIS_LOGIN_KEY_PREFIX='api_login:'
# 有效时长：单位秒
SYSTEM_SESSION_LIFETIME=259200
# 是否仅单设备登录（最后一次登录会强制其他登录离线）
SYSTEM_SESSION_SINGLE_DEVICE=true

# 雪花ID
# 设备ID配置方式
SNOWFLAKE_MACHINE_ID_CONFIG='auto'
# 解析HOSTNAME正则
SNOWFLAKE_HOSTNAME_REGEX='/server-n(\d+)/'
# 序列号获取器
SNOWFLAKE_SEQUENCE_RESOLVER='shared_memory'

# 搜索引擎配置
SEARCH_ENGINE=meilisearch
# Meilisearch配置
MEILISEARCH_HOST="http://*************:7700"
MEILISEARCH_KEY="wnyS2mGCJBqL1RRrgROJLVTapqhC3FnQMLpM6Wnmkig"
# Elasticsearch配置
ELASTICSEARCH_HOSTS="localhost:9200"

# PDF生成服务配置
# 服务节点，多个节点用,分割。请求时会随机请求
PDF_SERVICE_NODES=http://127.0.0.1:7001,http://*************:7001
# 服务超时时间，单位秒
PDF_SERVICE_TIMEOUT=3

# 图片服务器地址
IMAGE_SERVER_URL=http://img.dev.dreamvet.cn

# 佳博云打印服务配置
GPRINTER_MEMBER_CODE=BA4D4F318A6EFD31A8F04892E155F132
GPRINTER_API_KEY=7COKJGEVO8N40ILDZLFBG2LAXQXIQVRJ
GPRINTER_DEFAULT_GROUP_ID=75824

# ------------------------------------------------------------------------------
