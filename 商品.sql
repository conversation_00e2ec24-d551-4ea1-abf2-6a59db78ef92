/*
 Navicat Premium Data Transfer

 Source Server         : 萌悦家（230-测试）
 Source Server Type    : MySQL
 Source Server Version : 80405 (8.4.5)
 Source Host           : *************:3306
 Source Schema         : his

 Target Server Type    : MySQL
 Target Server Version : 80405 (8.4.5)
 File Encoding         : 65001

 Date: 16/05/2025 21:04:32
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for items_sale_price_operation
-- ----------------------------
DROP TABLE IF EXISTS `items_sale_price_operation`;
CREATE TABLE `items_sale_price_operation` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `item_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品类型 关联item_type.id',
  `item_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID，根据商品类型关联到不同的表的ID',
  `org_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属组织ID，his_org.id',
  `brand_id` int NOT NULL DEFAULT '0' COMMENT '所属品牌ID，his_brands.id',
  `province_id` int unsigned NOT NULL DEFAULT '0' COMMENT '省,对应province表的ID',
  `city_id` int unsigned NOT NULL DEFAULT '0' COMMENT '市,对应province_city表的ID',
  `hospital_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '所属医院ID, 对应his_hospitals.id',
  `pack_sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '整装统一销售价',
  `bulk_sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '散装统一销售价',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '1:组织统一售价 2:品牌售价 3:区域售价 4:医院售价',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_platform` tinyint unsigned DEFAULT '1' COMMENT '修改类型 1 mp 2api',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `item_type-item_id` (`item_type`,`item_id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `brand_id` (`brand_id`) USING BTREE,
  KEY `province_id` (`province_id`) USING BTREE,
  KEY `city_id` (`city_id`) USING BTREE,
  KEY `hospital_id` (`hospital_id`) USING BTREE,
  KEY `start_time` (`start_time`) USING BTREE,
  KEY `end_time` (`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品中心:销售价调价记录';

-- ----------------------------
-- Records of items_sale_price_operation
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for items_sale_price
-- ----------------------------
DROP TABLE IF EXISTS `items_sale_price`;
CREATE TABLE `items_sale_price` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `item_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品类型 关联item_type.id',
  `item_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID，根据商品类型关联到不同的表的ID',
  `org_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属组织ID，his_org.id',
  `brand_id` int NOT NULL DEFAULT '0' COMMENT '所属品牌ID，his_brands.id',
  `province_id` int unsigned NOT NULL DEFAULT '0' COMMENT '省,对应province表的ID',
  `city_id` int unsigned NOT NULL DEFAULT '0' COMMENT '市,对应province_city表的ID',
  `hospital_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '所属医院ID, 对应his_hospitals.id',
  `pack_sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '整装统一销售价',
  `bulk_sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '散装统一销售价',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `item_type-item_id` (`item_type`,`item_id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `brand_id` (`brand_id`) USING BTREE,
  KEY `province_id` (`province_id`) USING BTREE,
  KEY `city_id` (`city_id`) USING BTREE,
  KEY `hospital_id` (`hospital_id`) USING BTREE,
  KEY `start_time` (`start_time`) USING BTREE,
  KEY `end_time` (`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品中心:销售价';

-- ----------------------------
-- Records of items_sale_price
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_cost_price
-- ----------------------------
DROP TABLE IF EXISTS `item_cost_price`;
CREATE TABLE `item_cost_price` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `item_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品类型 关联item_type.id',
  `item_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID，根据商品类型关联到不同的表的ID',
  `cost_price` decimal(12,2) unsigned NOT NULL COMMENT '成本价',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `item_type-item_id` (`item_type`,`item_id`) USING BTREE,
  KEY `start_time` (`start_time`) USING BTREE,
  KEY `end_time` (`end_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:固定成本价(非库存管理类商品)';

-- ----------------------------
-- Records of item_cost_price
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_beauty
-- ----------------------------
DROP TABLE IF EXISTS `item_beauty`;
CREATE TABLE `item_beauty` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `english_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `alias_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '别名',
  `mem_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '助记码(大写首字母)',
  `first_sale_type_id` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '一级项目类型ID，关联item_sale_type.id',
  `second_sale_type_id` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '二级项目类型ID，关联item_sale_type.id',
  `first_category_id` int unsigned NOT NULL DEFAULT '0' COMMENT '一级分类ID，关联item_category.id',
  `second_category_id` int unsigned NOT NULL DEFAULT '0' COMMENT '二级分类ID，关联item_category.id',
  `use_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计量单位（开具单位）',
  `use_unit_id` int unsigned NOT NULL DEFAULT '0' COMMENT '计量单位id，关联item_unit.id',
  `is_fixed_cost_price` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否固定成本价 1:是 0:否',
  `cost_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '固定成本价',
  `sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '统一销售价',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '商品描述',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '2' COMMENT '-1:删除；0:待上线；1:上线；2:下线',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `deleted_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `alias_name` (`alias_name`) USING BTREE,
  KEY `first_sale_type_id` (`first_sale_type_id`) USING BTREE,
  KEY `second_sale_type_id` (`second_sale_type_id`) USING BTREE,
  KEY `first_category_id` (`first_category_id`) USING BTREE,
  KEY `second_category_id` (`second_category_id`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4000000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:洗美商品';

-- ----------------------------
-- Records of item_beauty
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_registration
-- ----------------------------
DROP TABLE IF EXISTS `item_registration`;
CREATE TABLE `item_registration` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '挂号商品uid，唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `alias_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '别名',
  `registration_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '挂号类型 1:普通门诊 2:夜间门诊 3:专家门诊',
  `start_time` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '可挂号开始时间',
  `end_time` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '可挂号结束时间',
  `sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '统一销售价',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '描述',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '2' COMMENT '-1:删除；0:待上线；1:上线；2:下线',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `deleted_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `alias_name` (`alias_name`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:挂号商品';

-- ----------------------------
-- Records of item_registration
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_package
-- ----------------------------
DROP TABLE IF EXISTS `item_package`;
CREATE TABLE `item_package` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '套餐主键ID',
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `english_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `alias_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '别名',
  `mem_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '助记码(大写首字母)',
  `package_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '套餐类型 1:服务型套餐 2:预售型套餐',
  `sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '套餐统一销售价',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '套餐描述',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '2' COMMENT '-1:删除；0:待上线；1:上线；2:下线',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `deleted_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `alias_name` (`alias_name`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3000000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务套餐：基础信息';

-- ----------------------------
-- Records of item_package
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_package_relation
-- ----------------------------
DROP TABLE IF EXISTS `item_package_relation`;
CREATE TABLE `item_package_relation` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `package_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '套餐ID，关联item_package.id',
  `item_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品类型 关联item_type.id',
  `item_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID，根据商品类型关联到不同的表的ID',
  `unit_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品单位类型 1: 整装 2:散装',
  `num` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `deleted_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `package_id` (`package_id`) USING BTREE,
  KEY `item_type-item_id` (`item_type`,`item_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务套餐：关联商品详情';

-- ----------------------------
-- Records of item_package_relation
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_suit
-- ----------------------------
DROP TABLE IF EXISTS `item_suit`;
CREATE TABLE `item_suit` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '组合项目主键ID',
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `english_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `alias_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '别名',
  `mem_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '助记码(大写首字母)',
  `sale_type_id` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '一级项目类型ID，关联item_sale_type.id',
  `use_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计量单位（用药单位）',
  `use_unit_id` int unsigned NOT NULL DEFAULT '0' COMMENT '计量单位id，关联item_unit.id',
  `is_print_items` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否打印子项目 1:打印 2：不打印',
  `is_use_items_price` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否使用子项目总价计价 1:是(子项目总价格即为组合价) 2：0否(使用组合定价)',
  `sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '组合统一销售价',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '组合描述',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '2' COMMENT '-1:删除；0:待上线；1:上线；2:下线',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `deleted_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `alias_name` (`alias_name`) USING BTREE,
  KEY `sale_type_id` (`sale_type_id`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2000000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品组合：基础信息';

-- ----------------------------
-- Records of item_suit
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_suit_relation
-- ----------------------------
DROP TABLE IF EXISTS `item_suit_relation`;
CREATE TABLE `item_suit_relation` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `suit_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '组合ID，关联item_suit.id',
  `item_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品类型 关联item_type.id',
  `item_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID，根据商品类型关联到不同的表的ID',
  `unit_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品单位类型 1: 整装 2:散装',
  `num` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `deleted_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `suit_id` (`suit_id`) USING BTREE,
  KEY `item_type-item_id` (`item_type`,`item_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品组合：关联商品详情';

-- ----------------------------
-- Records of item_suit_relation
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item
-- ----------------------------
DROP TABLE IF EXISTS `item`;
CREATE TABLE `item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品uid，唯一且无序',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `english_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `basis_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主要成分名称',
  `alias_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '别名',
  `mem_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '助记码(大写首字母)',
  `first_sale_type_id` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '一级项目类型ID，关联item_sale_type.id',
  `second_sale_type_id` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '二级项目类型ID，关联item_sale_type.id',
  `drug_type_id` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '药品类型ID，关联item_drug_type.id',
  `first_category_id` int unsigned NOT NULL DEFAULT '0' COMMENT '一级分类ID，关联item_category.id',
  `second_category_id` int unsigned NOT NULL DEFAULT '0' COMMENT '二级分类ID，关联item_category.id',
  `brand_id` int unsigned NOT NULL DEFAULT '0' COMMENT '品牌ID，关联item_brand.id',
  `pack_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '整装单位',
  `pack_unit_id` int unsigned NOT NULL DEFAULT '0' COMMENT '整装单位id，关联item_unit.id',
  `bulk_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '散装单位（采购单位/销售单位）',
  `bulk_unit_id` int unsigned NOT NULL DEFAULT '0' COMMENT '散装单位id，关联item_unit.id',
  `bulk_ratio` int unsigned NOT NULL DEFAULT '0' COMMENT '整散比',
  `use_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计量单位（用药单位）',
  `use_unit_id` int unsigned NOT NULL DEFAULT '0' COMMENT '计量单位id，关联item_unit.id',
  `use_ratio` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '计量比',
  `is_licensed` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否有兽药批号 0否 1是',
  `is_precise_metering` tinyint NOT NULL DEFAULT '0' COMMENT '是否处方精确计量品 0否 1是',
  `is_recipe_allow` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否处方可开具 0否 1是',
  `is_retail_allow` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否可零售 0否 1是',
  `is_pack_sale_allow` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否可整装销售 0否 1是',
  `is_shelf_life` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否保质期管理：0否；1是',
  `shelf_life` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '保质时长原内容:01-02-03（几年-几月-几日）',
  `shelf_life_day` int unsigned NOT NULL DEFAULT '0' COMMENT '保质时长天数（关联转换自保质时长原内容）',
  `over_life_day` int unsigned NOT NULL DEFAULT '0' COMMENT '临期预警提前天数',
  `is_receive_allow` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否可耗材领用出库 0否 1是',
  `pack_procurement_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '整装指导采购价',
  `bulk_procurement_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '散装指导采购价',
  `is_fixed_cost_price` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否固定成本价 1:是 0:否',
  `cost_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '固定成本价',
  `pack_sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '整装统一销售价',
  `bulk_sale_price` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '散装统一销售价',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '商品描述',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT '2' COMMENT '-1:删除；0:待上线；1:上线；2:下线',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `deleted_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `alias_name` (`alias_name`) USING BTREE,
  KEY `first_sale_type_id` (`first_sale_type_id`) USING BTREE,
  KEY `second_sale_type_id` (`second_sale_type_id`) USING BTREE,
  KEY `drug_type_id` (`drug_type_id`) USING BTREE,
  KEY `first_category_id` (`first_category_id`) USING BTREE,
  KEY `second_category_id` (`second_category_id`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1000001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:基础信息';

-- ----------------------------
-- Records of item
-- ----------------------------
BEGIN;
INSERT INTO `item` (`id`, `org_id`, `uid`, `name`, `english_name`, `basis_name`, `alias_name`, `mem_code`, `first_sale_type_id`, `second_sale_type_id`, `drug_type_id`, `first_category_id`, `second_category_id`, `brand_id`, `pack_unit`, `pack_unit_id`, `bulk_unit`, `bulk_unit_id`, `bulk_ratio`, `use_unit`, `use_unit_id`, `use_ratio`, `is_licensed`, `is_precise_metering`, `is_recipe_allow`, `is_retail_allow`, `is_pack_sale_allow`, `is_shelf_life`, `shelf_life`, `shelf_life_day`, `over_life_day`, `is_receive_allow`, `pack_procurement_price`, `bulk_procurement_price`, `is_fixed_cost_price`, `cost_price`, `pack_sale_price`, `bulk_sale_price`, `desc`, `remark`, `status`, `created_by`, `deleted_by`, `created_at`, `updated_at`, `deleted_at`) VALUES (1000000, 1, '9JHknfD', '速效救心丸', 'Quick-acting life-saving pill', '', '', '', 0, 0, 0, 0, 0, 0, '', 0, '', 0, 0, '', 0, 0.00, 0, 0, 0, 0, 0, 0, '', 0, 0, 0, 0.00, 0.00, 0, 0.00, 0.00, 0.00, NULL, NULL, 2, 0, 0, '2025-05-16 20:59:22.288', '2025-05-16 21:01:45.385', NULL);
COMMIT;

-- ----------------------------
-- Table structure for item_barcode
-- ----------------------------
DROP TABLE IF EXISTS `item_barcode`;
CREATE TABLE `item_barcode` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `item_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID，关联item.id',
  `item_barcode` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商品条码',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `item_id` (`item_id`) USING BTREE,
  KEY `item_barcode` (`item_barcode`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:条码';

-- ----------------------------
-- Records of item_barcode
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_brand
-- ----------------------------
DROP TABLE IF EXISTS `item_brand`;
CREATE TABLE `item_brand` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '品牌id',
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌简称',
  `chinese_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌中文全称',
  `english_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '品牌英文全称',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `is_show` tinyint NOT NULL DEFAULT '1' COMMENT '显示控制 0不可见  1 可见',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:品牌';

-- ----------------------------
-- Records of item_brand
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_unit
-- ----------------------------
DROP TABLE IF EXISTS `item_unit`;
CREATE TABLE `item_unit` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位名称',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:单位';

-- ----------------------------
-- Records of item_unit
-- ----------------------------
BEGIN;
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (1, 0, '盒', 1, 0, '2025-05-15 17:36:48.901', '2025-05-15 17:36:48.901');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (2, 0, '瓶', 1, 0, '2025-05-15 17:36:48.906', '2025-05-15 17:36:48.906');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (3, 0, '箱', 1, 0, '2025-05-15 17:36:48.923', '2025-05-15 17:36:48.923');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (4, 0, '包', 1, 0, '2025-05-15 17:36:48.928', '2025-05-15 17:36:48.928');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (5, 0, '支', 1, 0, '2025-05-15 17:36:48.935', '2025-05-15 17:36:48.935');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (6, 0, '片', 1, 0, '2025-05-15 17:36:48.940', '2025-05-15 17:36:48.940');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (7, 0, '丸', 1, 0, '2025-05-15 17:36:48.946', '2025-05-15 17:36:48.946');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (8, 0, '粒', 1, 0, '2025-05-15 17:36:48.952', '2025-05-15 17:36:48.952');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (9, 0, '滴', 1, 0, '2025-05-15 17:36:49.009', '2025-05-15 17:36:49.009');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (10, 0, '袋', 1, 0, '2025-05-15 17:36:49.052', '2025-05-15 17:36:49.052');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (11, 0, 'g', 1, 0, '2025-05-15 17:36:49.056', '2025-05-15 17:36:49.056');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (12, 0, 'mg', 1, 0, '2025-05-15 17:36:49.061', '2025-05-15 17:36:49.061');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (13, 0, 'ug', 1, 0, '2025-05-15 17:36:49.065', '2025-05-15 17:36:49.065');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (14, 0, 'ng', 1, 0, '2025-05-15 17:36:49.079', '2025-05-15 17:36:49.079');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (15, 0, 'kg', 1, 0, '2025-05-15 17:36:49.085', '2025-05-15 17:36:49.085');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (16, 0, 'L', 1, 0, '2025-05-15 17:36:49.091', '2025-05-15 17:36:49.091');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (17, 0, 'ml', 1, 0, '2025-05-15 17:36:49.096', '2025-05-15 17:36:49.096');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (18, 0, 'U', 1, 0, '2025-05-15 17:36:49.100', '2025-05-15 17:36:49.100');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (19, 0, '万U', 1, 0, '2025-05-15 17:36:49.105', '2025-05-15 17:36:49.105');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (20, 0, '头份', 1, 0, '2025-05-15 17:36:49.110', '2025-05-15 17:36:49.110');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (21, 0, '管', 1, 0, '2025-05-15 17:36:49.116', '2025-05-15 17:36:49.116');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (22, 0, '罐', 1, 0, '2025-05-15 17:36:49.120', '2025-05-15 17:36:49.120');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (23, 0, '个', 1, 0, '2025-05-15 17:36:49.131', '2025-05-15 17:36:49.131');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (24, 0, '套', 1, 0, '2025-05-15 17:36:49.137', '2025-05-15 17:36:49.137');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (25, 0, '条', 1, 0, '2025-05-15 17:36:49.143', '2025-05-15 17:36:49.143');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (26, 0, '根', 1, 0, '2025-05-15 17:36:49.149', '2025-05-15 17:36:49.149');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (27, 0, '板', 1, 0, '2025-05-15 17:36:49.187', '2025-05-15 17:36:49.187');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (28, 0, '双', 1, 0, '2025-05-15 17:36:49.232', '2025-05-15 17:36:49.232');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (29, 0, '副', 1, 0, '2025-05-15 17:36:49.237', '2025-05-15 17:36:49.237');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (30, 0, '万IU', 1, 0, '2025-05-15 17:36:49.241', '2025-05-15 17:36:49.241');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (31, 0, '件', 1, 0, '2025-05-15 17:36:49.246', '2025-05-15 17:36:49.246');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (32, 0, 'ul', 1, 0, '2025-05-15 17:36:49.251', '2025-05-15 17:36:49.251');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (33, 0, '次', 1, 0, '2025-05-15 17:36:49.257', '2025-05-15 17:36:49.257');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (34, 0, '分钟', 1, 0, '2025-05-15 17:36:49.272', '2025-05-15 17:36:49.272');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (35, 0, '半小时', 1, 0, '2025-05-15 17:36:49.278', '2025-05-15 17:36:49.278');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (36, 0, '小时', 1, 0, '2025-05-15 17:36:49.287', '2025-05-15 17:36:49.287');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (37, 0, '天', 1, 0, '2025-05-15 17:36:49.292', '2025-05-15 17:36:49.292');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (38, 0, '单位', 1, 0, '2025-05-15 17:36:49.297', '2025-05-15 17:36:49.297');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (39, 0, '斤', 1, 0, '2025-05-15 17:36:49.302', '2025-05-15 17:36:49.302');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (40, 0, '块', 1, 0, '2025-05-15 17:36:49.306', '2025-05-15 17:36:49.306');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (41, 0, '磅', 1, 0, '2025-05-15 17:36:49.312', '2025-05-15 17:36:49.312');
INSERT INTO `item_unit` (`id`, `org_id`, `name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (42, 0, '桶', 1, 0, '2025-05-15 17:36:49.316', '2025-05-15 17:36:49.316');
COMMIT;

-- ----------------------------
-- Table structure for item_category
-- ----------------------------
DROP TABLE IF EXISTS `item_category`;
CREATE TABLE `item_category` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '唯一且无序',
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `parent_id` int unsigned NOT NULL DEFAULT '0' COMMENT '上一级ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `alias_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类别名',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路径',
  `is_leaf` tinyint DEFAULT NULL COMMENT '是否为叶子节点（终极类）',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uid` (`uid`),
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `parent_id` (`parent_id`) USING BTREE,
  KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:分类';

-- ----------------------------
-- Records of item_category
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_drug_type
-- ----------------------------
DROP TABLE IF EXISTS `item_drug_type`;
CREATE TABLE `item_drug_type` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `alias_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '别名',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:药品类型';

-- ----------------------------
-- Records of item_drug_type
-- ----------------------------
BEGIN;
INSERT INTO `item_drug_type` (`id`, `name`, `alias_name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (6, '普药', '', 1, 0, '2025-05-15 17:36:47.343', '2025-05-15 17:36:47.343');
INSERT INTO `item_drug_type` (`id`, `name`, `alias_name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (7, '中药', '', 1, 0, '2025-05-15 17:36:47.356', '2025-05-15 17:36:47.356');
INSERT INTO `item_drug_type` (`id`, `name`, `alias_name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (8, '毒麻药', '', 1, 0, '2025-05-15 17:36:47.374', '2025-05-15 17:36:47.374');
INSERT INTO `item_drug_type` (`id`, `name`, `alias_name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (9, '保健品', '', 1, 0, '2025-05-15 17:36:47.381', '2025-05-15 17:36:47.381');
INSERT INTO `item_drug_type` (`id`, `name`, `alias_name`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (10, '处方粮', '', 1, 0, '2025-05-15 17:36:47.386', '2025-05-15 17:36:47.386');
COMMIT;

-- ----------------------------
-- Table structure for item_sale_type
-- ----------------------------
DROP TABLE IF EXISTS `item_sale_type`;
CREATE TABLE `item_sale_type` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int unsigned NOT NULL DEFAULT '0' COMMENT '上一级ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `alias_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '别名',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `is_leaf` tinyint unsigned DEFAULT '0' COMMENT '是否为叶子节点（终极类）1是 0否',
  `is_manage_stock` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否管理库存 1:管理，0：不管理',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:项目类型';

-- ----------------------------
-- Records of item_sale_type
-- ----------------------------
BEGIN;
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (1, 0, '普通商品、药品', '商品、药品', '', 0, 1, 1, 0, '2025-05-15 17:36:47.472', '2025-05-15 17:36:47.472');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (2, 0, '化验检查项目', '化验', '', 0, 1, 1, 0, '2025-05-15 17:36:47.487', '2025-05-15 17:36:47.487');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (3, 0, '影像检查项目', '影像', '', 0, 1, 1, 0, '2025-05-15 17:36:47.492', '2025-05-15 17:36:47.492');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (4, 0, '医疗处置项目', '处置', '', 0, 1, 1, 0, '2025-05-15 17:36:47.497', '2025-05-15 17:36:47.497');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (5, 0, '普美容洗护项目', '美容洗护', '', 0, 1, 1, 0, '2025-05-15 17:36:47.503', '2025-05-15 17:36:47.503');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (10, 1, '商品', '商品', '', 1, 1, 1, 0, '2025-05-15 17:36:47.540', '2025-05-15 17:36:47.540');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (11, 1, '药品', '药品', '', 1, 1, 1, 0, '2025-05-15 17:36:47.575', '2025-05-15 17:36:47.575');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (12, 1, '保健品', '保健品', '', 1, 1, 1, 0, '2025-05-15 17:36:47.580', '2025-05-15 17:36:47.580');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (13, 1, '辅助保健品', '辅助保健品', '', 1, 1, 1, 0, '2025-05-15 17:36:47.586', '2025-05-15 17:36:47.586');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (14, 1, '器械', '器械', '', 1, 1, 1, 0, '2025-05-15 17:36:47.590', '2025-05-15 17:36:47.590');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (15, 1, '化验耗材', '化验耗材', '', 1, 1, 1, 0, '2025-05-15 17:36:47.595', '2025-05-15 17:36:47.595');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (16, 2, '生化', '生化', '', 1, 0, 1, 0, '2025-05-15 17:36:47.601', '2025-05-15 17:36:47.601');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (17, 2, '血液', '血液', '', 1, 0, 1, 0, '2025-05-15 17:36:47.607', '2025-05-15 17:36:47.607');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (18, 2, '尿液', '尿液', '', 1, 0, 1, 0, '2025-05-15 17:36:47.612', '2025-05-15 17:36:47.612');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (19, 2, '粪便', '粪便', '', 1, 0, 1, 0, '2025-05-15 17:36:47.616', '2025-05-15 17:36:47.616');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (20, 2, '体液和分泌物', '体液和分泌物', '', 1, 0, 1, 0, '2025-05-15 17:36:47.621', '2025-05-15 17:36:47.621');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (21, 2, '组织病理', '组织病理', '', 1, 0, 1, 0, '2025-05-15 17:36:47.627', '2025-05-15 17:36:47.627');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (22, 2, '物理检查', '物理检查', '', 1, 0, 1, 0, '2025-05-15 17:36:47.640', '2025-05-15 17:36:47.640');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (23, 3, '超声', '超声', '', 1, 0, 1, 0, '2025-05-15 17:36:47.652', '2025-05-15 17:36:47.652');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (24, 3, 'DR', 'DR', '', 1, 0, 1, 0, '2025-05-15 17:36:47.657', '2025-05-15 17:36:47.657');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (25, 3, 'CT', 'CT', '', 1, 0, 1, 0, '2025-05-15 17:36:47.663', '2025-05-15 17:36:47.663');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (26, 3, '核磁', '核磁', '', 1, 0, 1, 0, '2025-05-15 17:36:47.708', '2025-05-15 17:36:47.708');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (27, 4, '诊断性', '诊断性', '', 1, 0, 1, 0, '2025-05-15 17:36:47.715', '2025-05-15 17:36:47.715');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (28, 4, '治疗性', '治疗性', '', 1, 0, 1, 0, '2025-05-15 17:36:47.719', '2025-05-15 17:36:47.719');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (29, 4, '急救性', '急救性', '', 1, 0, 1, 0, '2025-05-15 17:36:47.727', '2025-05-15 17:36:47.727');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (30, 4, '康复性', '康复性', '', 1, 0, 1, 0, '2025-05-15 17:36:47.732', '2025-05-15 17:36:47.732');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (31, 4, '护理性', '护理性', '', 1, 0, 1, 0, '2025-05-15 17:36:47.736', '2025-05-15 17:36:47.736');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (32, 5, '美容', '美容', '', 1, 0, 1, 0, '2025-05-15 17:36:47.742', '2025-05-15 17:36:47.742');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (33, 5, '洗澡', '洗澡', '', 1, 0, 1, 0, '2025-05-15 17:36:47.780', '2025-05-15 17:36:47.780');
INSERT INTO `item_sale_type` (`id`, `parent_id`, `name`, `alias_name`, `path`, `is_leaf`, `is_manage_stock`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (34, 5, '护理', '护理', '', 1, 0, 1, 0, '2025-05-15 17:36:47.785', '2025-05-15 17:36:47.785');
COMMIT;

-- ----------------------------
-- Table structure for item_support_pet_category
-- ----------------------------
DROP TABLE IF EXISTS `item_support_pet_category`;
CREATE TABLE `item_support_pet_category` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `org_id` int NOT NULL DEFAULT '0' COMMENT '所属组织ID',
  `item_type` tinyint NOT NULL DEFAULT '1' COMMENT '商品类型 关联item_type.id',
  `item_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID，根据商品类型关联到不同的表的ID',
  `category_id` int NOT NULL DEFAULT '0' COMMENT '宠物类别ID，pet_category_dict.id',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `item_type-item_id` (`item_type`,`item_id`) USING BTREE,
  KEY `category_id` (`category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品:商品支持的宠物类别白名单（不设置则为全部支持，设置则使用白名单）';

-- ----------------------------
-- Records of item_support_pet_category
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for item_type
-- ----------------------------
DROP TABLE IF EXISTS `item_type`;
CREATE TABLE `item_type` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `relation_table` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联表',
  `is_suit_support` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否支持添加到组合 1:支持，0：不支持',
  `is_package_support` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否支持添加到套餐 1:支持，0：不支持',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态 1正常 0作废',
  `created_by` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品中心:商品大类型';

-- ----------------------------
-- Records of item_type
-- ----------------------------
BEGIN;
INSERT INTO `item_type` (`id`, `name`, `relation_table`, `is_suit_support`, `is_package_support`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (1, 'sku单品', 'item', 1, 1, 1, 0, '2025-05-15 17:36:47.262', '2025-05-15 17:36:47.262');
INSERT INTO `item_type` (`id`, `name`, `relation_table`, `is_suit_support`, `is_package_support`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (2, 'spu组合商品', 'item_suit', 0, 1, 1, 0, '2025-05-15 17:36:47.267', '2025-05-15 17:36:47.267');
INSERT INTO `item_type` (`id`, `name`, `relation_table`, `is_suit_support`, `is_package_support`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (3, '挂号商品', 'item_registration', 1, 1, 1, 0, '2025-05-15 17:36:47.271', '2025-05-15 17:36:47.271');
INSERT INTO `item_type` (`id`, `name`, `relation_table`, `is_suit_support`, `is_package_support`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (4, '洗美商品', 'item_beauty', 1, 1, 1, 0, '2025-05-15 17:36:47.292', '2025-05-15 17:36:47.292');
INSERT INTO `item_type` (`id`, `name`, `relation_table`, `is_suit_support`, `is_package_support`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (5, '服务套餐', 'item_package', 0, 0, 1, 0, '2025-05-15 17:36:47.296', '2025-05-15 17:36:47.296');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
