<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
//
use App\Http\Middleware\SignMiddleware;
use App\Http\Middleware\LoginMiddleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->remove([ConvertEmptyStringsToNull::class]);

        $middleware->alias([
                               'api.sign' => SignMiddleware::class,
                               'api.login' => LoginMiddleware::class,
                           ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
