<?php

return [
    200   => 'Success',
    400   => 'Request parameter error',
    401   => 'Signature error',
    402   => 'Client time difference is too large',
    403   => 'Client time difference is too large',
    429   => 'Requests are too frequent',
    500   => 'Failure',
    1000  => 'User not logged in',
    1001  => 'Login not complete: hospital not selected',

    // 10000-19999 用户相关
    10000 => 'Invalid mobile phone number format',
    10010 => 'Invalid gender selection',
    10011 => 'Invalid birthdate',
    10100 => 'User not found',
    10101 => 'User already exists, cannot register again',
    10102 => 'User is disabled',
    10160 => 'Too many incorrect password attempts, please try again later',
    10180 => 'User registration failed',
    10190 => 'Login failed',
    10191 => 'Login failed: incorrect password',
    10196 => 'Failed to select hospital during login',
    10199 => 'Logout failed',

    // 10500-10599 用户验证码
    10500 => 'Invalid verification code type',
    10501 => 'Invalid verification code delivery method',
    10520 => 'Verification code request limit exceeded',
    10521 => 'Too many invalid code entries, please try again later',
    10551 => 'Error sending verification code',
    10552 => 'Failed to send verification code',
    10570 => 'Incorrect verification code',
    10571 => 'Verification code expired',
    10580 => 'Verification code invalid, please request again',
    10590 => 'No verification code record found',

    // 10600-10699 用户权限类
    10600 => 'No account for this hospital',
    10602 => 'User account disabled for this hospital',

    // 20000-29999 医院相关
    20100 => 'Hospital not found',
    20101 => 'Hospital already exists, cannot register again',
    20102 => 'Hospital is disabled',

    // 30000-31999 会员相关
    30000 => 'Pet owner already exists in this hospital',
    30001 => 'Failed to retrieve pet owner information',
    30002 => 'Pet owner not found',
    30003 => 'Pet owner phone number cannot be changed',
    30004 => 'Failed to update pet owner information',
    30005 => 'Failed to update pet owner profile',
    30006 => 'No permission to operate on this pet owner',

    // 32000-32999 宠物相关
    32000 => 'Pet not found',
    32001 => 'Failed to add pet',
    32002 => 'Failed to update pet information',
    32003 => 'Failed to add pet vaccination record',
    32004 => 'Failed to add pet deworming record',

    // 35000-35999 挂号相关
    35000 => 'Unknown registration source',
    35001 => 'Registration reason not found',
    35002 => 'Registration type invalid',
    35003 => 'Attending doctor not found',
    35004 => 'Invalid registration method',
    35005 => 'Unknown registration item',
    35006 => 'Pet owner not associated with this hospital, cannot register',
    35007 => 'Pet has an unused registration, cannot register again',
    35008 => 'Pet has an unfinished outpatient visit, cannot register',
    35009 => 'Pet has an unfinished hospitalization, cannot register',
    35010 => 'Registration failed',
    35011 => 'Registration record not found',

    // 36000-36999 门诊相关
    36000 => 'Outpatient visit not found',
    36001 => 'Visit status changed, cannot accept',
    36002 => 'Cannot accept outpatient from another doctor',
    36003 => 'Visit status changed, cannot pause treatment',
    36004 => 'No permission for this outpatient visit',
    36005 => 'Unpaid prescription exists, cannot conclude treatment',
    36006 => 'Treatment result not found',
    36007 => 'Abnormal visit status operation',

    // 37000-37999 住院相关
    37000 => 'Selected ward does not exist',
    37001 => 'Selected bed does not exist',
    37002 => 'Bed already in use, cannot select',
    37003 => 'Pet has unfinished hospitalization, cannot admit again',

    // 38000-38999 病历相关
    38000 => 'Medical record not found',
    38001 => 'Cannot operate on a medical record from another hospital',
    38002 => 'Cannot operate on another doctor’s medical record',
    38003 => 'Medical record already completed, cannot operate',
    38004 => 'Medical record is closed, cannot operate',
    38005 => 'Failed to update diagnostic information',
    38006 => 'Pet vital signs for medical record not found',

    // 39000-39099 处方相关
    39000 => 'Prescription not found',
    39001 => 'Prescription cannot be modified: medical record is already closed',
    39002 => 'Cannot operate on another doctor’s prescription',
    39003 => 'Prescription save failed: medication validation error',
    39004 => 'Prescription save failed: lab test validation error',
    39005 => 'Prescription save failed: imaging validation error',
    39006 => 'Prescription save failed: procedure validation error',
    39007 => 'Prescription save failed: combination validation error',
    39008 => 'Failed to save prescription',
    39009 => 'Prescription detail not found',

    // 39100-39199 处方模版相关
    39100 => 'Prescription template not found',
    39101 => 'Prescription template item not found',
    39102 => 'Failed to add item to prescription template, please retry',
    39103 => 'No permission to operate on this prescription template',
    39104 => 'Failed to delete prescription template, please retry',

    // 40000-40100 化验相关
    40000 => 'Lab test item not found',
    40001 => 'Lab technician not found',
    40002 => 'Lab test already in progress, cannot operate',
    40003 => 'Lab result already exists, cannot operate',
    40004 => 'Failed to start lab test',
    40005 => 'Lab test not started, cannot record results',
    40006 => 'Lab template expired, cannot use',
    40007 => 'Lab template missing required sample location',
    40008 => 'Lab template missing required sampling method',
    40009 => 'Lab template contains invalid dimension parameters',
    40010 => 'Lab template contains invalid indicator parameters',
    40011 => 'Lab report template indicators missing',
    40012 => 'Lab result save failed',
    40013 => 'Lab report template structure changed, please refresh and retry',

    // 40101-40200 影像相关
    40101 => 'Imaging record not found',
    40102 => 'Imaging technician not found',
    40103 => 'Imaging already in progress, cannot operate',
    40104 => 'Imaging result already exists, cannot operate',
    40105 => 'Imaging not started, cannot record results',
    40106 => 'Medical record closed, cannot enter imaging result',
    40107 => 'Imaging template expired, cannot use',
    40108 => 'Imaging template missing required body part',
    40109 => 'Imaging template missing required findings/descriptions',
    40110 => 'Imaging template missing required guidance/comments',
    40111 => 'Imaging images required',
    40112 => 'Imaging save failed due to invalid data',
    40113 => 'Failed to start imaging',
];
