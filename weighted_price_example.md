# 加权成本价计算功能实现说明

## 功能概述

在 `StockItemShelfLogic` 类中实现了商品入库后自动计算加权成本价的功能。

## 计算公式

```
加权成本价 = （（本次入库整装数 × 本次入库整装价格） + （本次入库散装数 × 本次入库散装价格） + （当前库存数 × 当前加权价））/ （本次入库数 + 当前库存数）
```

其中：
- 本次入库数 = （入库整装数 × 整散比） + （散装入库数）
- 当前库存数 = （当前整装数 × 整散比） + （当前散装数）

## 实现的方法

### 1. `calculateWeightedCostPrice()` - 计算加权成本价
```php
private static function calculateWeightedCostPrice(
    int $addPackQuantity,      // 本次入库整装数
    float $addPackPrice,       // 本次入库整装价格
    int $addBulkQuantity,      // 本次入库散装数
    float $addBulkPrice,       // 本次入库散装价格
    int $currentPackQuantity,  // 当前整装库存数
    int $currentBulkQuantity,  // 当前散装库存数
    float $currentWeightedPrice, // 当前加权价格
    int $bulkRatio             // 整散比
): LogicResult
```

### 2. `getCurrentStockQuantity()` - 获取当前库存数量
```php
private static function getCurrentStockQuantity(int $itemId, int $hospitalId): LogicResult
```

### 3. `getCurrentWeightedPrice()` - 获取当前加权价格
```php
private static function getCurrentWeightedPrice(int $itemId, int $orgId, int $brandId, int $hospitalId): LogicResult
```

### 4. `updateWeightedPrice()` - 更新加权价格到数据库
```php
private static function updateWeightedPrice(
    int $itemId,
    int $orgId,
    int $brandId,
    int $hospitalId,
    float $newWeightedPrice,
    int $userId
): LogicResult
```

## 集成到入库流程

在 `addStockItemShelf()` 方法中，入库完成后会自动：

1. 获取入库前的当前库存数量
2. 获取当前的加权价格
3. 计算新的加权成本价
4. 将新的加权价格保存到 `stock_item_daily_price` 表

## 计算示例

假设：
- 本次入库：整装 10 个，价格 100 元/个；散装 5 个，价格 10 元/个
- 当前库存：整装 20 个，散装 10 个
- 当前加权价格：8 元/个
- 整散比：10（1 个整装 = 10 个散装）

计算过程：
1. 本次入库数（散装单位）= (10 × 10) + 5 = 105 个
2. 当前库存数（散装单位）= (20 × 10) + 10 = 210 个
3. 本次入库总金额 = (10 × 100) + (5 × 10) = 1050 元
4. 当前库存总金额 = 210 × 8 = 1680 元
5. 新加权价格 = (1050 + 1680) / (105 + 210) = 2730 / 315 = 8.67 元

## 测试

已创建完整的单元测试 `tests/Unit/StockItemShelfLogicTest.php`，包含以下测试场景：

1. 正常情况下的加权成本价计算
2. 零库存情况下的计算
3. 零入库数量情况下的计算
4. 全部为零的边界情况

运行测试：
```bash
php artisan test tests/Unit/StockItemShelfLogicTest.php
```

## 数据库表

使用 `stock_item_daily_price` 表存储加权价格记录，主要字段：
- `item_id`: 商品ID
- `org_id`: 组织ID
- `brand_id`: 品牌ID
- `hospital_id`: 医院ID
- `cost_price`: 加权成本价
- `price_date`: 价格日期
- `created_by`: 创建人ID

## 注意事项

1. 加权价格计算在数据库事务中进行，确保数据一致性
2. 如果计算过程中出现异常，会回滚整个入库操作
3. 价格保留2位小数
4. 当总数量为0时，加权价格设为0
5. 每次入库都会创建新的价格记录，保持历史记录
