<?php

function index()
{
    try
    {
        $params = [
            'version'   => '1.0.0.299',
            'timestamp' => time(),
            'nonce'     => bin2hex(random_bytes(8)),
            'signature' => '',
            'payload'   => json_encode([]),
        ];
        ksort($params);


        // 业务参数
        $payloadParams = [

            'petUid'           => 'UQGkMN2Ce',
            'petSterileStatus' => 1,
            'petSterileDate'   => null
        ];

        $params['payload'] = json_encode($payloadParams);

        // 接口签名
        $params['signature'] = hash_hmac('sha256',
                                         $params['payload'] . $params['timestamp'],
                                         $params['nonce'] . '1D[nmhvl@k9&$%sNpG%%y!GEAnv#%N^B');


        $params = array_merge($params);
        $res    = curl_post("http://api227.dev.dreamvet.cn/api/v1/case/list",
                            $params,
                            [CURLOPT_HTTPHEADER => ["Authorization: Bearer 9fd2e55167c2e1d5926cbe811ae980da9fdb66384f35bb712eca735eb8775565"]]);
        echo $res;
        if (json_validate($res))
        {
            print_r(json_decode($res, true));
        }
    } catch (Exception $e)
    {
        var_dump('error:' . $e->getCode(), $e->getMessage());
    }
}

function curl_post($url, $post = null, $options = [])
{
    $defaults = [
        CURLOPT_POST           => 1,
        CURLOPT_HEADER         => 0,
        CURLOPT_URL            => $url,
        CURLOPT_FRESH_CONNECT  => 1,
        CURLOPT_RETURNTRANSFER => 1,
        CURLOPT_FORBID_REUSE   => 1,
        CURLOPT_TIMEOUT        => 0,
        CURLOPT_POSTFIELDS     => http_build_query($post)
    ];

    $ch = curl_init();
    curl_setopt_array($ch, ($options + $defaults));

    if (!$result = curl_exec($ch))
    {
        trigger_error(curl_error($ch));
    }

    //	curl_close($ch);

    return $result;
}

index();
