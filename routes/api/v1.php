<?php

use Illuminate\Support\Facades\Route;

Route::prefix('/v1')
     ->namespace('App\Http\Controllers\V1')
     ->group(function () {
         //登录
         Route::prefix('/login')
              ->group(function () {
                  Route::post('/senLoginSms', 'LoginController@SendLoginSms');
                  Route::post('/login', 'LoginController@Login');
                  Route::post('/chooseHospital', 'LoginController@ChooseHospital')
                       ->middleware('api.login:1');//验证登录，不验证选择医院
                  Route::post('/logout', 'LoginController@Logout');
                  Route::post('/status', 'LoginController@LoginStatus')
                       ->middleware('api.login');//验证登录及选择医院
              });

         // 基础数据
         Route::prefix('/basic')
              ->group(function () {
                  // 不需要验证选择医院
                  Route::middleware(['api.login:1'])
                       ->group(function () {
                           Route::post('/getMemberFromOptions', 'BasicEnumDataController@GetMemberFromOptions');
                           Route::post('/getProvinceOptions', 'BasicEnumDataController@GetProvinceOptions');
                           Route::post('/getPetCategoryOptions', 'BasicEnumDataController@GetPetCategoryOptions');
                           Route::post('/getPetBreedOptions', 'BasicEnumDataController@GetPetBreedOptions');
                           Route::post('/getPetColorOptions', 'BasicEnumDataController@GetPetColorOptions');
                           Route::post('/getPetLiveStatusOptions', 'BasicEnumDataController@GetPetLiveStatusOptions');
                           Route::post('/getTemperatureTypeOptions', 'BasicEnumDataController@GetTemperatureTypeOptions');
                           Route::post('/getBloodPressureTypeOptions', 'BasicEnumDataController@GetBloodPressureTypeOptions');
                           Route::post('/getNursingLevelOptions', 'BasicEnumDataController@GetNursingLevelOptions');
                           Route::post('/getRecipeItemUsageOptions', 'BasicEnumDataController@GetRecipeItemUsageOptions');
                           Route::post('/getItemBrandOptions', 'BasicEnumDataController@GetItemBrandOptions');
                       });

                  // 需要验证选择医院
                  Route::middleware(['api.login'])
                       ->group(function () {
                           Route::post('/getRoomsOptions', 'BasicEnumDataController@GetRoomsOptions');
                           Route::post('/getRoomBedsOptions', 'BasicEnumDataController@GetRoomBedsOptions');
                       });
              });

         Route::middleware(['api.login'])
              ->group(function () {

                  // 医院用户相关
                  Route::prefix('/user')
                       ->group(function () {
                           Route::post('/getUserByBusiness', 'UserController@GetUserByBusiness');
                       });

                  // 医院相关
                  Route::prefix('/hospital')
                       ->group(function () {
                           Route::post('/getTransferHospital', 'HospitalController@GetTransferHospitalList');
                       });

                  // 会员相关
                  Route::prefix('/member')
                       ->group(function () {
                           Route::post('/list', 'MemberController@Lists');
                           Route::post('/add', 'MemberController@Add');
                           Route::post('/edit', 'MemberController@Edit');
                           Route::post('/getMicroProfile', 'MemberController@GetMicroProfile');
                           Route::post('/editMicroProfile', 'MemberController@EditMicroProfile');
                           Route::post('/detail', 'MemberController@Detail');
                       });

                  // 宠物相关
                  Route::prefix('/pet')
                       ->group(function () {
                           Route::post('/list', 'PetController@Lists');
                           Route::post('/add', 'PetController@Add');
                           Route::post('/edit', 'PetController@Edit');
                           Route::post('/detail', 'PetController@Detail');
                           Route::post('/addPrevent', 'PetController@AddPrevent');
                           Route::post('/getPreventList', 'PetController@GetPetPreventList');
                           Route::post('/addDeworming', 'PetController@AddDeworming');
                           Route::post('/getDewormingList', 'PetController@GetPetDewormingList');
                           Route::post('/getPetWeightList', 'PetController@GetPetWeightList');
                       });

                  // 挂号相关
                  Route::prefix('/registration')
                       ->group(function () {
                           Route::post('/getRegistrationReasonOptions',
                                       'RegistrationController@GetRegistrationReasonsOptions');
                           Route::post('/getRegistrationTypeOptions',
                                       'RegistrationController@GetRegistrationTypeOptions');
                           Route::post('/getHospitalDoctorOptions', 'RegistrationController@GetHospitalDoctorOptions');
                           Route::post('/getHospitalBeautyOptions', 'RegistrationController@GetHospitalBeautyOptions');
                           Route::post('/getRegistrationPetLists', 'RegistrationController@GetRegistrationPetLists');
                           Route::post('/add', 'RegistrationController@AddPetRegistration');
                           Route::post('/getDiscount', 'RegistrationController@GetRegistrationDiscount');
                       });

                  // 病历相关
                  Route::prefix('/case')
                       ->group(function () {
                           Route::post('/list', 'CaseController@Lists');
                           Route::post('/getCaseDetail', 'CaseController@GetCaseDetail');
                           Route::post('/editPetVitalSign', 'CaseController@EditPetVitalSign');
                           Route::post('/editCaseDiagnose', 'CaseController@EditCaseDiagnose');
                           Route::post('/getTreatmentOutcome', 'CaseController@GetTreatmentOutcome');
                           Route::post('/historyCaseList', 'CaseController@GetHistoryCases');
                           Route::post('/historyCaseDetail', 'CaseController@GetHistoryCaseDetail');
                           Route::post('/summary', 'CaseController@GetCaseSummary');
                           Route::post('/getDiseaseCategory', 'CaseController@GetDiseaseCategory');
                           Route::post('/editHistoryPetVitalSign', 'CaseController@EditHistoryPetVitalSign');
                           Route::post('/editHistoryDiagnose', 'CaseController@EditHistoryCaseDiagnose');
                       });

                  // 门诊相关
                  Route::prefix('/outpatient')
                       ->group(function () {
                           Route::post('/total', 'OutpatientController@GetOutpatientTotal');
                           Route::post('/list', 'OutpatientController@Lists');
                           Route::post('/changeStatus', 'OutpatientController@ChangeStatus');
                           Route::post('/getInTreatmentOutpatient',
                                       'OutpatientController@GetDoctorInTreatmentOutpatient');
                           Route::post('/checkEndOutpatient', 'OutpatientController@CheckEndOutpatient');
                       });

                  // 住院相关
                  Route::prefix('/inpatient')
                       ->group(function () {
                           Route::post('/admit', 'InpatientController@Admit');
                           Route::post('/list', 'InpatientController@Lists');
                           Route::post('/changeStatus', 'InpatientController@ChangeStatus');
                           Route::post('/getInTreatmentInpatient', 'InpatientController@GetDoctorInTreatmentInpatient');
                           Route::post('/checkEndInpatient', 'InpatientController@CheckEndInpatient');
                       });

                  // 处方相关
                  Route::prefix('/recipe')
                       ->group(function () {
                           Route::post('/searchItems', 'RecipeController@SearchRecipeItems');
                           Route::post('/add', 'RecipeController@AddRecipe');
                           Route::post('/verifyEdit', 'RecipeController@VerifyEditRecipe');
                           Route::post('/edit', 'RecipeController@EditRecipe');
                           Route::post('/delete', 'RecipeController@DeleteRecipe');
                           Route::post('/verifyDelete', 'RecipeController@VerifyDeleteRecipe');
                           Route::post('/list', 'RecipeController@GetCaseRecipesList');
                           Route::post('/detail', 'RecipeController@GetRecipeDetail');
                           Route::post('/historyRecipeList', 'RecipeController@GetHistoryRecipes');
                           Route::post('/historyRecipeDetail', 'RecipeController@GetHistoryRecipeDetail');
                           Route::post('/skipFollowUp', 'RecipeController@SkipFollowUp');
                           Route::post('/followUpAssistant', 'RecipeController@FollowUpAssistant');
                       });

                  // 处方模版相关
                  Route::prefix('/recipeTemplate')
                       ->group(function () {
                           Route::post('/options', 'RecipeTemplateController@GetTemplateOptions');
                           Route::post('/list', 'RecipeTemplateController@GetRecipeTemplateList');
                           Route::post('/detail', 'RecipeTemplateController@GetRecipeTemplateDetail');
                           Route::post('/add', 'RecipeTemplateController@AddRecipeTemplate');
                           Route::post('/delete', 'RecipeTemplateController@DeleteRecipeTemplate');
                       });

                  // 打印相关
                  Route::prefix('/print')
                       ->group(function () {
                           Route::post('/recipe', 'PrintController@PrintRecipe');
                           Route::post('/case', 'PrintController@PrintCase');
                           Route::post('/testReport', 'PrintController@PrintTestReport');
                           Route::post('/imageReport', 'PrintController@PrintImageReport');
                       });

                  // 检测中心-化验
                  Route::prefix('/test')
                       ->group(function () {
                           Route::post('/filterOptions', 'TestController@GetTestFilterOptions');
                           Route::post('/list', 'TestController@Lists');
                           Route::post('/detail', 'TestController@Detail');
                           Route::post('/start', 'TestController@StartTest');
                           Route::post('/followTester', 'TestController@FollowTester');
                           Route::post('/saveResult', 'TestController@SaveResult');
                           Route::post('/templateList', 'TestController@GetTestTemplateList');
                           Route::post('/viewTemplateReport', 'TestController@GetTestTemplateReport');
                       });

                  // 检测中心-影像
                  Route::prefix('/image')
                       ->group(function () {
                           Route::post('/filterOptions', 'ImageController@GetImageFilterOptions');
                           Route::post('/list', 'ImageController@Lists');
                           Route::post('/detail', 'ImageController@Detail');
                           Route::post('/start', 'ImageController@StartTest');
                           Route::post('/followTester', 'ImageController@FollowTester');
                           Route::post('/saveResult', 'ImageController@SaveResult');
                           Route::post('/templateList', 'ImageController@GetImageTemplateList');
                           Route::post('/viewTemplateReport', 'ImageController@GetTestTemplateReport');
                       });

                  // 处置中心

                  // 商品零售
                  Route::prefix('/retail')
                       ->group(function () {
                           Route::post('/searchItems', 'RetailController@SearchRetailItems');
                           Route::post('/createSheet', 'RetailController@CreateSheet');
                       });

                  // 美容洗澡
                  Route::prefix('/beauty')
                       ->group(function () {
                           Route::post('/searchItems', 'BeautyServiceController@SearchBeautyServiceItems');
                           Route::post('/createSheet', 'BeautyServiceController@CreateSheet');
                           Route::post('/editSheet', 'BeautyServiceController@EditSheet');
                           Route::post('/deleteSheet', 'BeautyServiceController@DeleteSheet');
                           Route::post('/unpaidSheetFilterOptions', 'BeautyServiceController@UnpaidSheetFilterOptions');
                           Route::post('/unpaidSheetList', 'BeautyServiceController@UnpaidSheetList');
                           Route::post('/sheetDetail', 'BeautyServiceController@SheetDetail');
                           Route::post('/serviceFilterOptions', 'BeautyServiceController@ServiceFilterOptions');
                           Route::post('/serviceList', 'BeautyServiceController@ServiceList');
                           Route::post('/followExecutor', 'BeautyServiceController@FollowServiceExecutor');
                       });

                  // 转诊相关（院内转诊、内部转院）
                  Route::prefix('/transfer')
                       ->group(function () {
                           Route::post('/filterOptions', 'TransferController@GetTransferFilterOptions');
                           Route::post('/list', 'TransferController@Lists');
                           Route::post('/rejectAccept', 'TransferController@RejectAccept');
                       });

                  // 采购相关
                  Route::prefix('/purchase')
                       ->group(function () {
                           Route::post('/filterOptions', 'PurchaseController@GetPurchaseFilterOptions');
                           Route::post('/typeOptions', 'PurchaseController@GetPurchaseTypeOptions');
                           Route::post('/supplierOptions', 'PurchaseController@GetPurchaseSupplier');
                           Route::post('/searchItems', 'PurchaseController@SearchPurchaseItems');
                           Route::post('/list', 'PurchaseController@Lists');
                           Route::post('/detail', 'PurchaseController@Detail');
                           Route::post('/add', 'PurchaseController@Add');
                           Route::post('/edit', 'PurchaseController@Edit');
                           Route::post('/delete', 'PurchaseController@Delete');
                       });

                  // 采购-到货签收
                  Route::prefix('/purchaseReceived')
                       ->group(function () {
                           Route::post('/list', 'PurchaseReceivedController@Lists');
                           Route::post('/detail', 'PurchaseReceivedController@Detail');
                           Route::post('/add', 'PurchaseReceivedController@Add');
                       });

                  // 采购-到货入库
                  Route::prefix('purchaseInbound')
                       ->group(function () {
                           Route::post('/list', 'PurchaseInboundController@Lists');
                           Route::post('/stockInbound', 'PurchaseInboundController@StockInbound');
                       });

                  // 仓储-药房相关
                  Route::prefix('/warehouse')
                       ->group(function () {
                           Route::post('/list', 'WarehouseController@Lists');
                           Route::post('/add', 'WarehouseController@Add');
                           Route::post('/edit', 'WarehouseController@Edit');
                           Route::post('/delete', 'WarehouseController@Delete');
                           Route::post('/shelvesList', 'WarehouseController@GetShelvesList');
                           Route::post('/addShelf', 'WarehouseController@AddShelf');
                           Route::post('/shelfSlotList', 'WarehouseController@GetShelfSlotList');
                           Route::post('/addShelfSlot', 'WarehouseController@AddShelfSlot');
                       });

                  // 库存
                  Route::prefix('/stock')
                       ->group(function () {
                           Route::post('/list', 'StockItemShelfController@Lists');
                           Route::post('/warningList', 'StockItemShelfController@WarningList');
                           Route::post('/expireList', 'StockItemShelfController@ExpireList');
                           Route::post('/detail', 'StockItemShelfController@Detail');
                       });

                  // 拣货出库
                  Route::prefix('/stockPicking')
                       ->group(function () {
                           Route::post('/filterOptions', 'StockPendingController@GetPendingOrderFilterOptions');
                           Route::post('/list', 'StockPendingController@Lists');
                           Route::post('/detail', 'StockPendingController@Detail');
                       });

                  // 耗材出库
                  Route::prefix('/stockConsumables')
                       ->group(function () {
                           Route::post('/filterOptions', 'StockConsumablesController@GetConsumablesFilterOptions');
                           Route::post('/searchItems', 'StockConsumablesController@SearchConsumablesItems');
                           Route::post('/list', 'StockConsumablesController@Lists');
                           Route::post('/detail', 'StockConsumablesController@Detail');
                           Route::post('/add', 'StockConsumablesController@Add');
                           Route::post('/edit', 'StockConsumablesController@Edit');
                       });

                  // 调拨出库
                  Route::prefix('/stockTransfer')
                       ->group(function () {
                           Route::post('/list', 'StockTransferController@Lists');
                           Route::post('/detail', 'StockTransferController@Detail');
                       });

                  // 系统设置-小票打印机
                  Route::prefix('/setting/printer')
                       ->group(function () {
                           Route::post('/add', 'SettingPrinterController@Add');
                           Route::post('/edit', 'SettingPrinterController@Edit');
                           Route::post('/list', 'SettingPrinterController@List');
                           Route::post('/refresh', 'SettingPrinterController@Refresh');
                           Route::post('/printLabel', 'SettingPrinterController@PrintLabel');
                       });

                  // 客户充值
                  Route::prefix('/balance/recharge')
                       ->group(function () {
                           Route::post('/activities', 'BalanceRechargeController@RechargeActivities');
                           Route::post('/createSheet', 'BalanceRechargeController@CreateRechargeSheet');
                       });

                  // 结算
                  Route::prefix('/checkout')
                       ->group(function () {
                           Route::post('/unpaidFilterOptions', 'CheckoutController@UnpaidFilterOptions');
                           Route::post('/unpaidList', 'CheckoutController@UnpaidList');
                       });
              });

         //登录状态守护中间件
         Route::middleware(['api.login'])
              ->group(function () {
                  //权限验证中间件
              });
     });
