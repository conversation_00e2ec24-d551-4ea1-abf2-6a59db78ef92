<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

if (config('app.debug'))
{
    Artisan::command('app:debug:uuid7', function () {
        $this->info(\Illuminate\Support\Str::uuid7());
    })->purpose('Generate uuid v7');

    Artisan::command('app:debug:generateUUID', function () {
        $this->info(generateUUID());
    })->purpose('Generate generateUUID use helper.php');

    Artisan::command('app:debug:snowflake:generate', function () {
        $this->info(\App\Facades\SnowflakeFacade::generateId());
    })->purpose('Generate Snowflake uuid');

    Artisan::command('app:debug:snowflake:parse', function () {
        $id = \App\Facades\SnowflakeFacade::generateId();
        $this->info(json_encode(\App\Facades\SnowflakeFacade::parseId($id)));
    })->purpose('Parse Snowflake uuid');

    Artisan::command('app:debug:snowflake:test', function () {
        $result = [];
        $start  = microtime(true);
        for ($i = 0; $i < 100; $i ++)
        {
            $result[] = \App\Facades\SnowflakeFacade::generateId();
        }
        $end = microtime(true);
        $this->info(sprintf('Generate 100000 id, Start: %s, End: %s, Total time: %s', $start, $end, $end - $start));

        \Log::debug('app:debug:snowflake:test', $result);
        $counted    = array_count_values($result); // 统计数组中每个值的出现次数
        $duplicates = array_filter($counted, function ($count) {
            return $count > 1; // 筛选出出现次数大于 1 的项
        });

        $this->info(sprintf('Duplicate: %s', json_encode($duplicates)));
    })->purpose('Generate Snowflake uuid test');

    Artisan::command('app:debug:snowflake:config', function () {
        $this->info(json_encode(\App\Facades\SnowflakeFacade::getConfiguration()));
    })->purpose('Generate Snowflake uuid');
}
