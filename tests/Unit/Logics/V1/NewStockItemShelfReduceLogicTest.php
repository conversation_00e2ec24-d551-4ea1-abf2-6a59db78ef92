<?php

namespace Tests\Unit\Logics\V1;

use App\Logics\V1\NewStockItemShelfReduceLogic;
use App\Support\Stock\StockQuantityConversionHelper;
use ReflectionClass;
use Tests\TestCase;

class NewStockItemShelfReduceLogicTest extends TestCase
{
    private static function getDeductFromShelvesMethod(): \ReflectionMethod
    {
        $class = new ReflectionClass(NewStockItemShelfReduceLogic::class);
        $method = $class->getMethod('_deductFromShelves');
        $method->setAccessible(true);
        return $method;
    }

    /**
     * @dataProvider deductScenariosProvider
     */
    public function testDeductFromShelvesLogic(string $message, int $totalDemandBulk, array $availableShelves, int $bulkRatio, array $expectedUpdates, array $expectedReduces)
    {
        $method = self::getDeductFromShelvesMethod();
        $logic = new NewStockItemShelfReduceLogic();

        // 定义模拟的公共参数和价格
        $publicParams = ['_hospitalId' => 1, '_userId' => 1, '_hospitalOrgId' => 1, '_hospitalBrandId' => 1];
        $reduceItem = ['itemId' => 1, 'type' => 1, 'subType' => 1, 'relationCode' => 'TEST001'];
        $itemPrice = ['packPrice' => 100, 'bulkPrice' => 10];

        $result = $method->invoke($logic, $totalDemandBulk, $availableShelves, $bulkRatio, $reduceItem, $publicParams, $itemPrice);

        // 仅比较我们关心的核心计算结果
        $this->assertEquals($expectedUpdates, $result['shelfUpdates'], "场景: {$message} - 货架更新不匹配");

        // 简化对 reduceRecords 的断言，只验证数量和关键ID
        $this->assertCount(count($expectedReduces), $result['reduceRecords'], "场景: {$message} - 扣减记录数量不匹配");

        foreach ($expectedReduces as $index => $expectedRecord) {
            $actualRecord = $result['reduceRecords'][$index];
            $this->assertEquals($expectedRecord['stock_item_shelf_id'], $actualRecord['stock_item_shelf_id'], "场景: {$message} - 记录[{$index}]货架ID不匹配");
            $this->assertEquals($expectedRecord['pack_quantity'], $actualRecord['pack_quantity'], "场景: {$message} - 记录[{$index}]整装数量不匹配");
            $this->assertEquals($expectedRecord['bulk_quantity'], $actualRecord['bulk_quantity'], "场景: {$message} - 记录[{$index}]散装数量不匹配");
        }
    }

    public static function deductScenariosProvider(): array
    {
        return [
            '场景1: 简单散装扣减' => [
                'message' => '简单散装扣减',
                'totalDemandBulk' => 5,
                'availableShelves' => [
                    ['id' => 1, 'effective_pack_quantity' => 10, 'effective_bulk_quantity' => 10, 'warehouse_id' => 1, 'item_barcode' => 'A', 'produce_date' => '2025-01-01', 'expired_date' => '2099-12-31', 'shelf_life' => 365, 'shelf_code' => 'A1']
                ],
                'bulkRatio' => 10,
                'expectedUpdates' => [
                    ['id' => 1, 'effective_pack_quantity' => 10, 'effective_bulk_quantity' => 5]
                ],
                'expectedReduces' => [
                    ['stock_item_shelf_id' => 1, 'pack_quantity' => 0, 'bulk_quantity' => 5]
                ]
            ],
            '场景2: 扣减需要拆包' => [
                'message' => '扣减需要拆包',
                'totalDemandBulk' => 15,
                'availableShelves' => [
                    ['id' => 2, 'effective_pack_quantity' => 2, 'effective_bulk_quantity' => 0, 'warehouse_id' => 1, 'item_barcode' => 'B', 'produce_date' => '2025-01-01', 'expired_date' => '2099-12-31', 'shelf_life' => 365, 'shelf_code' => 'A2']
                ],
                'bulkRatio' => 10,
                'expectedUpdates' => [
                    ['id' => 2, 'effective_pack_quantity' => 0, 'effective_bulk_quantity' => 5]
                ],
                'expectedReduces' => [
                    ['stock_item_shelf_id' => 2, 'pack_quantity' => 1, 'bulk_quantity' => 5]
                ]
            ],
            '场景3: 跨货架扣减' => [
                'message' => '跨货架扣减',
                'totalDemandBulk' => 15,
                'availableShelves' => [
                    ['id' => 3, 'effective_pack_quantity' => 1, 'effective_bulk_quantity' => 0, 'warehouse_id' => 1, 'item_barcode' => 'C1', 'produce_date' => '2025-01-01', 'expired_date' => '2099-11-30', 'shelf_life' => 365, 'shelf_code' => 'A3'],
                    ['id' => 4, 'effective_pack_quantity' => 1, 'effective_bulk_quantity' => 10, 'warehouse_id' => 1, 'item_barcode' => 'C2', 'produce_date' => '2025-01-01', 'expired_date' => '2099-12-31', 'shelf_life' => 365, 'shelf_code' => 'A4']
                ],
                'bulkRatio' => 10,
                'expectedUpdates' => [
                    ['id' => 3, 'effective_pack_quantity' => 0, 'effective_bulk_quantity' => 0],
                    ['id' => 4, 'effective_pack_quantity' => 1, 'effective_bulk_quantity' => 5]
                ],
                'expectedReduces' => [
                    ['stock_item_shelf_id' => 3, 'pack_quantity' => 1, 'bulk_quantity' => 0],
                    ['stock_item_shelf_id' => 4, 'pack_quantity' => 0, 'bulk_quantity' => 5]
                ]
            ],
            '场景4: 刚好用完所有库存' => [
                'message' => '刚好用完所有库存',
                'totalDemandBulk' => 25,
                'availableShelves' => [
                    ['id' => 5, 'effective_pack_quantity' => 2, 'effective_bulk_quantity' => 5, 'warehouse_id' => 1, 'item_barcode' => 'D', 'produce_date' => '2025-01-01', 'expired_date' => '2099-12-31', 'shelf_life' => 365, 'shelf_code' => 'A5']
                ],
                'bulkRatio' => 10,
                'expectedUpdates' => [
                    ['id' => 5, 'effective_pack_quantity' => 0, 'effective_bulk_quantity' => 0]
                ],
                'expectedReduces' => [
                    ['stock_item_shelf_id' => 5, 'pack_quantity' => 2, 'bulk_quantity' => 5]
                ]
            ],
        ];
    }
}
