# DreamVet API 编码规范

## 变量命名规范

### 1. 变量命名风格
- 使用有意义的变量名，避免使用get/set等前缀
- 变量名应该表达其包含的数据或操作结果
- 示例：
  ```php
  // ❌ 错误的命名
  $warehouseListRes = WarehouseLogic::GetWarehouseList($hospitalId);
  
  // ✅ 正确的命名
  $getWarehouseListRes = WarehouseLogic::GetWarehouseList($hospitalId);
  ```

### 2. 逻辑层变量命名
- 数据查询结果变量：`$xxxRes`
- 返回结果变量：`$returnXxxList`
- 循环中的当前项：`$curInfo`
- 示例：
  ```php
  $getWarehouseListRes = StockWarehouseModel::getData(...);
  $returnWarehouseList = [];
  foreach ($getWarehouseListRes as $curInfo) {
      $returnWarehouseList[] = [...];
  }
  ```

## Model层使用规范

### 1. 数据查询方法
- **禁止**在Logic层直接使用Eloquent查询方法
- **必须**使用Model类的`getData()`方法进行数据查询
- 示例：
  ```php
  // ❌ 错误的用法
  $warehouses = StockWarehouseModel::where('hospital_id', $hospitalId)
      ->where('delete_status', 0)
      ->where('status', 1)
      ->select('uid', 'name')
      ->get();
  
  // ✅ 正确的用法
  $warehouses = StockWarehouseModel::getData(
      ['uid', 'name'],
      ['hospital_id' => $hospitalId, 'delete_status' => 0, 'status' => 1]
  );
  ```

### 2. Model继承说明
- 所有Model类都继承自`App\Models\Model`
- 基类提供了统一的`getData()`方法用于数据查询
- 不要使用基类中不存在的参数，如`$onWritePdo`

## Helpers函数使用规范

### 1. 获取医院相关信息
从`app/helpers.php`中获取公共参数的正确方法：

```php
// 获取医院ID
$hospitalId = getPublicParamsHospitalId();

// 获取医院组织ID
$hospitalOrgId = getPublicParamsHospitalOrgId();

// 获取医院用户ID
$hospitalUserId = getPublicParamsHospitalUserId();
```

### 2. 常用Helper函数
- `getRequestReservedParameters()` - 获取请求保留参数
- `outputJsonResult()` - 输出JSON成功结果
- `outputJsonError()` - 输出JSON错误结果
- `generateUUID()` - 生成UUID
- `smsSend()` - 发送短信
- `checkValidCellphone()` - 验证手机号
- `calculatePetAge()` - 计算宠物年龄
- `getNowDateTime()` - 获取当前时间

## 空数据处理规范

### 1. 空数据返回
- 当数据为空时，不要返回无意义的空数组
- 使用`self::Success()`而非`self::Success([])`
- 示例：
  ```php
  // ❌ 错误的返回
  if (empty($data)) {
      return self::Success([]);
  }
  
  // ✅ 正确的返回
  if (empty($data)) {
      return self::Success();
  }
  ```

### 2. 成功返回格式
- 有数据时返回具体数据结构
- 按照项目约定返回格式化的数据
- 示例：
  ```php
  return self::Success(['data' => $returnWarehouseList]);
  ```

## 参数验证规范

### 1. 必选参数验证
- 使用`empty()`而非`<= 0`进行参数验证
- 错误消息要明确指出缺少的参数
- 示例：
  ```php
  // ✅ 正确的验证
  if (empty($hospitalId)) {
      return self::Fail('获取仓库药房列表，缺少医院ID必选参数', 400);
  }
  ```

## 常见错误总结

1. **变量命名错误**：使用get/set等前缀
2. **Model使用错误**：直接使用Eloquent查询而非getData方法
3. **空数据返回错误**：返回无意义的空数组
4. **Helper函数使用错误**：使用不存在的函数名
5. **参数验证错误**：使用不当的验证条件

## 代码审查要点

在代码审查时，请重点关注：
- 变量命名是否有意义
- 是否正确使用Model的getData方法
- 空数据处理是否正确
- Helper函数调用是否正确
- 参数验证是否合理

---

*本文档会根据项目开发过程中发现的问题持续更新*
