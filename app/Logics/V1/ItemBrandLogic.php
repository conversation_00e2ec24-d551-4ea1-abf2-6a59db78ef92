<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\ItemBrandModel;
use App\Models\ItemUsageModel;

/**
 * Class ItemBrandLogic
 * @package App\Logics\V1
 */
class ItemBrandLogic extends Logic
{

    /**
     * 获取项目品牌
     * @return LogicResult
     */
    public static function GetBrandList(): LogicResult
    {
        $getBrandListRes = ItemBrandModel::getData(where: ['status' => 1, 'is_show' => 1]);
        if (empty($getBrandListRes))
        {
            return self::Success();
        }

        $returnBrandList = [];
        foreach ($getBrandListRes as $curInfo)
        {
            $returnBrandList[] = [
                'uid'  => $curInfo['uid'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success($returnBrandList);
    }
}
