<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Enums\CaseSourceTypeEnum;
use App\Enums\InpatientStatusEnum;
use App\Enums\OutpatientStatusEnum;
use App\Enums\TreatmentOutcomeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\InpatientTreatmentStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\CasesModel;
use App\Models\InpatientModel;
use App\Models\MemberPetsModel;
use App\Models\PetVitalSignModel;
use App\Models\RegistrationsModel;
use App\Models\InpatientDoctorStatusModel;
use App\Models\InpatientChangeStatusLogModel;

class InpatientLogic extends Logic
{

    /**
     * 办理住院
     *
     * @param int   $outpatientId
     * @param array $inpatientParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AdmitPatient(int $outpatientId, array $inpatientParams, array $publicParams): LogicResult
    {
        if (empty($outpatientId))
        {
            return self::Fail('办理住院，缺少门诊ID参数', 400);
        }
        if (empty($inpatientParams))
        {
            return self::Fail('办理住院，缺少住院必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('办理住院，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId', 0));
        $doctorId        = intval(Arr::get($publicParams, '_userId', 0));
        $doctorUid       = trim(Arr::get($publicParams, '_userUid', ''));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId) || empty($doctorId) || empty($doctorUid))
        {
            return self::Fail('办理住院，缺少公共必选参数', 400);
        }

        // 住院参数
        $gradeId = intval(Arr::get($inpatientParams, 'gradeId', 0));
        $days    = intval(Arr::get($inpatientParams, 'days', 0));
        $roomId  = intval(Arr::get($inpatientParams, 'roomId', 0));
        $bedId   = intval(Arr::get($inpatientParams, 'bedId', 0));
        if (empty($gradeId))
        {
            return self::Fail('办理住院，缺少住院等级必选参数', 37005);
        }
        if (empty($days))
        {
            return self::Fail('办理住院，缺少住院天数必选参数', 37005);
        }
        if (empty($roomId))
        {
            return self::Fail('办理住院，缺少病房ID必选参数', 37005);
        }
        if (empty($bedId))
        {
            return self::Fail('办理住院，缺少床位ID必选参数', 37005);
        }

        // 验证床位是否可用
        $getAvailableRes = BedLogic::IsBedAvailable($roomId, $bedId, $publicParams);
        if ($getAvailableRes->isFail())
        {
            return $getAvailableRes;
        }

        // 获取门诊信息
        $getValidOutpatientRes = OutpatientLogic::GetValidOutpatientById($outpatientId, $publicParams);
        if ($getValidOutpatientRes->isFail())
        {
            return $getValidOutpatientRes;
        }

        $getOutpatientRes = $getValidOutpatientRes->getData();
        if ($getOutpatientRes['status'] != OutpatientStatusEnum::InTreatment->value)
        {
            return self::Fail('门诊状态未就诊中，不可办理住院', 37003);
        }

        // 获取宠物信息
        $memberId  = $getOutpatientRes['member_id'];
        $petId     = $getOutpatientRes['pet_id'];
        $getPetRes = PetLogic::GetValidPetByIdOrUid($petId, memberId: $memberId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 获取宠物有效的住院记录
        $getInpatientPetRes = InpatientModel::getInpatientByPetId($hospitalId,
                                                                  $memberId,
                                                                  [$petId],
                                                                  [InpatientStatusEnum::Inpatient->value]);
        if ($getInpatientPetRes->isNotEmpty())
        {
            return self::Fail('当前宠物存在未完结住院', 37004);
        }

        // 验证转住院门诊是否可以结束诊断
        $getCheckEndOutpatientRes = OutpatientLogic::CheckEndOutpatient($outpatientId, $publicParams);
        if ($getCheckEndOutpatientRes->isFail())
        {
            return $getCheckEndOutpatientRes;
        }

        // 门诊关联的病历ID
        $caseId = $getCheckEndOutpatientRes->getData('caseId', 0);
        if (empty($caseId))
        {
            return self::Fail('门诊关联的病历不存在', 38000);
        }

        // 办理住院
        try
        {
            DB::beginTransaction();

            // 结束当前门诊断疗
            $endOutpatientParams = ['treatmentOutcome' => TreatmentOutcomeEnum::Inpatient->value];
            $endOutpatientRes    = OutpatientLogic::EndOutpatient($outpatientId, $endOutpatientParams, $publicParams);
            if ($endOutpatientRes->isFail())
            {
                DB::rollBack();

                return $endOutpatientRes;
            }

            // 生成住院信息
            $insertInpatientData = [
                'inpatient_code'           => generateBusinessCodeNumber(BusinessCodePrefixEnum::ZYSX),
                'member_id'                => $memberId,
                'pet_id'                   => $petId,
                'room_id'                  => $roomId,
                'bed_id'                   => $bedId,
                'org_id'                   => $hospitalOrgId,
                'brand_id'                 => $hospitalBrandId,
                'hospital_id'              => $hospitalId,
                'doctor_id'                => $doctorId,
                'days'                     => $days,
                'admission_date'           => getCurrentTimeWithMilliseconds(),
                'relation_doctor_id'       => $getOutpatientRes['doctor_id'],
                'relation_case_id'         => $caseId,
                'relation_outpatient_id'   => $outpatientId,
                'relation_registration_id' => $getOutpatientRes['registration_id'],
            ];
            $inpatientId         = InpatientModel::insertOne($insertInpatientData);

            // 使用床位
            $useBedRes = BedLogic::UseBed($roomId, $bedId, $inpatientId, $publicParams);
            if ($useBedRes->isFail())
            {
                DB::rollBack();

                return $useBedRes;
            }

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 办理住院异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('办理住院异常', 37005);
        }

        return self::Success();
    }

    /**
     * 获取有效住院信息
     *
     * @param int   $inpatientId
     * @param array $publicParams
     * @param bool  $withHospitalId
     * @param bool  $withDoctorId
     *
     * @return LogicResult
     */
    public static function GetValidInpatientById(int $inpatientId, array $publicParams, bool $withHospitalId = true, bool $withDoctorId = false): LogicResult
    {
        if (empty($inpatientId))
        {
            return self::Fail('获取有效住院信息，缺少住院ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效住院信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效门诊信息，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取有效门诊信息，缺少医生ID必选参数', 400);
        }

        // 获取有效住院信息
        $where = [
            'id' => $inpatientId,
        ];
        if (!empty($withHospitalId))
        {
            $where['hospital_id'] = $hospitalId;
        }
        if (!empty($withDoctorId))
        {
            $where['doctor_id'] = $doctorId;
        }

        $getInpatientRes = InpatientModel::getData(where: $where);
        if (empty($getInpatientRes))
        {
            return self::Fail('住院不存在', 37006);
        }

        return self::Success(current($getInpatientRes));
    }

    /**
     * 获取住院列表
     *
     * @param int $hospitalId
     * @param int $doctorId
     *
     * @return LogicResult
     */
    public static function GetInpatientList(int $hospitalId, int $doctorId): LogicResult
    {
        if (empty($hospitalId) || empty($doctorId))
        {
            return self::Fail('获取住院列表，缺少必选参数', 400);
        }

        // 获取住院列表
        $getInpatientListRes = InpatientModel::getData(where: [
                                                                  'hospital_id' => $hospitalId,
                                                                  'status'      => InpatientStatusEnum::Inpatient
                                                              ]);
        if (empty($getInpatientListRes))
        {
            return self::Success(['data' => []]);
        }

        // 获取住院关联的宠物
        $petIds    = array_unique(array_column($getInpatientListRes, 'pet_id'));
        $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        $getPetRes = $getPetRes->getData();

        // 获取当前医生正在进行的住院
        $getInInpatientRes = InpatientDoctorStatusModel::getDoctorInTreatmentInpatient($hospitalId, $doctorId);

        $returnInpatientList = [];
        foreach ($getInpatientListRes as $inpatientInfo)
        {
            // 关联宠物信息
            $curPetInfo = $getPetRes[$inpatientInfo['pet_id']]['petInfo'] ?? [];

            // 当前住院是否正在诊断
            if (!empty($getInInpatientRes[$inpatientInfo['id']]))
            {
                $inpatientStatus = InpatientTreatmentStatusEnum::InTreatment->value;
            }
            else
            {
                $inpatientStatus = InpatientTreatmentStatusEnum::Waiting->value;
            }

            $tmpInpatientInfo = [
                'inpatientCode'       => $inpatientInfo['inpatient_code'],
                'inpatientStatus'     => $inpatientStatus,
                'inpatientStatusName' => InpatientTreatmentStatusEnum::getDescription($inpatientStatus),
                'admissionDate'       => formatDisplayDateTime($inpatientInfo['admission_date'], 'Y-m-d'),
                'inpatientDays'       => $inpatientInfo['days'],
                'actualInpatientDate' => formatTimeInterval($inpatientInfo['admission_date'],
                                                            getCurrentTimeWithMilliseconds()),
                'petInfo'             => $curPetInfo
            ];

            $returnInpatientList[] = $tmpInpatientInfo;
        }

        return self::Success(['data' => $returnInpatientList]);
    }

    /**
     * 住院-开始诊断
     *
     * @param       $inpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function StartInpatient($inpatientId, array $publicParams): LogicResult
    {
        if (empty($inpatientId))
        {
            return self::Fail('开始诊断，缺少住院ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('开始诊断，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('开始诊断，缺少医院必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('开始诊断，缺少医生必选参数', 400);
        }

        // 获取住院信息
        $getValidInpatientRes = self::GetValidInpatientById($inpatientId, $publicParams);
        if ($getValidInpatientRes->isFail())
        {
            return $getValidInpatientRes;
        }

        // 转住院时关联的门诊信息
        $getValidInpatientRes   = $getValidInpatientRes->getData();
        $memberId               = $getValidInpatientRes['member_id'];
        $petId                  = $getValidInpatientRes['pet_id'];
        $relationRegistrationId = $getValidInpatientRes['relation_registration_id'];
        $relationCaseId         = $getValidInpatientRes['relation_case_id'];

        // 是否已出院
        if ($getValidInpatientRes['status'] == InpatientStatusEnum::DischargedInpatient->value)
        {
            return self::Fail('当前住院已出院，不可接诊', 37007);
        }

        // 当前住院关联的病历，可能不存在。比如：未开始诊断前
        $getCaseRes = CasesModel::getCaseByRelationId($hospitalId, CaseSourceTypeEnum::Inpatient->value, $inpatientId);
        if (!empty($getCaseRes) && $getCaseRes['finished'] == 1)
        {
            return self::Fail('病历已完结，不可接诊', 38003);
        }

        // 如果当前病历不存在，则获取在门诊中的宠物体况信息。在生成病历时，同时生成该病历的体况信息
        $getCasePetVitalSignRes = [];
        if (empty($getCaseRes))
        {
            $getCasePetVitalSignRes = PetVitalSignModel::getVitalSignByCaseIdOrRegistrationId($relationCaseId);
        }

        // 获取医生当前正在进行诊断的住院病历
        $getDoctorInTreatment = InpatientDoctorStatusModel::getDoctorInTreatmentInpatient($hospitalId, $doctorId);
        $getDoctorInTreatment = $getDoctorInTreatment->isNotEmpty() ? $getDoctorInTreatment->toArray() : [];
        if (!empty($getDoctorInTreatment[$inpatientId]))
        {
            return self::Fail('该住院已经开始了诊断，请刷新', 37005);
        }

        try
        {
            DB::beginTransaction();

            // 如果存在正在进行的住院病历，先关闭
            if (!empty($getDoctorInTreatment))
            {
                $inInpatientIds = array_column($getDoctorInTreatment, 'id');
                InpatientDoctorStatusModel::on()
                                          ->whereIn('id', $inInpatientIds)
                                          ->update(['status' => InpatientTreatmentStatusEnum::Stop->value]);
            }

            // 记录开始诊断当前住院病历
            InpatientDoctorStatusModel::on()
                                      ->updateOrCreate([
                                                           'hospital_id'  => $hospitalId,
                                                           'inpatient_id' => $inpatientId,
                                                           'doctor_id'    => $doctorId,
                                                       ],
                                                       [
                                                           'status' => InpatientTreatmentStatusEnum::InTreatment->value,
                                                       ]);

            // 记录住院就诊状态变更日志
            InpatientChangeStatusLogModel::insertOne([
                                                         'hospital_id'  => $hospitalId,
                                                         'inpatient_id' => $inpatientId,
                                                         'doctor_id'    => $doctorId,
                                                         'status'       => InpatientTreatmentStatusEnum::InTreatment->value,
                                                         'desc'         => InpatientTreatmentStatusEnum::getDescription(InpatientTreatmentStatusEnum::InTreatment->value),
                                                     ]);

            // 如果当前病历不存在，则生成一个病历
            if (empty($getCaseRes))
            {
                $insertPetCaseData = [
                    'case_code'          => generateBusinessCodeNumber(BusinessCodePrefixEnum::ZYBL),
                    'source_type'        => CaseSourceTypeEnum::Inpatient->value,
                    'source_relation_id' => $inpatientId,
                    'hospital_id'        => $hospitalId,
                    'brand_id'           => $hospitalBrandId,
                    'org_id'             => $hospitalOrgId,
                    'doctor_id'          => $doctorId,
                    'member_id'          => $memberId,
                    'pet_id'             => $petId,
                ];
                $caseId            = CasesModel::insertOne($insertPetCaseData);

                // 生成住院病历关联的体征信息，初始化信息来自门诊病历关联的体况信息
                if (!empty($getCasePetVitalSignRes))
                {
                    $insertPetVitalSignData = [
                        'member_id'           => $memberId,
                        'pet_id'              => $petId,
                        'registration_id'     => $relationRegistrationId,
                        'case_id'             => $caseId,
                        'weight'              => $getCasePetVitalSignRes['weight'],
                        'birthday'            => $getCasePetVitalSignRes['birthday'],
                        'temperature_type'    => $getCasePetVitalSignRes['temperature_type'],
                        'temperature'         => $getCasePetVitalSignRes['temperature'],
                        'physical'            => $getCasePetVitalSignRes['physical'],
                        'heartbeat'           => $getCasePetVitalSignRes['heartbeat'],
                        'respiration'         => $getCasePetVitalSignRes['respiration'],
                        'blood_pressure_type' => $getCasePetVitalSignRes['blood_pressure_type'],
                        'blood_pressure'      => $getCasePetVitalSignRes['blood_pressure'],
                    ];
                    PetVitalSignModel::insertOne($insertPetVitalSignData);
                }
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 开始诊断住院异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('开始诊断住院异常', 37005);
        }
    }

    /**
     * 住院-暂停诊断
     *
     * @param       $inpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function SuspendOutpatient($inpatientId, array $publicParams): LogicResult
    {
        if (empty($inpatientId))
        {
            return self::Fail('暂停诊断，缺少住院ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('暂停诊断，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('暂停诊断，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('暂停诊断，缺少医生必选参数', 400);
        }

        // 获取正在进行门诊信息
        $getInInpatientRes = InpatientDoctorStatusModel::getDoctorInTreatmentInpatient($hospitalId,
                                                                                       $doctorId,
                                                                                       [$inpatientId]);
        if (empty($getInInpatientRes[$inpatientId]))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 更新住院为暂停状态
            InpatientDoctorStatusModel::updateOne($getInInpatientRes[$inpatientId]['id'],
                                                  [
                                                      'status' => InpatientTreatmentStatusEnum::Stop->value,
                                                  ]);

            // 记录住院就诊状态变更日志
            InpatientChangeStatusLogModel::insertOne([
                                                         'hospital_id'  => $hospitalId,
                                                         'inpatient_id' => $inpatientId,
                                                         'doctor_id'    => $doctorId,
                                                         'status'       => InpatientTreatmentStatusEnum::Stop->value,
                                                         'desc'         => InpatientTreatmentStatusEnum::getDescription(OutpatientStatusEnum::Stop->value),
                                                     ]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 暂停诊断住院异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('暂停诊断住院异常', 37008);
        }
    }

    /**
     * 住院-结束诊断
     *
     * @param int   $inpatientId
     * @param array $endInpatientParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EndInpatient(int $inpatientId, array $endInpatientParams, array $publicParams): LogicResult
    {
        if (empty($inpatientId))
        {
            return self::Fail('结束住院，缺少住院ID必选参数', 400);
        }
        if (empty($endInpatientParams))
        {
            return self::Fail('结束住院，缺少结束住院结果必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('结束住院，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('结束住院，缺少医生ID必选参数', 400);
        }

        // 结束住院参数
        $treatmentOutcomeId  = $endInpatientParams['treatmentOutcome'];
        $transferHospitalUid = $endInpatientParams['transferHospitalUid'] ?? '';
        $transferRemark      = $endInpatientParams['transferRemark'] ?? '';
        if (empty($treatmentOutcomeId))
        {
            return self::Fail('诊断结果必选', 400);
        }

        // 验证是否可以结束诊断
        $getCheckRes = self::CheckEndInpatient($inpatientId, $publicParams);
        if ($getCheckRes->isFail())
        {
            return $getCheckRes;
        }

        // 诊断结果是否存在
        $caseId                 = $getCheckRes->getData('caseId', 0);
        $getTreatmentOutcomeRes = CaseLogic::GetEndCaseTreatmentOutcome($caseId, $publicParams);
        if ($getTreatmentOutcomeRes->isFail())
        {
            return $getTreatmentOutcomeRes;
        }

        $getTreatmentOutcomeRes = $getTreatmentOutcomeRes->getData('data', []);
        if (!in_array($treatmentOutcomeId, array_column($getTreatmentOutcomeRes, 'id')))
        {
            return self::Fail('选择的诊断结果不存在', 36006);
        }

        // 结束诊断
        try
        {
            DB::beginTransaction();

            // 更新住院为结束状态
            InpatientModel::updateOne($inpatientId,
                                      [
                                          'outcome_id'     => $treatmentOutcomeId,
                                          'status'         => InpatientStatusEnum::DischargedInpatient->value,
                                          'discharge_date' => getCurrentTimeWithMilliseconds(),
                                      ]);

            // 更新病历为结束状态
            CasesModel::updateOne($caseId,
                                  [
                                      'finished'      => 1,
                                      'finished_time' => getCurrentTimeWithMilliseconds(),
                                      'outcome_id'    => $treatmentOutcomeId
                                  ]);

            // 更新医生操作住院记录状态为结束
            InpatientDoctorStatusModel::on()
                                      ->where(['inpatient_id' => $inpatientId, 'doctor_id' => $doctorId])
                                      ->update(['status' => InpatientTreatmentStatusEnum::Completed->value]);

            // 释放床位
            BedLogic::ReleaseBed($inpatientId, $publicParams);

            // 结束诊断，特殊业务单独处理（内部转院，需生成转诊单）。如果不存在则不会执行
            $callbackMethod = TreatmentOutcomeEnum::TREATMENT_OUTCOME_CALLBACK[$treatmentOutcomeId] ?? [];
            if (!empty($callbackMethod))
            {
                $transferParams = [
                    'caseId'              => $caseId,
                    'sourceType'          => CaseSourceTypeEnum::Inpatient->value,
                    'sourceRelationId'    => $inpatientId,
                    'transferHospitalUid' => $transferHospitalUid,
                    'transferRemark'      => $transferRemark,
                ];
                $callResult     = call_user_func($callbackMethod, $transferParams, $publicParams);
                if ($callResult->isFail())
                {
                    DB::rollBack();

                    return $callResult;
                }
            }

            // 记录住院就诊状态变更日志
            InpatientChangeStatusLogModel::insertOne([
                                                         'hospital_id'  => $hospitalId,
                                                         'inpatient_id' => $inpatientId,
                                                         'doctor_id'    => $doctorId,
                                                         'status'       => InpatientTreatmentStatusEnum::Completed->value,
                                                         'desc'         => InpatientTreatmentStatusEnum::getDescription(OutpatientStatusEnum::Completed->value),
                                                     ]);

            DB::commit();

            // TODO 异步生成病历下处方快照
            DataSnapshotLogic::createCaseSnapshot($caseId, $publicParams);

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 结束住院异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('结束住院异常', 37010);
        }
    }

    /**
     * 检查是否可以结束诊断
     *
     * @param int   $inpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function CheckEndInpatient(int $inpatientId, array $publicParams): LogicResult
    {
        if (empty($inpatientId))
        {
            return self::Fail('验证是否可以结束住院，缺少住院ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证是否可以结束住院，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId) || empty($doctorId))
        {
            return self::Fail('验证是否可以结束住院，缺少公共必选参数', 400);
        }

        // 获取住院信息
        $getValidInpatientRes = self::GetValidInpatientById($inpatientId, $publicParams);
        if ($getValidInpatientRes->isFail())
        {
            return $getValidInpatientRes;
        }

        $getValidInpatientRes = $getValidInpatientRes->getData();
        if ($getValidInpatientRes['status'] != InpatientStatusEnum::Inpatient->value)
        {
            return self::Fail('住院状态非住院中，不可操作', 37009);
        }

        // 获取当前医生是否存在对当前住院的诊治
        $getDoctorInTreatmentRes = self::GetDoctorInTreatmentInpatient($hospitalId, $doctorId, $inpatientId);
        if ($getDoctorInTreatmentRes->isFail())
        {
            return self::Fail('当前医生不存在诊断此住院记录，不可操作', 37011);
        }
        if (empty($getDoctorInTreatmentRes->getData()))
        {
            return self::Fail('当前医生不存在诊断此住院记录，不可操作', 37011);
        }

        // 验证住院关联的病历是否可以结束
        return CaseLogic::CheckEndAbleBySourceType($hospitalId,
                                                   CaseSourceTypeEnum::Inpatient->value,
                                                   $inpatientId,
                                                   $doctorId);
    }

    /**
     * 获取一个医生正在诊断的住院，返回住院对应的病历编码，用户展示工作台病历信息
     *
     * @param int $hospitalId
     * @param int $doctorId
     * @param int $inpatientId
     *
     * @return LogicResult
     */
    public static function GetDoctorInTreatmentInpatient(int $hospitalId, int $doctorId, int $inpatientId = 0): LogicResult
    {
        if (empty($hospitalId) || empty($doctorId))
        {
            return self::Fail('获取诊断中的住院，缺少必选参数', 400);
        }

        // 获取医生正在进行的住院记录
        $withInpatientIds = [];
        if ($inpatientId > 0)
        {
            $withInpatientIds = [$inpatientId];
        }

        $getInInpatientRes = InpatientDoctorStatusModel::getDoctorInTreatmentInpatient($hospitalId,
                                                                                       $doctorId,
                                                                                       $withInpatientIds);
        if ($getInInpatientRes->isEmpty())
        {
            return self::Success();
        }

        $getInInpatientRes = $getInInpatientRes->last();
        $inpatientId       = $getInInpatientRes['inpatient_id'];

        // 获取住院信息
        $publicParams         = ['_hospitalId' => $hospitalId, '_userId' => $doctorId];
        $getValidInpatientRes = self::GetValidInpatientById($inpatientId, $publicParams);
        if ($getValidInpatientRes->isFail())
        {
            return $getValidInpatientRes;
        }

        // 住院关联的门诊挂号ID
        $inpatientCode  = $getValidInpatientRes->getData('inpatient_code', '');
        $registrationId = $getValidInpatientRes->getData('relation_registration_id', 0);

        // 获取门诊关联的挂号
        $getRegistrationRes = RegistrationsModel::getOne($registrationId);
        if (empty($getRegistrationRes))
        {
            return self::Fail('正在进行的门诊关联挂号不存在', 35011);
        }

        // 获取住院关联的病历编码
        $getCaseRes = CasesModel::getCaseByRelationId($hospitalId, CaseSourceTypeEnum::Inpatient->value, $inpatientId);
        if (empty($getCaseRes))
        {
            return self::Fail('正在进行的住院关联病例不存在', 38000);
        }

        // 获取病历关联的宠物信息
        $petId     = $getCaseRes['pet_id'];
        $getPetRes = MemberPetsModel::getOne($petId);
        if (empty($getPetRes))
        {
            return self::Fail('正在进行的住院关联宠物不存在', 32000);
        }

        return self::Success([
                                 'caseCode'         => $getCaseRes['case_code'],
                                 'inpatientCode'    => $inpatientCode,
                                 'petUid'           => $getPetRes['uid'],
                                 'registrationCode' => $getRegistrationRes['registration_code']
                             ]);
    }
}
