<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\AddressCityModel;
use App\Models\HisBrandModel;
use App\Models\HisOrgModel;
use App\Models\HospitalModel;
use Arr;

class HospitalLogic extends Logic
{

    /**
     * 获取医院的基本信息
     *
     * @param string $uid
     * @param int    $hospitalId
     * @param bool   $withId
     * @param bool   $onWritePdo
     *
     * @return LogicResult
     */
    public static function GetHospitalBaseInfo(string $uid = '', int $hospitalId = 0, bool $withId = false, bool $onWritePdo = false): LogicResult
    {
        if ($uid == '' && $hospitalId <= 0)
        {
            return self::Fail('获取医院基本信息，缺少必选参数', 400);
        }

        if ($uid != '')
        {
            $hospital = HospitalModel::getOneByUid($uid, $onWritePdo);
        }
        else
        {
            $hospital = HospitalModel::getOne($hospitalId, $onWritePdo);
        }

        if (empty($hospital) || $hospital->delete_status == 1)
        {
            return self::Fail('医院不存在', 20100);
        }

        if ($hospital->status != 1)
        {
            return self::Fail('医院已禁用', 20102);
        }

        // 关联品牌是否存在
        $getHospitalBrandRes = HisBrandModel::getOne($hospital->brand_id, $onWritePdo);
        if (empty($getHospitalBrandRes) || $getHospitalBrandRes->status == 0)
        {
            return self::Fail('医院品牌已经禁用', 20100);
        }

        // 关联组织是否存在
        $getHospitalOrgRes = HisOrgModel::getOne($hospital->org_id, $onWritePdo);
        if (empty($getHospitalOrgRes) || $getHospitalOrgRes->status == 0)
        {
            return self::Fail('医院组织已经禁用', 20100);
        }

        $hospitalInfo = [
            'uid'         => $hospital->uid,
            'orgUid'      => $getHospitalOrgRes->uid,
            'name'        => $hospital->name,
            'aliasName'   => $hospital->alias_name,
            'logo'        => $hospital->logo,//TODO:处理图片路径
            'addressInfo' => [
                'provinceId' => $hospital->province_id,
                'cityId'     => $hospital->city_id,
                'areaId'     => $hospital->area_id,
                'townId'     => $hospital->town_id,
                'detail'     => $hospital->address,
            ],
        ];

        if ($withId)
        {
            $hospitalInfo['id']      = $hospital->id;
            $hospitalInfo['orgId']   = $hospital->org_id;
            $hospitalInfo['brandId'] = $hospital->brand_id;
        }

        return self::Success($hospitalInfo);
    }

    /**
     * 获取多个医院的基本信息
     *
     * @param array         $hospitalIds
     * @param bool          $withId
     * @param callable|null $recommend
     *
     * @return LogicResult
     */
    public static function GetHospitalsBaseInfo(array $hospitalIds, bool $withId = false, callable|null $recommend = null): LogicResult
    {
        if (empty($hospitalIds))
        {
            return self::Success(['hospitals' => []]);
        }

        $hospitals = HospitalModel::getManyByIds($hospitalIds);

        $hospitalsInfo = [];
        foreach ($hospitals as $hospital)
        {
            $temp = [
                'uid'       => $hospital->uid,
                'name'      => $hospital->name,
                'aliasName' => $hospital->alias_name,
                'logo'      => $hospital->logo,//TODO:处理图片路径
            ];

            if ($withId)
            {
                $temp['id'] = $hospital->id;
            }

            if (is_callable($recommend))
            {
                $temp['recommend'] = $recommend($hospital);
            }

            $hospitalsInfo[] = $temp;
        }

        return self::Success(['hospitals' => $hospitalsInfo]);
    }

    /**
     * 获取医院可操作的品牌
     *
     * @param int    $hospitalId
     * @param string $hospitalUid
     *
     * @return LogicResult
     */
    public static function GetAllowedBrandIdsByHospitalId(int $hospitalId = 0, string $hospitalUid = ''): LogicResult
    {
        if (empty($hospitalId) && empty($hospitalUid))
        {
            return self::Fail('hospitalId、hospitalUid，缺少必选参数', 400);
        }

        // 获取医院信息
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo($hospitalUid, $hospitalId, true, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        // 获取数据
        $hospitalOrgId   = $getHospitalRes->getData('orgId', 0);
        $hospitalBrandId = $getHospitalRes->getData('brandId', 0);

        // 如果当前医院品牌不互通，那么只返回当前品牌
        $brandIsChain = HisBrandModel::getBrandIsShare($hospitalBrandId);
        if (empty($brandIsChain))
        {
            return self::Success([
                                     'hospitalId'      => $hospitalId,
                                     'hospitalBrandId' => $hospitalBrandId,
                                     'allowedBrandIds' => [$hospitalBrandId]
                                 ]);
        }

        // 获取医院组织下互通的品牌，如果为空，那么默认只有当前医院所关联的品牌
        $getShareBrandRes = HisBrandModel::getShareBrandByOrgId($hospitalOrgId, isShare: 1);
        if ($getShareBrandRes->isEmpty())
        {
            return self::Success([
                                     'hospitalId'      => $hospitalId,
                                     'hospitalBrandId' => $hospitalBrandId,
                                     'allowedBrandIds' => [$hospitalBrandId]
                                 ]);
        }

        $brandIds = $getShareBrandRes->pluck('id')
                                     ->toArray();

        return self::Success([
                                 'hospitalId'      => $hospitalId,
                                 'hospitalBrandId' => $hospitalBrandId,
                                 'allowedBrandIds' => $brandIds
                             ]);
    }

    /**
     * 获取可转院的医院列表
     *
     * @param array $publicParams
     * @param int   $transferHospitalId 转入医院ID，获取指定的
     *
     * @return LogicResult
     */
    public static function getTransferHospitalList(array $publicParams, int $transferHospitalId = 0): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取品牌下医院，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('获取品牌下医院，缺少必选参数', 400);
        }

        // 如果当前医院品牌互通，那么获取当前组织下所有互通的品牌。否则只获取当前品牌下所有医院
        $getAllowedBrandIdsRes = self::GetAllowedBrandIdsByHospitalId($hospitalId);
        $allowedBrandIds       = $getAllowedBrandIdsRes->getData('allowedBrandIds', []);
        if (empty($allowedBrandIds))
        {
            return self::Success();
        }

        // 获取医院数据
        $getHospitalsWhere = [['id', '!=', $hospitalId], ['status', '=', 1], ['delete_status', '=', 0]];
        if (!empty($transferHospitalId))
        {
            $getHospitalsWhere[] = ['id', '=', $transferHospitalId];
        }

        $getHospitalsRes = HospitalModel::getData(where  : $getHospitalsWhere,
                                                  whereIn: ['brand_id' => $allowedBrandIds]);
        if (empty($getHospitalsRes))
        {
            return self::Success();
        }

        // 医院品牌
        $allBrandIds = array_unique(array_column($getHospitalsRes, 'brand_id'));
        $getBrandRes = HisBrandModel::getData(where: ['status' => 1], whereIn: ['id' => $allBrandIds], keyBy: 'id');
        if (empty($getBrandRes))
        {
            return self::Success();
        }

        // 医院城市
        $allCityIds = array_unique(array_column($getHospitalsRes, 'city_id'));
        $getCityRes = AddressCityModel::getData(whereIn: ['id' => $allCityIds], keyBy: 'id');
        if (empty($getCityRes))
        {
            return self::Success();
        }

        $returnData = [];
        foreach ($getHospitalsRes as $curHospital)
        {
            // 当前医院品牌
            $curBrandId = $curHospital['brand_id'];
            if (empty($getBrandRes[$curBrandId]))
            {
                continue;
            }

            // 当前医院城市
            $curCityId = $curHospital['city_id'];
            if (empty($getCityRes[$curCityId]))
            {
                continue;
            }

            // 初始化品牌节点
            if (empty($returnData[$curBrandId]))
            {
                $returnData[$curBrandId] = [
                    'uid'      => $getBrandRes[$curBrandId]['id'],
                    'name'     => $getBrandRes[$curBrandId]['name'],
                    'children' => [],
                ];
            }

            // 初始化城市节点
            if (empty($returnData[$curBrandId]['children'][$curCityId]))
            {
                $returnData[$curBrandId]['children'][$curCityId] = [
                    'uid'      => $getCityRes[$curCityId]['id'],
                    'name'     => $getCityRes[$curCityId]['name'],
                    'children' => [],
                ];
            }

            // 添加医院
            $returnData[$curBrandId]['children'][$curCityId]['children'][] = [
                'uid'  => $curHospital['uid'],
                'name' => $curHospital['name'],
            ];
        }

        // 重建索引
        foreach ($returnData as &$brandGroup)
        {
            $brandGroup['children'] = array_values($brandGroup['children']);
            foreach ($brandGroup['children'] as &$cityGroup)
            {
                $cityGroup['children'] = array_values($cityGroup['children']);
            }
        }

        return self::Success(array_values($returnData));
    }
}
