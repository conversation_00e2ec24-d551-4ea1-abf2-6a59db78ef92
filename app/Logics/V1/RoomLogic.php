<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RoomsModel;

class RoomLogic extends Logic
{
    /**
     * 获取病房信息
     * @return LogicResult
     */
    public static function GetRoomsOptions(int $hospitalId): LogicResult
    {
        if (empty($hospitalId))
        {
            return self::Fail('获取医院住院部，缺少医院ID必选参数', 400);
        }

        // 获取病房信息
        $getRoomsRes = RoomsModel::getData(where: ['hospital_id' => $hospitalId, 'status' => 1]);
        if (empty($getRoomsRes))
        {
            return self::Success(['roomsOptions' => []]);
        }

        $returnRoomsInfo = [];
        foreach ($getRoomsRes as $curInfo)
        {
            $returnRoomsInfo[] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['roomsOptions' => $returnRoomsInfo]);
    }
}
