<?php

namespace App\Logics\V1;

use Illuminate\Support\Arr;
use App\Facades\SearchFacade;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PageEnum;

class RetailLogic extends Logic
{
    /**
     * 搜索洗美服务商品
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetSearchItems(array $params, array $publicParams): LogicResult
    {
        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        // 业务参数
        $keyword = trim(Arr::get($params, 'keyword', ''));

        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('orgId、brandId、hospitalId，缺少必选参数', 400);
        }

        if (empty($keyword))
        {
            return self::Fail('keywords，缺少必选参数', 400);
        }

        // 搜索关键词
        $keyword = trimWhitespace($keyword);

        // 搜索商品
        $getSearchItemsRes = SearchFacade::searchRetailItems(
            keywords: $keyword,
            orgId:    $orgId
        );
        if (empty($getSearchItemsRes))
        {
            return self::Success(['data' => []]);
        }

        // 过滤搜索商品
        $getFilterSearchItemsRes = self::FilterSearchItems($getSearchItemsRes);
        if ($getFilterSearchItemsRes->isFail())
        {
            return $getFilterSearchItemsRes;
        }

        // 构建商品信息结构
        $searchItems      = $getFilterSearchItemsRes->getData();
        $getFormatItemRes = SearchItemLogic::FormatItemInfoStructure($searchItems, $publicParams, true);
        if ($getFormatItemRes->isFail())
        {
            return $getFormatItemRes;
        }

        //TODO:使用库存

        // 标记商品在不满足的情况下禁用添加到服务
        $getFormatItemRes = $getFormatItemRes->getData();
        foreach ($getFormatItemRes as &$itemInfo)
        {
            // 是否禁用添加，true:禁用，不可添加 false:不禁用，可添加
            $itemInfo['disabled'] = false;

            // 价格无效
            if ($itemInfo['packPrice'] <= 0 || $itemInfo['bulkPrice'] <= 0)
            {
                $itemInfo['disabled'] = true;
            }
        }

        return self::Success(['data' => $getFormatItemRes]);
    }

    /**
     * 过滤搜索商品
     *
     * @param array $searchItems
     *
     * @return LogicResult
     */
    private static function FilterSearchItems(array $searchItems): LogicResult
    {
        if (empty($searchItems))
        {
            return self::Success();
        }

        $returnSearchItems = [];
        foreach ($searchItems as $itemInfo)
        {
            // 搜索排名分值
            $curItemRankingScore = $itemInfo['ranking_score'] ?? 0;
            if ($curItemRankingScore < 0.5)
            {
                continue;
            }

            $returnSearchItems[] = $itemInfo;
        }

        return self::Success($returnSearchItems);
    }
}
