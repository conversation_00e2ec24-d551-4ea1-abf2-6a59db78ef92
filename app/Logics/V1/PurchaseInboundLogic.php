<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Enums\PageEnum;
use App\Enums\StockAddTypeEnum;
use App\Enums\PurchaseOrderStatusEnum;
use App\Enums\PurchaseOrderInboundStatusEnum;
use App\Enums\PurchaseOrderReceivedStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\PurchaseReceivedModel;
use App\Models\PurchaseOrderItemModel;
use App\Models\StockItemShelfAddModel;
use App\Models\PurchaseReceivedDetailModel;
use App\Models\PurchaseReceivedVoucherModel;

class PurchaseInboundLogic extends Logic
{
    /**
     * 获取到货单列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetInboundLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取到货入库列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取到货入库列表，缺少公共必选参数', 400);
        }

        // 业务参数
        $iPage     = intval(Arr::get($searchParams, 'page', 0)) ?? PageEnum::DefaultPageIndex->value;
        $iPageSize = intval(Arr::get($searchParams, 'count', 0)) ?? PageEnum::DefaultPageSize->value;

        // 获取到货单列表
        $searchParams['hospitalId'] = $hospitalId;
        $getReceivedListRes         = PurchaseReceivedModel::getReceivedListData($searchParams, $iPage, $iPageSize);
        if (empty($getReceivedListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount      = $getReceivedListRes['total'] ?? 0;
        $receivedListRes = $getReceivedListRes['data'] ?? [];
        if (empty($totalCount) || empty($receivedListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 格式化到货单列表
        $getFormatReceivedRes = self::FormatReceivedStructure($receivedListRes);
        if ($getFormatReceivedRes->isFail())
        {
            return $getFormatReceivedRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatReceivedRes->getData()]);
    }

    /**
     * 到货入库
     *
     * @param int   $receivedId
     * @param int   $receivedDetailId
     * @param array $addInboundParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function StockItemInbound(int $receivedId, int $receivedDetailId, array $addInboundParams, array $publicParams): LogicResult
    {
        if (empty($receivedId) || empty($receivedDetailId))
        {
            return self::Fail('商品入库，缺少必选参数', 400);
        }
        if (empty($addInboundParams))
        {
            return self::Fail('商品入库，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('商品入库，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('商品入库，缺少公共必选参数', 400);
        }

        // 业务参数
        $itemUid      = trimWhitespace(Arr::get($addInboundParams, 'itemUid', ''));
        $itemBarcode  = trimWhitespace(Arr::get($addInboundParams, 'itemBarcode', ''));
        $shelfCode    = trimWhitespace(Arr::get($addInboundParams, 'shelfCode', ''));
        $packQuantity = intval(Arr::get($addInboundParams, 'packQuantity', 0));
        $bulkQuantity = intval(Arr::get($addInboundParams, 'bulkQuantity', 0));
        $produceDate  = Arr::get($addInboundParams, 'productionDate', '');
        $expireDate   = Arr::get($addInboundParams, 'expiredDate', '');
        if (empty($itemUid) || empty($itemBarcode) || empty($shelfCode))
        {
            return self::Fail('商品入库，入库商品缺少必选参数', 400);
        }
        if ($packQuantity <= 0 && $bulkQuantity <= 0)
        {
            return self::Fail('商品入库，入库数量不能同时为0', 400);
        }

        // 获取到货单信息
        $getReceivedRes = PurchaseReceivedLogic::GetValidPurchaseReceived($receivedId, $publicParams);
        if ($getReceivedRes->isFail())
        {
            return $getReceivedRes;
        }

        // 是否可入库
        $getReceivedRes = $getReceivedRes->getData();
        if ($getReceivedRes['inbound_status'] == PurchaseOrderInboundStatusEnum::FullyInbound->value)
        {
            return self::Fail('到货单已全部库，无法再次入库', 42022);
        }

        // 获取入库明细信息
        $getReceivedDetailRes = PurchaseReceivedDetailModel::getData(where: ['id' => $receivedDetailId, 'status' => 1]);
        $getReceivedDetailRes = $getReceivedDetailRes ? current($getReceivedDetailRes) : [];
        if (empty($getReceivedDetailRes))
        {
            return self::Fail('入库商品明细不存在', 42023);
        }
        if ($getReceivedDetailRes['is_inbound_complete'] == 1)
        {
            return self::Fail('入库商品已全部入库，无法再次入库', 42024);
        }
        if ($getReceivedDetailRes['item_barcode'] != $itemBarcode)
        {
            return self::Fail('入库商品条形码与到货单商品条形码不一致', 42025);
        }

        // 获取到货单关联的采购单
        $getPurchaseOrderRes = PurchaseLogic::GetValidPurchaseOrder($getReceivedRes['purchase_order_id'], $publicParams);
        if ($getPurchaseOrderRes->isFail())
        {
            return $getPurchaseOrderRes;
        }

        // 采购单是否可以入库
        $getPurchaseOrderRes = $getPurchaseOrderRes->getData();
        if ($getPurchaseOrderRes['status'] != PurchaseOrderStatusEnum::Approved->value)
        {
            return self::Fail('采购单状态非审核通过，无法入库', 42028);
        }
        if ($getPurchaseOrderRes['received_status'] == PurchaseOrderReceivedStatusEnum::NotReceived->value)
        {
            return self::Fail('采购单未签收，无法入库', 42029);
        }
        if ($getPurchaseOrderRes['inbound_status'] == PurchaseOrderInboundStatusEnum::FullyInbound->value)
        {
            return self::Fail('采购单已全部入库，无法再次入库', 42030);
        }

        // 获取入库商品关联的采购商品
        $getPurchaseOrderItemRes = PurchaseOrderItemModel::getData(where: ['id' => $getReceivedDetailRes['purchase_order_item_id'], 'status' => 1]);
        $getPurchaseOrderItemRes = $getPurchaseOrderItemRes ? current($getPurchaseOrderItemRes) : [];
        if (empty($getPurchaseOrderItemRes))
        {
            return self::Fail('入库商品关联的采购商品不存在', 42011);
        }
        if ($getPurchaseOrderItemRes['item_barcode'] != $itemBarcode)
        {
            return self::Fail('入库商品条形码与采购商品条形码不一致', 42011);
        }

        // 获取货位信息
        $getShelfSlotRes = WarehouseLogic::GetShelfSlotInfo($shelfCode, $publicParams);
        if ($getShelfSlotRes->isFail())
        {
            return $getShelfSlotRes;
        }

        // 商品整散比
        $itemBulkRatio = $getPurchaseOrderItemRes['item_bulk_ratio'] ?? 1;

        // 计算本次到货，剩余可入库数量（到货数量 - 已入库数量）
        $getRemainQuantityRes = StockQuantityConversionHelper::getRemainPackAndBulkQuantity($getReceivedDetailRes['pack_quantity'],
                                                                                            $getReceivedDetailRes['bulk_quantity'],
                                                                                            $getReceivedDetailRes['inbound_pack_quantity'],
                                                                                            $getReceivedDetailRes['inbound_bulk_quantity'],
                                                                                            $itemBulkRatio);

        $remainPackQuantity = $getRemainQuantityRes['remainPackQuantity'];
        $remainBulkQuantity = $getRemainQuantityRes['remainBulkQuantity'];

        // 验证入库数量不超过剩余可入库数量（支持整散比换算）
        $getCheckSufficientRes = StockQuantityConversionHelper::checkQuantityIsSufficient($packQuantity, $bulkQuantity, $remainPackQuantity, $remainBulkQuantity, $itemBulkRatio);
        if (empty($getCheckSufficientRes))
        {
            return self::Fail("入库数量超出可入库数量。(剩余可入库整装：{$remainPackQuantity}，剩余可入库散装：$remainBulkQuantity)", 42026);
        }

        // 获取本次到货累计入库数量
        $getStockItemShelfAddQuantityRes = StockItemShelfAddModel::getStockItemShelfAddQuantity($hospitalId,
                                                                                                [
                                                                                                    'type'             => StockAddTypeEnum::Purchase->value,
                                                                                                    'relationCode'     => $getReceivedRes['received_code'],
                                                                                                    'relationId'       => $receivedId,
                                                                                                    'relationDetailId' => $receivedDetailId,
                                                                                                    'itemId'           => $getPurchaseOrderItemRes['item_id'],
                                                                                                    'itemBarcode'      => $itemBarcode,
                                                                                                ]);
        if (empty($getStockItemShelfAddQuantityRes))
        {
            return self::Fail('获取商品已入库数量失败', 42026);
        }

        // 计算本次入库后的累计入库数量（累计入库数量 + 本次入库数量）
        $totalInboundPackQuantity = numberAdd([$getStockItemShelfAddQuantityRes['packQuantity'], $packQuantity]);
        $totalInboundBulkQuantity = numberAdd([$getStockItemShelfAddQuantityRes['bulkQuantity'], $bulkQuantity]);

        // 验证累计入库数量不超过到货数量（支持整散比换算）
        $getCheckSufficientRes = StockQuantityConversionHelper::checkQuantityIsSufficient($totalInboundPackQuantity,
                                                                                          $totalInboundBulkQuantity,
                                                                                          $getReceivedDetailRes['pack_quantity'],
                                                                                          $getReceivedDetailRes['bulk_quantity'],
                                                                                          $itemBulkRatio);
        if (empty($getCheckSufficientRes))
        {
            return self::Fail("入库记录数量超出到货总数量", 42026);
        }

        try
        {
            DB::beginTransaction();

            $addStockParams = [
                'itemId'           => $getPurchaseOrderItemRes['item_id'],
                'itemBarcode'      => $itemBarcode,
                'addType'          => StockAddTypeEnum::Purchase->value,
                'addSubType'       => 0,
                'relationCode'     => $getReceivedRes['received_code'],
                'relationId'       => $receivedId,
                'relationDetailId' => $receivedDetailId,
                'shelfCode'        => $shelfCode,
                'packQuantity'     => $packQuantity,
                'bulkQuantity'     => $bulkQuantity,
                'produceDate'      => $produceDate,
                'expiredDate'      => $expireDate,
                'remark'           => '到货入库',
            ];

            // 入库整装，计算入库整装价格
            $inboundTotalPrice = 0;
            if ($packQuantity > 0)
            {
                $addStockParams['packPrice'] = numberMul([$getPurchaseOrderItemRes['item_bulk_ratio'], $getPurchaseOrderItemRes['bulk_avg_price']], 4);
                $inboundTotalPrice           = numberAdd([$inboundTotalPrice, numberMul([$packQuantity, $addStockParams['packPrice']], 4)], 4);
            }

            // 入库散装，计算入库散装价格
            if ($bulkQuantity > 0)
            {
                $addStockParams['bulkPrice'] = $getPurchaseOrderItemRes['bulk_avg_price'];
                $inboundTotalPrice           = numberAdd([$inboundTotalPrice, numberMul([$bulkQuantity, $addStockParams['bulkPrice']], 4)], 4);
            }

            // 增加库存
            $getAddStockRes = StockItemShelfLogic::AddStockShelfQuantity([$addStockParams], $publicParams);
            if ($getAddStockRes->isFail())
            {
                DB::rollBack();

                return $getAddStockRes;
            }

            // 更新整装到货入库数量
            if ($packQuantity > 0)
            {
                $curAffectedRows = PurchaseReceivedDetailModel::on()
                                                              ->where(['id' => $receivedDetailId])
                                                              ->increment('inbound_pack_quantity', $packQuantity);
                if ($curAffectedRows <= 0)
                {
                    DB::rollBack();

                    return self::Fail('入库商品【' . $getReceivedDetailRes['item_barcode'] . '】，更新整超出可入库限制', 42031);
                }
            }

            // 更新散装到货入库数量
            if ($bulkQuantity > 0)
            {
                $curAffectedRows = PurchaseReceivedDetailModel::on()
                                                              ->where(['id' => $receivedDetailId])
                                                              ->increment('inbound_bulk_quantity', $bulkQuantity);
                if ($curAffectedRows <= 0)
                {
                    DB::rollBack();

                    return self::Fail('入库商品【' . $getReceivedDetailRes['item_barcode'] . '】，更新散超出可入库限制', 42031);
                }
            }

            // 判断是否入库完成（使用整散比换算判断）
            $afterInboundPackQuantity = numberAdd([$getReceivedDetailRes['inbound_pack_quantity'], $packQuantity]);
            $afterInboundBulkQuantity = numberAdd([$getReceivedDetailRes['inbound_bulk_quantity'], $bulkQuantity]);

            // 检查入库后的累计数量是否达到到货数量，如果到货全部入库完成更新入库状态（支持整散比换算）
            $afterInboundBulkTotal = StockQuantityConversionHelper::convertToTotalBulkQuantity($afterInboundPackQuantity, $afterInboundBulkQuantity, $itemBulkRatio);
            $receivedBulkTotal     = StockQuantityConversionHelper::convertToTotalBulkQuantity($getReceivedDetailRes['pack_quantity'],
                                                                                               $getReceivedDetailRes['bulk_quantity'],
                                                                                               $itemBulkRatio);
            if ($afterInboundBulkTotal >= $receivedBulkTotal)
            {
                $curAffectedRows = PurchaseReceivedDetailModel::updateOne($receivedDetailId, ['is_inbound_complete' => 1]);
                if ($curAffectedRows <= 0)
                {
                    DB::rollBack();

                    return self::Fail('入库商品【' . $getReceivedDetailRes['item_barcode'] . '】，更新入库完成失败', 42031);
                }
            }

            // 获取本到货单是否全部签收
            $notInboundDetailCount = PurchaseReceivedDetailModel::getTotalNumber(where: ['received_id' => $receivedId, 'is_inbound_complete' => 0, 'status' => 1]);
            $upInboundStatus       = $notInboundDetailCount > 0 ? PurchaseOrderInboundStatusEnum::PartiallyInbound->value : PurchaseOrderInboundStatusEnum::FullyInbound->value;
            $upInboundCompleteAt   = $upInboundStatus == PurchaseOrderInboundStatusEnum::FullyInbound->value ? getCurrentTimeWithMilliseconds() : null;

            // 更新到货单入库状态、金额
            $curAffectedRows = PurchaseReceivedModel::updateOne($receivedId,
                                                                [
                                                                    'inbound_status'      => $upInboundStatus,
                                                                    'inbound_price'       => DB::raw('inbound_price + ' . $inboundTotalPrice),
                                                                    'inbound_complete_at' => $upInboundCompleteAt
                                                                ]);
            if ($curAffectedRows <= 0)
            {
                DB::rollBack();

                return self::Fail('更新到货单入库状态失败', 42031);
            }

            // 更新采购单入库
            $getUpPurchaseRes = PurchaseLogic::UpdatePurchaseOrderInbound($getPurchaseOrderRes['id'], $inboundTotalPrice, $publicParams);
            if ($getUpPurchaseRes->isFail())
            {
                DB::rollBack();

                return $getUpPurchaseRes;
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 到货入库异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('到货入库异常', 42031);
        }
    }

    /**
     * 格式化到货单列表
     *
     * @param array $receivedList
     *
     * @return LogicResult
     */
    private static function FormatReceivedStructure(array $receivedList): LogicResult
    {
        if (empty($receivedList))
        {
            return self::Success();
        }

        // 获取到货凭证
        $receivedIds           = array_filter(array_column($receivedList, 'id'));
        $getReceivedVoucherRes = [];
        if (!empty($receivedIds))
        {
            $getReceivedVoucherRes = PurchaseReceivedVoucherModel::getData(where: ['status' => 1], whereIn: ['received_id' => $receivedIds]);
        }

        $returnReceivedList = [];
        foreach ($receivedList as $curReceived)
        {
            // 当前到货凭证
            $curReceivedVoucher = [];
            foreach ($getReceivedVoucherRes as $voucherKey => $voucherInfo)
            {
                if ($voucherInfo['received_id'] != $curReceived['id'])
                {
                    continue;
                }

                $curReceivedVoucher[] = [
                    'id'   => $voucherInfo['id'],
                    'name' => $voucherInfo['image_name'],
                    'url'  => realPicturePath($voucherInfo['image_url']),
                ];
                unset($getReceivedVoucherRes[$voucherKey]);
            }

            $tmpPurchaseOrder = [
                'receivedCode'    => $curReceived['received_code'],
                'purchaseCode'    => $curReceived['purchase_code'],
                'supplierName'    => PurchaseLogic::FormatPurchaseSupplierName($curReceived)
                                                  ->getData('supplierName', ''),
                'receivedInPrice' => $curReceived['received_price'] <= 0 ? '' : formatDisplayNumber($curReceived['received_price'], 4),
                'inboundStatus'   => [
                    'id'   => $curReceived['inbound_status'],
                    'name' => PurchaseOrderInboundStatusEnum::getDescription($curReceived['inbound_status']),
                ],
                'createUser'      => [
                    'uid'  => $curReceived['user_uid'],
                    'name' => $curReceived['user_name'],
                ],
                'createTime'      => formatDisplayDateTime($curReceived['created_at']),
                'remark'          => $curReceived['remark'],
                'voucherPictures' => $curReceivedVoucher,
            ];

            $returnReceivedList[] = $tmpPurchaseOrder;
        }

        return self::Success($returnReceivedList);
    }
}
