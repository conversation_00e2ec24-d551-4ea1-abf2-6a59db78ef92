<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\PurchaseSupplierModel;

class PurchaseSupplierLogic extends Logic
{
    public static function GetValidPurchaseSupplier(int $supplierId = 0, string $supplierUid = ''): LogicResult
    {
        if (empty($supplierId) && empty($supplierUid))
        {
            return self::Fail('查找有效采购供应商，缺少必选参数', 400);
        }

        $where = [];
        if (!empty($supplierId))
        {
            $where['id'] = $supplierId;
        }
        if (!empty($supplierUid))
        {
            $where['uid'] = $supplierUid;
        }

        $getSupplierRes = PurchaseSupplierModel::getData(where: $where);
        $getSupplierRes = $getSupplierRes ? current($getSupplierRes) : [];
        if (empty($getSupplierRes))
        {
            return self::Fail('采购供应商无效、不存在', 42002);
        }
        if ($getSupplierRes['status'] != 1)
        {
            return self::Fail('采购供应商已暂停供货', 42002);
        }

        return self::Success($getSupplierRes);
    }
}
