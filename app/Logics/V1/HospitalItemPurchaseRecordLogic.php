<?php

namespace App\Logics\V1;

use Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\PurchaseReceivedDetailModel;
use App\Models\HospitalItemPurchaseRecordModel;

class HospitalItemPurchaseRecordLogic extends Logic
{
    /**
     * 标记医院采购商品记录
     *
     * @param int   $itemId
     * @param int   $receivedDetailId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function MarkPurchaseItemRecord(int $itemId, int $receivedDetailId, array $publicParams): LogicResult
    {
        if (empty($itemId) || empty($receivedDetailId))
        {
            return self::Fail('标记医院采购商品记录，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('标记医院采购商品记录，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('标记医院采购商品记录，缺少公共必选参数', 400);
        }

        // 签收记录
        $getReceivedRes = PurchaseReceivedDetailModel::getOneReceivedDetailById($receivedDetailId, $hospitalId, $itemId);
        if (empty($getReceivedRes))
        {
            return self::Fail('标记医院采购商品记录，签收记录不存在', 400);
        }

        HospitalItemPurchaseRecordModel::on()
                                       ->firstOrCreate(['hospital_id' => $hospitalId, 'item_id' => $itemId],
                                                       ['org_id' => $orgId, 'brand_id' => $brandId, 'received_detail_id' => $receivedDetailId]);

        return self::Success();
    }
}
