<?php

namespace App\Logics\V1;

use Arr;
use App\Enums\WarehouseEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\StockWarehouseModel;
use App\Models\StockWarehouseShelfModel;

class WarehouseLogic extends Logic
{
    /**
     * 获取仓库药房列表
     *
     * @param int $hospitalId
     *
     * @return LogicResult
     */
    public static function GetWarehouseList(int $hospitalId): LogicResult
    {
        if (empty($hospitalId))
        {
            return self::Fail('获取仓库药房列表，缺少医院ID必选参数', 400);
        }

        $getWarehouseListRes = StockWarehouseModel::getData(where: ['hospital_id' => $hospitalId, 'status' => 1]);
        if (empty($getWarehouseListRes))
        {
            return self::Success(['data' => []]);
        }

        $returnWarehouseList = [];
        foreach ($getWarehouseListRes as $curInfo)
        {
            $returnWarehouseList[] = [
                'uid'  => $curInfo['uid'],
                'name' => $curInfo['name'],
                'desc' => $curInfo['desc'],
            ];
        }

        return self::Success(['data' => $returnWarehouseList]);
    }

    /**
     * 获取有效仓库信息
     *
     * @param int   $warehouseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidWarehouseById(int $warehouseId, array $publicParams): LogicResult
    {
        if (empty($warehouseId))
        {
            return self::Fail('获取有效仓库信息，缺少仓库ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效仓库信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效仓库信息，缺少医院ID必选参数', 400);
        }

        // 验证仓库是否存在
        $getWarehouseRes = StockWarehouseModel::getData(where: [
                                                                   'id'          => $warehouseId,
                                                                   'hospital_id' => $hospitalId,
                                                                   'status'      => 1
                                                               ]);
        $getWarehouseRes = $getWarehouseRes ? current($getWarehouseRes) : [];
        if (empty($getWarehouseRes))
        {
            return self::Fail('获取有效仓库信息，仓库不存在或已失效', 41000);
        }

        return self::Success($getWarehouseRes);
    }

    /**
     * 添加添加药房
     *
     * @param array $addWarehouseParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function AddWarehouse(array $addWarehouseParams, array $publicParams): LogicResult
    {
        if (empty($addWarehouseParams))
        {
            return self::Fail('添加仓库，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加仓库，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalOrgId) || empty($hospitalBrandId) || empty($userId))
        {
            return self::Fail('添加仓库，缺少公共必选参数', 400);
        }

        // 业务参数
        $warehouseName = trimWhitespace($addWarehouseParams['name'] ?? '');
        $warehouseDesc = trimWhitespace($addWarehouseParams['desc'] ?? '');
        if (empty($warehouseName))
        {
            return self::Fail('添加仓库，缺少仓库名称必选参数', 400);
        }

        // 添加仓库
        $insertWarehouseData = [
            'uid'         => generateUUID(),
            'org_id'      => $hospitalOrgId,
            'brand_id'    => $hospitalBrandId,
            'hospital_id' => $hospitalId,
            'name'        => $warehouseName,
            'desc'        => $warehouseDesc,
            'created_by'  => $userId,
        ];
        StockWarehouseModel::insertOne($insertWarehouseData);

        return self::Success();
    }

    /**
     * 编辑仓库
     *
     * @param int   $warehouseId
     * @param array $editWarehouseParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function EditWarehouse(int $warehouseId, array $editWarehouseParams, array $publicParams): LogicResult
    {
        if (empty($warehouseId) || empty($editWarehouseParams))
        {
            return self::Fail('编辑仓库，缺少仓库必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑仓库，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('编辑仓库，缺少医院ID必选参数', 400);
        }

        // 业务参数
        $warehouseName = trimWhitespace($editWarehouseParams['name'] ?? '');
        $warehouseDesc = trimWhitespace($editWarehouseParams['desc'] ?? '');
        if (empty($warehouseName))
        {
            return self::Fail('编辑仓库，缺少仓库名称必选参数', 400);
        }

        // 验证仓库是否存在
        $getWarehouseRes = self::GetValidWarehouseById($warehouseId, $publicParams);
        if ($getWarehouseRes->isFail())
        {
            return $getWarehouseRes;
        }

        // 编辑仓库
        $updateWarehouseData = [
            'name' => $warehouseName,
            'desc' => $warehouseDesc,
        ];
        StockWarehouseModel::updateOne($warehouseId, $updateWarehouseData);

        return self::Success();
    }

    /**
     * 删除仓库
     *
     * @param int   $warehouseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function DeleteWarehouse(int $warehouseId, array $publicParams): LogicResult
    {
        if (empty($warehouseId))
        {
            return self::Fail('删除仓库，缺少仓库ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('删除仓库，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($userId))
        {
            return self::Fail('删除仓库，缺少医院ID必选参数', 400);
        }

        // 验证仓库是否存在
        $getWarehouseRes = self::GetValidWarehouseById($warehouseId, $publicParams);
        if ($getWarehouseRes->isFail())
        {
            return $getWarehouseRes;
        }

        // 仓库下是否存在有效货位
        $getWarehouseShelfTotalRes = StockWarehouseShelfModel::getTotalNumber(['warehouse_id' => $warehouseId, 'status' => 1]);
        if ($getWarehouseShelfTotalRes > 0)
        {
            return self::Fail('当前仓库存在有效的货位，不可删除', 41002);
        }

        // 删除仓库
        StockWarehouseModel::updateOne($warehouseId, ['deleted_by' => $userId, 'deleted_at' => getCurrentTimeWithMilliseconds(), 'status' => 0]);

        return self::Success();
    }

    /**
     * 获取仓库货架列表
     *
     * @param int   $warehouseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetWarehouseShelvesList(int $warehouseId, array $publicParams): LogicResult
    {
        if (empty($warehouseId))
        {
            return self::Fail('获取仓库货架列表，缺少仓库ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取仓库货架列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取仓库货架列表，缺少医院ID必选参数', 400);
        }

        // 验证仓库是否存在
        $getWarehouseRes = self::GetValidWarehouseById($warehouseId, $publicParams);
        if ($getWarehouseRes->isFail())
        {
            return $getWarehouseRes;
        }

        // 获取仓库货架列表
        $getWarehouseShelfListRes = StockWarehouseShelfModel::getData(where   : [
                                                                                    'warehouse_id' => $warehouseId,
                                                                                    'hospital_id'  => $hospitalId,
                                                                                    'status'       => 1
                                                                                ],
                                                                      group   : 'shelf_number',
                                                                      orderBys: ['created_at' => 'desc']);
        if (empty($getWarehouseShelfListRes))
        {
            return self::Success(['data' => []]);
        }

        $returnShelfList = [];
        foreach ($getWarehouseShelfListRes as $curInfo)
        {
            $returnShelfList[] = [
                'warehouseUid' => $getWarehouseRes->getData('uid', ''),
                'shelfNumber'  => chr($curInfo['shelf_number']),
            ];
        }

        return self::Success(['data' => $returnShelfList]);
    }

    /**
     * 获取货架货位列表
     *
     * @param int    $warehouseId
     * @param string $shelfNumber
     * @param array  $publicParams
     *
     * @return LogicResult
     */
    public static function GetWarehouseShelfSlotList(int $warehouseId, string $shelfNumber, array $publicParams): LogicResult
    {
        if (empty($warehouseId))
        {
            return self::Fail('获取货位列表，缺少仓库ID必选参数', 400);
        }
        if (empty($shelfNumber))
        {
            return self::Fail('获取货位列表，缺少货架号必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取货位列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取货位列表，缺少医院ID必选参数', 400);
        }

        // 获取仓库信息
        $getWarehouseRes = self::GetValidWarehouseById($warehouseId, $publicParams);
        if ($getWarehouseRes->isFail())
        {
            return $getWarehouseRes;
        }

        $getWarehouseRes = $getWarehouseRes->getData();
        $warehouseUid    = $getWarehouseRes['uid'];

        // 获取货架信息
        $intShelfNumber       = ord($shelfNumber);
        $getWarehouseShelfRes = StockWarehouseShelfModel::getData(where: [
                                                                             'warehouse_id' => $warehouseId,
                                                                             'shelf_number' => $intShelfNumber,
                                                                             'hospital_id'  => $hospitalId,
                                                                             'status'       => 1
                                                                         ]);

        $shelfUsedColumns = [];
        $shelfUsedRows    = [];
        $shelfSlotMap     = [];
        foreach ($getWarehouseShelfRes as $slotInfo)
        {
            // 列坐标信息
            $columnChar   = chr($slotInfo['shelf_number']);
            $columnNumber = $columnChar . str_pad($slotInfo['shelf_column'], 2, '0', STR_PAD_LEFT);

            // 行坐标信息
            $rowNumber = str_pad($slotInfo['shelf_row'], 2, '0', STR_PAD_LEFT);
            $rowName   = $slotInfo['shelf_row'];

            // 记录货位已使用的列
            if (!in_array($columnNumber, $shelfUsedColumns))
            {
                $shelfUsedColumns[] = $columnNumber;
            }

            // 记录货位已使用的行
            if (!in_array($rowNumber, $shelfUsedRows))
            {
                $shelfUsedRows[] = $rowNumber;
            }

            $shelfSlotMap[$rowName][$columnNumber] = [
                'uid'       => $slotInfo['uid'],
                'shelfCode' => $slotInfo['shelf_code'],
                'row'       => $rowNumber,
                'column'    => $columnNumber,
            ];
        }

        // 按行号从大到小排序
        krsort($shelfSlotMap);

        // 货架上的货位信息
        $shelfSlotRows = [];
        foreach ($shelfSlotMap as $row)
        {
            // 每行内按列排序
            ksort($row);
            $shelfSlotRows[] = $row;
        }

        // 计算剩余可添加的列数
        $leftColumnNum = WarehouseEnum::MaxShelfColumnNumber - count($shelfUsedColumns);
        if ($leftColumnNum < 0)
        {
            $leftColumnNum = 0;
        }

        // 计算剩余可添加的行数
        $leftRowNum = WarehouseEnum::MaxShelfRowNumber - count($shelfUsedRows);
        if ($leftRowNum < 0)
        {
            $leftRowNum = 0;
        }

        $returnData = [
            'warehouseUid'  => $warehouseUid,
            'shelfNumber'   => $shelfNumber,
            'leftColumnNum' => $leftColumnNum,
            'leftRowNum'    => $leftRowNum,
            'columns'       => $shelfUsedColumns,
            'rows'          => $shelfSlotRows,
        ];

        return self::Success(['data' => $returnData]);
    }

    /**
     * 新增货架，并且默认增加“1行1列”货位
     *
     * @param int   $warehouseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function AddShelf(int $warehouseId, array $publicParams): LogicResult
    {
        if (empty($warehouseId))
        {
            return self::Fail('添加货架，缺少仓库ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加货架，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('添加货架，缺少医院相关必选参数', 400);
        }

        // 验证仓库是否存在
        $getWarehouseRes = self::GetValidWarehouseById($warehouseId, $publicParams);
        if ($getWarehouseRes->isFail())
        {
            return $getWarehouseRes;
        }

        $getShelfNumberRes = StockWarehouseShelfModel::getData(fields  : ['shelf_number'],
                                                               where   : ['hospital_id' => $hospitalId, 'status' => 1],
                                                               group   : 'shelf_number',
                                                               orderBys: ['shelf_number' => 'asc']);
        if (!empty($getShelfNumberRes))
        {
            // 可用的货架编号：A-Z
            $canUseShelfNumbers = range(WarehouseEnum::MinShelfNumberAscii, WarehouseEnum::MaxShelfNumberAscii);

            // 已使用的货架编号
            $usedShelfNumbers = array_unique(array_column($getShelfNumberRes, 'shelf_number'));

            // 找出第一个未使用的货架编号
            $notUsedShelfNumbers = array_diff($canUseShelfNumbers, $usedShelfNumbers);
            $newShelfNumber      = current($notUsedShelfNumbers);
            if (empty($newShelfNumber))
            {
                return self::Fail('添加货架，货架数量已达到上限', 41001);
            }
        }
        else
        {
            $newShelfNumber = WarehouseEnum::MinShelfNumberAscii;
        }

        // 无可用的货架编号
        if (empty($newShelfNumber))
        {
            return self::Fail('添加货架，货架数量已达到上限', 41001);
        }

        // 生成新编号的货架，并且货位要保证有1行1列
        $getAddShelfSlotRes = self::AddShelfSlot($warehouseId,
                                                 [
                                                     'shelfNumber'    => chr($newShelfNumber),
                                                     'rowQuantity'    => 1,
                                                     'columnQuantity' => 1
                                                 ],
                                                 $publicParams);
        if ($getAddShelfSlotRes->isFail())
        {
            return $getAddShelfSlotRes;
        }

        return self::Success();

    }

    /**
     * 获取货位信息
     *
     * @param string $shelfCode
     * @param array  $publicParams
     *
     * @return LogicResult
     */
    public static function GetShelfSlotInfo(string $shelfCode, array $publicParams): LogicResult
    {
        if (empty($shelfCode))
        {
            return self::Fail('获取货位信息，缺少货位码必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取货位信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取货位信息，缺少医院相关必选参数', 400);
        }

        // 获取货位信息
        $getShelfSlotRes = StockWarehouseShelfModel::getData(where: [
                                                                        'shelf_code'  => $shelfCode,
                                                                        'hospital_id' => $hospitalId,
                                                                        'status'      => 1
                                                                    ]);
        $getShelfSlotRes = $getShelfSlotRes ? current($getShelfSlotRes) : [];
        if (empty($getShelfSlotRes))
        {
            return self::Fail('获取货位信息，货位不存在或已失效', 41000);
        }

        return self::Success($getShelfSlotRes);
    }

    /**
     * 新增仓库货位
     *
     * @param int   $warehouseId
     * @param       $addSlotParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function AddShelfSlot(int $warehouseId, $addSlotParams, array $publicParams): LogicResult
    {
        if (empty($warehouseId))
        {
            return self::Fail('新增货位，缺少仓库ID必选参数', 400);
        }
        if (empty($addSlotParams))
        {
            return self::Fail('新增货位，缺少货位必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('新增货位，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($orgId) || empty($brandId) || empty($userId))
        {
            return self::Fail('新增货位，缺少医院相关必选参数', 400);
        }

        // 业务参数
        $shelfNumber    = strval(Arr::get($addSlotParams, 'shelfNumber', ''));
        $columnQuantity = intval(Arr::get($addSlotParams, 'columnQuantity', 0));
        $rowQuantity    = intval(Arr::get($addSlotParams, 'rowQuantity', 0));
        if ($rowQuantity <= 0 && $columnQuantity <= 0)
        {
            return self::Fail('新增货位，行数和列数至少需要一个大于0', 41002);
        }

        // 验证仓库是否存在
        $getWarehouseRes = self::GetValidWarehouseById($warehouseId, $publicParams);
        if ($getWarehouseRes->isFail())
        {
            return $getWarehouseRes;
        }

        // 转换货架编号为ASCII码
        $shelfNumberAscii = ord($shelfNumber);

        // 获取已有货架信息
        $getWarehouseShelfRes = StockWarehouseShelfModel::getData(where: [
                                                                             'warehouse_id' => $warehouseId,
                                                                             'shelf_number' => $shelfNumberAscii,
                                                                             'hospital_id'  => $hospitalId,
                                                                             'status'       => 1
                                                                         ]);

        // 计算现有货架的最大行列数
        $existingMaxRow    = 0;
        $existingMaxColumn = 0;
        $existingSlotMap   = [];
        foreach ($getWarehouseShelfRes as $slotInfo)
        {
            $existingMaxRow    = max($existingMaxRow, $slotInfo['shelf_row']);
            $existingMaxColumn = max($existingMaxColumn, $slotInfo['shelf_column']);

            $existingSlotMap[$slotInfo['shelf_row']][$slotInfo['shelf_column']] = true;
        }

        // 计算新的总行列数（累加模式）
        // 如果现有货架为空，至少要有1行1列作为基础
        if ($existingMaxRow == 0 && $existingMaxColumn == 0)
        {
            // 空货架：至少创建1行1列，然后加上用户要增加的数量
            $newTotalRows    = max(1, $rowQuantity);
            $newTotalColumns = max(1, $columnQuantity);
        }
        else
        {
            // 非空货架：在现有基础上增加
            // 如果不增加行，保持现有行数；如果不增加列，保持现有列数
            $newTotalRows    = $existingMaxRow + $rowQuantity;
            $newTotalColumns = $existingMaxColumn + $columnQuantity;

            // 确保至少保持现有的最小值
            $newTotalRows    = max($existingMaxRow, $newTotalRows);
            $newTotalColumns = max($existingMaxColumn, $newTotalColumns);
        }

        // 检查是否超过最大限制
        if ($newTotalRows > WarehouseEnum::MaxShelfRowNumber)
        {
            return self::Fail('新增货位，行数超过最大限制(' . WarehouseEnum::MaxShelfRowNumber . ')', 41003);
        }

        if ($newTotalColumns > WarehouseEnum::MaxShelfColumnNumber)
        {
            return self::Fail('新增货位，列数超过最大限制(' . WarehouseEnum::MaxShelfColumnNumber . ')', 41003);
        }

        // 生成需要新增的货位数据
        $insertShelfData = [];
        for ($row = 1; $row <= $newTotalRows; $row ++)
        {
            for ($column = 1; $column <= $newTotalColumns; $column ++)
            {
                // 检查该位置是否已存在
                if (!isset($existingSlotMap[$row][$column]))
                {
                    // 生成货位编码
                    $curColumnCode = str_pad($column, 2, '0', STR_PAD_LEFT);
                    $curRowCode    = str_pad($row, 2, '0', STR_PAD_LEFT);
                    $curShelfCode  = $shelfNumber . $curColumnCode . '-' . $curRowCode;

                    $insertShelfData[] = [
                        'uid'          => generateUUID(),
                        'org_id'       => $orgId,
                        'brand_id'     => $brandId,
                        'hospital_id'  => $hospitalId,
                        'warehouse_id' => $warehouseId,
                        'shelf_number' => $shelfNumberAscii,
                        'shelf_row'    => $row,
                        'shelf_column' => $column,
                        'shelf_code'   => $curShelfCode,
                        'created_by'   => $userId,
                    ];
                }
            }
        }

        // 批量插入新货位数据
        if (!empty($insertShelfData))
        {
            StockWarehouseShelfModel::insert($insertShelfData);
        }

        return self::Success();
    }
}
