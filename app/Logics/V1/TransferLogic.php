<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PageEnum;
use App\Enums\TransferStatusEnum;
use App\Enums\OutpatientStatusEnum;
use App\Enums\CaseSourceTypeEnum;
use App\Enums\InpatientStatusEnum;
use App\Enums\RegistrationModeEnum;
use App\Enums\RegistrationTypeEnum;
use App\Enums\RegistrationSourceEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Models\CasesModel;
use App\Models\UsersModel;
use App\Models\TransfersModel;
use App\Models\PetVitalSignModel;
use App\Models\HospitalUserModel;
use App\Models\RegistrationsModel;

class TransferLogic extends Logic
{
    /**
     * 获取转诊相关医院列表，列表筛选选择医院使用
     *
     * @param int   $listType
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTransferRelationHospitalList(int $listType, array $publicParams): LogicResult
    {
        if (empty($listType) || !in_array($listType, [1, 2]))
        {
            return self::Fail('获取转诊医院列表，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取转诊医院列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取转诊医院列表，缺少医院ID必选参数', 400);
        }

        // 获取转诊相关医院列表，本院转出：返回转入到的医院筛选列表；本院转入：返回其它医院转出的医院筛选列表
        $getWhere = [
            ['status', '!=', TransferStatusEnum::Invalid->value],
            ['reason_id', '=', RegistrationSourceEnum::TransferHospital->value]
        ];
        if ($listType == 1)
        {
            $getWhere[]        = ['hospital_id', '=', $hospitalId];
            $getHospitalIdsRes = TransfersModel::getData(fields: ['transfer_hospital_id as hospital_id'],
                                                         where : $getWhere,
                                                         group : 'transfer_hospital_id');
        }
        else
        {
            $getWhere[]        = ['transfer_hospital_id', '=', $hospitalId];
            $getHospitalIdsRes = TransfersModel::getData(fields: ['hospital_id as hospital_id'],
                                                         where : $getWhere,
                                                         group : 'hospital_id');
        }

        // 无任何转诊
        if (empty($getHospitalIdsRes))
        {
            return self::Success();
        }

        // 获取医院信息
        $hospitalIds    = array_column($getHospitalIdsRes, 'hospital_id');
        $getHospitalRes = HospitalLogic::GetHospitalsBaseInfo($hospitalIds);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        $returnHospitalList = [];
        $getHospitalRes     = $getHospitalRes->getData('hospitals', []);
        foreach ($getHospitalRes as $item)
        {
            $returnHospitalList[] = [
                'uid'  => $item['uid'],
                'name' => $item['aliasName'],
            ];
        }

        return self::Success($returnHospitalList);
    }

    /**
     * 获取有效转诊信息
     *
     * @param int   $transferId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidTransferById(int $transferId, array $publicParams): LogicResult
    {
        if (empty($transferId))
        {
            return self::Fail('获取有效转诊信息，缺少转诊ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效转诊信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效转诊信息，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取有效转诊信息，缺少医生ID必选参数', 400);
        }

        // 获取转诊信息
        $getWhere       = [['id', '=', $transferId], ['status', '!=', TransferStatusEnum::Invalid->value]];
        $getTransferRes = TransfersModel::getData(where: $getWhere);
        $getTransferRes = $getTransferRes ? current($getTransferRes) : [];
        if (empty($getTransferRes))
        {
            return self::Fail('转诊单不存在或已失效', 40301);
        }

        return self::Success($getTransferRes);
    }

    /**
     * 获取本院转出的转诊列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTransferOutHospitalList(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取转诊列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取转诊列表，缺少医院ID必选参数', 400);
        }

        // 业务参数
        $listType = intval(Arr::get($searchParams, 'listType', 1));
        $page     = intval(Arr::get($searchParams, 'page', 1)) ?? PageEnum::DefaultPageIndex->value;
        $count    = intval(Arr::get($searchParams, 'count', 10)) ?? PageEnum::DefaultPageSize->value;
        if (empty($listType) || !in_array($listType, [1, 2]))
        {
            return self::Fail('获取转诊列表，列表类型参数错误', 400);
        }

        // 查询条件，只查询转院转诊的，院内转诊不查询
        $getWhere = [
            ['hospital_id', '=', $hospitalId],
            ['reason_id', '=', RegistrationSourceEnum::TransferHospital->value],
            ['status', '!=', TransferStatusEnum::Invalid->value]
        ];
        if (!empty($searchParams['keyword']))
        {
            $getWhere[] = ['transfer_code', 'like', '%' . $searchParams['keyword'] . '%'];
        }
        if (!empty($searchParams['startDate']) && strtotime($searchParams['startDate']) !== false)
        {
            $getWhere[] = ['created_at', '>=', $searchParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($searchParams['endDate']) && strtotime($searchParams['endDate']) !== false)
        {
            $getWhere[] = ['created_at', '<=', $searchParams['endDate'] . ' 23:59:59'];
        }
        if (is_numeric($searchParams['status']) && in_array($searchParams['status'], TransferStatusEnum::values()))
        {
            $getWhere[] = ['status', '=', $searchParams['status']];
        }

        // 本院转出的转诊单，指定医院代表查询具体转入到某医院的
        if (!empty($searchParams['hospitalId']))
        {
            $getWhere[] = ['transfer_hospital_id', '=', $searchParams['hospitalId']];
        }

        // 获取转诊数量
        $getTransferTotalRes = TransfersModel::getTotalNumber($getWhere);
        if (empty($getTransferTotalRes) || $getTransferTotalRes <= 0)
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取转诊列表
        $getTransferListRes = TransfersModel::getData(where    : $getWhere,
                                                      orderBys : ['created_at' => 'desc'],
                                                      pageIndex: $page,
                                                      pageSize : $count);
        if (empty($getTransferListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 转入的医院信息
        $transferHospitalIds = array_column($getTransferListRes, 'transfer_hospital_id');
        $getHospitalRes      = HospitalLogic::GetHospitalsBaseInfo($transferHospitalIds, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        $getHospitalRes = $getHospitalRes->getData('hospitals', []);
        $getHospitalRes = array_column($getHospitalRes, null, 'id');

        // 格式化转诊列表
        $getFormatTransferListRes = self::FormatTransferList($getTransferListRes, $getHospitalRes);
        if ($getFormatTransferListRes->isFail())
        {
            return $getFormatTransferListRes;
        }

        return self::Success(['total' => $getTransferTotalRes, 'data' => $getFormatTransferListRes->getData()]);
    }

    /**
     * 获取转入本院的转诊列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTransferInHospitalList(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取转诊列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取转诊列表，缺少医院ID必选参数', 400);
        }

        // 业务参数
        $listType = intval(Arr::get($searchParams, 'listType', 1));
        $page     = intval(Arr::get($searchParams, 'page', 1)) ?? PageEnum::DefaultPageIndex->value;
        $count    = intval(Arr::get($searchParams, 'count', 10)) ?? PageEnum::DefaultPageSize->value;
        if (empty($listType) || !in_array($listType, [1, 2]))
        {
            return self::Fail('获取转诊列表，列表类型参数错误', 400);
        }

        // 查询条件，只查询转院转诊的，院内转诊不查询
        $getWhere = [
            ['transfer_hospital_id', '=', $hospitalId],
            ['reason_id', '=', RegistrationSourceEnum::TransferHospital->value],
            ['status', '!=', TransferStatusEnum::Invalid->value]
        ];
        if (!empty($searchParams['keyword']))
        {
            $getWhere[] = ['transfer_code', 'like', '%' . $searchParams['keyword'] . '%'];
        }
        if (!empty($searchParams['startDate']) && strtotime($searchParams['startDate']) !== false)
        {
            $getWhere[] = ['created_at', '>=', $searchParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($searchParams['endDate']) && strtotime($searchParams['endDate']) !== false)
        {
            $getWhere[] = ['created_at', '<=', $searchParams['endDate'] . ' 23:59:59'];
        }
        if (is_numeric($searchParams['status']) && in_array($searchParams['status'], TransferStatusEnum::values()))
        {
            $getWhere[] = ['status', '=', $searchParams['status']];
        }

        // 转入的转诊单，指定医院代表查询具体哪家转出的
        if (!empty($searchParams['hospitalId']))
        {
            $getWhere[] = ['hospital_id', '=', $searchParams['hospitalId']];
        }

        // 获取转诊数量
        $getTransferTotalRes = TransfersModel::getTotalNumber($getWhere);
        if (empty($getTransferTotalRes) || $getTransferTotalRes <= 0)
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取转诊列表
        $getTransferListRes = TransfersModel::getData(where    : $getWhere,
                                                      orderBys : ['created_at' => 'desc'],
                                                      pageIndex: $page,
                                                      pageSize : $count);
        if (empty($getTransferListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 转入的医院信息
        $transferHospitalIds = array_column($getTransferListRes, 'transfer_hospital_id');
        $getHospitalRes      = HospitalLogic::GetHospitalsBaseInfo($transferHospitalIds, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        $getHospitalRes = $getHospitalRes->getData('hospitals', []);
        $getHospitalRes = array_column($getHospitalRes, null, 'id');

        // 格式化转诊列表
        $getFormatTransferListRes = self::FormatTransferList($getTransferListRes, $getHospitalRes);
        if ($getFormatTransferListRes->isFail())
        {
            return $getFormatTransferListRes;
        }

        return self::Success(['total' => $getTransferTotalRes, 'data' => $getFormatTransferListRes->getData()]);
    }

    /**
     * 门诊结束诊断-院内转诊
     *
     * @param array $transferParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function TransferWithinHospital(array $transferParams, array $publicParams): LogicResult
    {
        if (empty($transferParams))
        {
            return self::Fail('院内转诊，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('院内转诊，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('院内转诊，缺少必选参数', 400);
        }

        // 转诊业务参数，转移前病历ID、业务来源（门诊、住院）、关联业务ID号来源、转移原因ID（对应的其实是转入的挂号原因）、转诊接受医生UID
        $caseId            = $transferParams['caseId'] ?? 0;
        $sourceType        = $transferParams['sourceType'] ?? 0;
        $sourceRelationId  = $transferParams['sourceRelationId'] ?? 0;
        $transferDoctorUid = $transferParams['transferDoctorUid'] ?? 0;
        if (empty($caseId) || empty($sourceType) || empty($sourceRelationId))
        {
            return self::Fail('院内转诊，缺少必选参数', 400);
        }
        if (empty($transferDoctorUid))
        {
            return self::Fail('院内转诊时，转诊医生必选', 400);

        }

        // 院内转诊只支持门诊
        if (!CaseSourceTypeEnum::getCaseIsOutpatientBySourceType($sourceType))
        {
            return self::Fail('院内转诊只支持门诊转', 40302);
        }

        // 获取病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes = $getCaseRes->getData();
        if ($getCaseRes['source_relation_id'] != $sourceRelationId)
        {
            return self::Fail('转诊门诊与病历关联门诊不一致', 40303);
        }
        if (empty($getCaseRes['finished']))
        {
            return self::Fail('转诊病历未结束诊断，不可转诊', 40304);
        }

        // 获取病历中宠物体况信息
        $caseId                 = $getCaseRes['id'];
        $getCasePetVitalSignRes = PetVitalSignModel::getVitalSignByCaseIdOrRegistrationId($caseId);
        if (empty($getCasePetVitalSignRes))
        {
            return self::Fail('院内转诊，病历宠物关联体征信息不存在', 40305);
        }

        // 获取门诊信息
        $getValidOutpatientRes = OutpatientLogic::GetValidOutpatientById($sourceRelationId, $publicParams);
        if ($getValidOutpatientRes->isFail())
        {
            return $getValidOutpatientRes;
        }

        $getOutpatientRes = $getValidOutpatientRes->getData();
        if ($getOutpatientRes['status'] != OutpatientStatusEnum::Completed->value)
        {
            return self::Fail('转诊的门诊非结束就诊，不可转诊', 40306);
        }

        // 获取门诊关联的挂号信息
        $getRegistrationRes = RegistrationsModel::getOne($getOutpatientRes['registration_id']);
        if (empty($getRegistrationRes))
        {
            return self::Fail('转诊门诊关联挂号不存在', 40307);
        }

        // 转诊医生是否存在
        $getDoctorInfoRes = UsersModel::getOneByUid($transferDoctorUid);
        if (empty($getDoctorInfoRes))
        {
            return self::Fail('转诊接诊医生不存在', 40308);
        }

        // TODO 是否有医生角色
        $getTransferDoctorRes = HospitalUserModel::getHospitalUsers($hospitalId, [$getDoctorInfoRes['id']]);
        $transferDoctorId     = $getTransferDoctorRes->isNotEmpty() ? $getTransferDoctorRes->first()->user_id : 0;
        if ($getTransferDoctorRes->isEmpty() || empty($transferDoctorId))
        {
            return self::Fail('转诊接诊医生不存在', 40308);
        }
        if ($transferDoctorId == $doctorId)
        {
            return self::Fail('转诊接诊医生不能与原诊断医生一致', 40308);
        }

        // 转诊
        try
        {
            DB::beginTransaction();

            // 创建转诊单
            $transferCode       = generateBusinessCodeNumber(BusinessCodePrefixEnum::YNZZ);
            $insertTransferData = [
                'transfer_code'        => $transferCode,
                'org_id'               => $hospitalOrgId,
                'brand_id'             => $hospitalBrandId,
                'hospital_id'          => $hospitalId,
                'case_id'              => $caseId,
                'reason_id'            => RegistrationSourceEnum::InHospitalTransfer->value,
                'member_id'            => $getCaseRes['member_id'],
                'pet_id'               => $getCaseRes['pet_id'],
                'transfer_org_id'      => $hospitalOrgId,
                'transfer_brand_id'    => $hospitalBrandId,
                'transfer_hospital_id' => $hospitalId,
                'transfer_doctor_id'   => $transferDoctorId,
                'created_by'           => $doctorId,
            ];
            $transferId         = TransfersModel::insertOne($insertTransferData);

            // 创建挂号
            $addRegistrationParams = [
                'transferCode'          => $transferCode,
                'PetWeight'             => $getCasePetVitalSignRes['weight'],
                'registrationSourceId'  => RegistrationSourceEnum::InHospitalTransfer->value,
                'registrationReasonId'  => $getRegistrationRes['reason_id'],
                'registrationTypeId'    => RegistrationTypeEnum::TransferVisit->value,
                'registrationDoctorUid' => $transferDoctorUid,
                'registrationMode'      => RegistrationModeEnum::RegistrationAndPay->value,
            ];
            $addRegistrationRes    = RegistrationLogic::AddRegistration($getCaseRes['member_id'],
                                                                        $getCaseRes['pet_id'],
                                                                        $addRegistrationParams,
                                                                        $publicParams);
            $newRegistrationId     = $addRegistrationRes->getData('registrationId', 0);
            if ($addRegistrationRes->isFail() || empty($newRegistrationId))
            {
                DB::rollBack();

                return $addRegistrationRes;
            }

            // 回写转诊单对应新的挂号ID
            TransfersModel::updateOne($transferId, ['transfer_registration_id' => $newRegistrationId]);

            DB::commit();

            return self::Success(['transferCode' => $insertTransferData['transfer_code']]);

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 院内转诊异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('院内转诊异常', 40309);
        }
    }

    /**
     * 内部转院
     *
     * @param array $transferParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function TransferToAnotherHospital(array $transferParams, array $publicParams): LogicResult
    {
        if (empty($transferParams))
        {
            return self::Fail('内部转院，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('内部转院，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalUid     = trim(Arr::get($publicParams, '_hospitalUid'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('内部转院，缺少必选参数', 400);
        }

        // 转院业务参数，转移前病历ID、业务来源（门诊、住院）、关联业务ID号来源、转移原因ID（对应的其实是转入的挂号原因）、转院医院UID
        $caseId              = $transferParams['caseId'] ?? 0;
        $sourceType          = $transferParams['sourceType'] ?? 0;
        $sourceRelationId    = $transferParams['sourceRelationId'] ?? 0;
        $transferHospitalUid = $transferParams['transferHospitalUid'] ?? '';
        $transferRemark      = $transferParams['transferRemark'] ?? '';
        if (empty($caseId) || empty($sourceType) || empty($sourceRelationId))
        {
            return self::Fail('内部转院，缺少必选参数', 400);
        }
        if (empty($transferHospitalUid))
        {
            return self::Fail('内部转院，转入医院必选', 400);

        }

        // 获取病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes = $getCaseRes->getData();
        if ($getCaseRes['source_relation_id'] != $sourceRelationId)
        {
            return self::Fail('转院来源与病历关联来源不一致', 40303);
        }
        if (empty($getCaseRes['finished']))
        {
            return self::Fail('转院病历未结束诊断，不可转院', 40304);
        }

        // 获取病历中宠物体况信息
        $caseId                 = $getCaseRes['id'];
        $getCasePetVitalSignRes = PetVitalSignModel::getVitalSignByCaseIdOrRegistrationId($caseId);
        if (empty($getCasePetVitalSignRes))
        {
            return self::Fail('内部转院，病历宠物关联体征信息不存在', 40305);
        }

        // 获取来源是门诊、住院信息的信息
        if (CaseSourceTypeEnum::getCaseIsOutpatientBySourceType($sourceType))
        {
            $getRelationInfoRes = OutpatientLogic::GetValidOutpatientById($sourceRelationId, $publicParams);
            if ($getRelationInfoRes->getData('status', - 1) != OutpatientStatusEnum::Completed->value)
            {
                return self::Fail('转院的门诊非结束就诊，不可转院', 40306);
            }
        }
        else
        {
            $getRelationInfoRes = InpatientLogic::GetValidInpatientById($sourceRelationId, $publicParams);
            if ($getRelationInfoRes->getData('status', - 1) != InpatientStatusEnum::DischargedInpatient->value)
            {
                return self::Fail('转出的住院非出院状态，不可转院', 40306);
            }
        }

        // 转入医院与当前医院一致
        if ($transferHospitalUid == $hospitalUid)
        {
            return self::Fail('转出与转入医院一致，不可转院', 40310);
        }

        // 转入的医院是否存在，并且是否可以接受
        $getTransferHospitalRes = HospitalLogic::GetHospitalBaseInfo($transferHospitalUid, withId: true);
        if ($getTransferHospitalRes->isFail())
        {
            return $getTransferHospitalRes;
        }

        $getTransferHospitalRes      = $getTransferHospitalRes->getData();
        $getTransferHospitalExistRes = HospitalLogic::getTransferHospitalList($publicParams,
                                                                              $getTransferHospitalRes['id']);
        if ($getTransferHospitalExistRes->isFail())
        {
            return $getTransferHospitalExistRes;
        }
        if (empty($getTransferHospitalExistRes->getData()))
        {
            return self::Fail('转入的医院不存在或不可接受转院', 40311);
        }

        // 转院
        try
        {
            DB::beginTransaction();

            // 创建转院单
            $insertTransferData = [
                'transfer_code'        => generateBusinessCodeNumber(BusinessCodePrefixEnum::NBZY),
                'org_id'               => $hospitalOrgId,
                'brand_id'             => $hospitalBrandId,
                'hospital_id'          => $hospitalId,
                'case_id'              => $caseId,
                'reason_id'            => RegistrationSourceEnum::TransferHospital->value,
                'member_id'            => $getCaseRes['member_id'],
                'pet_id'               => $getCaseRes['pet_id'],
                'transfer_org_id'      => $getTransferHospitalRes['orgId'],
                'transfer_brand_id'    => $getTransferHospitalRes['brandId'],
                'transfer_hospital_id' => $getTransferHospitalRes['id'],
                'remark'               => $transferRemark,
                'created_by'           => $userId,
            ];
            TransfersModel::insertOne($insertTransferData);

            DB::commit();

            return self::Success(['transferCode' => $insertTransferData['transfer_code']]);

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 内部转院异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('内部转院异常', 40316);
        }
    }

    /**
     * 拒绝接受转诊
     *
     * @param int    $transferId
     * @param string $rejectReason
     * @param array  $publicParams
     *
     * @return LogicResult
     */
    public static function RejectAcceptTransfer(int $transferId, string $rejectReason, array $publicParams): LogicResult
    {
        if (empty($transferId))
        {
            return self::Fail('拒绝转诊，缺少转诊ID必选参数', 400);
        }
        if (empty($rejectReason))
        {
            return self::Fail('拒绝转诊，缺少拒绝原因必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('拒绝转诊，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('拒绝转诊，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('拒绝转诊，缺少医生ID必选参数', 400);
        }

        // 获取转院单
        $getTransferRes = self::GetValidTransferById($transferId, $publicParams);
        if ($getTransferRes->isFail())
        {
            return $getTransferRes;
        }

        $getTransferRes = $getTransferRes->getData();
        if ($getTransferRes['transfer_hospital_id'] != $hospitalId)
        {
            return self::Fail('转院单非本院接收，不可操作', 40312);
        }
        if ($getTransferRes['status'] != TransferStatusEnum::Waiting->value)
        {
            return self::Fail('转院单状态非待处理，不可操作', 40313);
        }

        // 更新转院单为已拒绝
        TransfersModel::updateOne($transferId,
                                  [
                                      'status'        => TransferStatusEnum::Rejected->value,
                                      'reject_reason' => $rejectReason,
                                      'reject_by'     => $userId,
                                  ]);

        return self::Success();
    }

    /**
     * 格式化转诊列表
     *
     * @param array $transferListRes
     * @param array $hospitalListRes
     *
     * @return LogicResult
     */
    private static function FormatTransferList(array $transferListRes, array $hospitalListRes): LogicResult
    {
        if (empty($transferListRes))
        {
            return self::Success();
        }

        // 转诊宠物信息
        $petIds    = array_column($transferListRes, 'pet_id');
        $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        $getPetRes = $getPetRes->getData();

        // 获取病历信息
        $caseIds    = array_column($transferListRes, 'case_id');
        $getCaseRes = CasesModel::getData(whereIn: ['id' => $caseIds], keyBy: 'id');

        $returnTransferList = [];
        foreach ($transferListRes as $curInfo)
        {
            $curMemberInfo   = $getPetRes[$curInfo['pet_id']]['memberInfo'] ?? [];
            $curPetInfo      = $getPetRes[$curInfo['pet_id']]['petInfo'] ?? [];
            $curHospitalInfo = $hospitalListRes[$curInfo['transfer_hospital_id']] ?? [];

            $tmpTransferInfo = [
                'transferCode'   => $curInfo['transfer_code'],
                'status'         => [
                    'id'   => $curInfo['status'],
                    'name' => TransferStatusEnum::getDescription($curInfo['status']),
                ],
                'transferDate'   => formatDisplayDateTime($curInfo['created_at']),
                'transferRemark' => $curInfo['remark'],
                'rejectReason'   => $curInfo['reject_reason'],
                'petInfo'        => $curPetInfo,
                'memberInfo'     => $curMemberInfo,
                'hospitalInfo'   => [
                    'uid'  => $curHospitalInfo['uid'] ?? '',
                    'name' => $curHospitalInfo['aliasName'] ?? '',
                ],
                'caseInfo'       => [
                    'caseCode' => $getCaseRes[$curInfo['case_id']]['case_code'] ?? '',
                ]
            ];

            $returnTransferList[] = $tmpTransferInfo;
        }

        return self::Success($returnTransferList);
    }
}
