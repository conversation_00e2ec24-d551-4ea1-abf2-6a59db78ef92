<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RegistrationReasonModel;

class RegistrationReasonLogic extends Logic
{
    /**
     * 获取挂号原因
     * @return LogicResult
     */
    public static function GetRegistrationReasonOptions(): LogicResult
    {
        $getRegistrationReasonRes = RegistrationReasonModel::getData(where: ['status' => 1]);

        $registrationReasonInfo = [];
        foreach ($getRegistrationReasonRes as $curInfo)
        {
            $registrationReasonInfo[] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['registrationReasonOptions' => $registrationReasonInfo]);
    }
}
