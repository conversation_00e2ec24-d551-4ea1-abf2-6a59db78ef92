<?php

namespace App\Logics\V1;

use Arr;
use DB;
use Log;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PageEnum;
use App\Enums\CaseSourceTypeEnum;
use App\Enums\CaseTemperatureTypeEnum;
use App\Enums\PetWeightSourceTypeEnum;
use App\Enums\CaseBloodPressureTypeEnum;
use App\Models\TestModel;
use App\Models\UsersModel;
use App\Models\ImagesModel;
use App\Models\RecipeModel;
use App\Models\MemberModel;
use App\Models\CasesModel;
use App\Models\HisBrandModel;
use App\Models\MemberPetsModel;
use App\Models\PetVitalSignModel;
use App\Models\CaseSnapshotModel;
use App\Models\RecipeSnapshotModel;
use App\Models\DiseaseCategoryModel;
use App\Models\TreatmentOutcomeModel;
use App\Models\CasesCompletedChangeLogModel;

class CaseLogic extends Logic
{
    /**
     * 体况-获取体温类型
     * @return LogicResult
     */
    public static function GetTemperatureTypeOptions(): LogicResult
    {
        $returnData = [];

        foreach (CaseTemperatureTypeEnum::cases() as $typeObj)
        {
            $returnData[] = [
                'id'   => $typeObj->value,
                'name' => CaseTemperatureTypeEnum::getDescription($typeObj->value),
            ];
        }

        return self::Success(['temperatureTypeOptions' => $returnData]);
    }

    /**
     * 体况-获取血压测量部位
     * @return LogicResult
     */
    public static function GetBloodPressureTypeOptions(): LogicResult
    {
        $returnData = [];

        foreach (CaseBloodPressureTypeEnum::cases() as $typeObj)
        {
            $returnData[] = [
                'id'   => $typeObj->value,
                'name' => CaseBloodPressureTypeEnum::getDescription($typeObj->value),
            ];
        }

        return self::Success(['bloodPressureTypeOptions' => $returnData]);
    }

    /**
     * 查找病历列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function SearchCaseList(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($searchParams))
        {
            return self::Fail('查找病历列表，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('查找病历列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('查找病历列表，缺少医院必选参数', 400);
        }

        // 业务参数
        $keyword     = trim(Arr::get($searchParams, 'keyword', ''));
        $searchRange = intval(Arr::get($searchParams, 'rangeType', 1)); // 病历查找范围，1-本院 2-全部
        $page        = intval(Arr::get($searchParams, 'page', 1)) ?? PageEnum::DefaultPageIndex->value;
        $count       = intval(Arr::get($searchParams, 'count', 10)) ?? PageEnum::DefaultPageSize->value;

        //TODO 获取病历基本条件，如果医院品牌互通，那么获取整个组织下所有互通的品牌下医院的数据。否则不互通只获取当前品牌下所有医院。默认品牌下医院必须为互通
        $hospitalBrandNotWhere = [];
        if ($searchRange == 2)
        {
            $brandIsChain = HisBrandModel::getBrandIsShare($hospitalBrandId);
            if (!empty($brandIsChain))
            {
                $hospitalWhere = [['org_id', '=', $hospitalOrgId]];

                // 获取当前组织下不互通的品牌
                $getNotShareBrandRes = HisBrandModel::getShareBrandByOrgId($hospitalOrgId, [$hospitalBrandId], 0);
                if ($getNotShareBrandRes->isNotEmpty())
                {
                    $hospitalBrandNotWhere['brand_id'] = $getNotShareBrandRes->pluck('id')
                                                                             ->toArray();
                }
            }
            else
            {
                $hospitalWhere = [['brand_id', '=', $hospitalBrandId]];
            }
        }
        else
        {
            $hospitalWhere = [['hospital_id', '=', $hospitalId]];
        }

        // 基础条件
        $memberBaseWhere = array_merge($hospitalWhere, [['status', '=', 1], ['delete_status', '=', 0]]);

        // 存在单条件多维度查询条件
        $memberIds = [];
        $petIds    = [];
        if (!empty($keyword))
        {
            // 1. 优先验证是否手机号查询
            if (checkValidCellphone($keyword))
            {
                $phoneWhere     = array_merge($memberBaseWhere, [['phone', '=', $keyword]]);
                $membersByPhone = MemberModel::getData(fields    : ['id'],
                                                       where     : $phoneWhere,
                                                       whereNotIn: $hospitalBrandNotWhere);
                if (!empty($membersByPhone))
                {
                    $memberIds = array_merge($memberIds, array_column($membersByPhone, 'id'));
                }
            }
            else
            {
                // 2. 先匹配用户名称
                $nameLikeWhere = array_merge($memberBaseWhere, [['name', 'like', '%' . $keyword . '%']]);
                $membersByName = MemberModel::getData(fields    : ['id'],
                                                      where     : $nameLikeWhere,
                                                      whereNotIn: $hospitalBrandNotWhere,
                                                      pageSize  : 50);
                if (!empty($membersByName))
                {
                    $memberIds = array_merge($memberIds, array_column($membersByName, 'id'));
                }

                // 3. 匹配宠物名称、病历号
                $petStatusWhere = array_merge($hospitalWhere, [['status', '=', 1]]);

                // 使用OR条件组合宠物名称和病历号查询
                $petLikeWhereOr = [
                    ['name', 'like', '%' . $keyword . '%'],
                    ['record_number', 'like', '%' . $keyword . '%']
                ];

                $membersByPet = MemberPetsModel::getData(fields    : ['id', 'member_id'],
                                                         where     : $petStatusWhere,
                                                         orWhere   : $petLikeWhereOr,
                                                         whereNotIn: $hospitalBrandNotWhere,
                                                         pageSize  : 50);
                if (!empty($membersByPet))
                {
                    $memberIds = array_merge($memberIds, array_column($membersByPet, 'member_id'));
                    $petIds    = array_merge($petIds, array_column($membersByPet, 'id'));
                }

                // 去重
                $memberIds = array_unique($memberIds);
                $petIds    = array_unique($petIds);

                // 限制最多50个结果
                $memberIds = array_slice($memberIds, 0, 100);
                $petIds    = array_slice($petIds, 0, 100);
            }

            // 如果没有找到任何匹配的会员
            if (empty($memberIds))
            {
                return self::Success(['total' => 0, 'data' => []]);
            }
        }

        // 单个条件查询
        $withWhere = [];
        if (!empty($searchParams['startDate']) && strtotime($searchParams['startDate']) !== false)
        {
            $withWhere[] = ['finished_time', '>=', $searchParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($searchParams['endDate']) && strtotime($searchParams['endDate']) !== false)
        {
            $withWhere[] = ['finished_time', '<=', $searchParams['endDate'] . ' 23:59:59'];
        }
        if (!empty($searchParams['doctorId']))
        {
            $withWhere[] = ['doctor_id', '=', $searchParams['doctorId']];
        }

        // 组合条件
        $getWhere = array_merge($hospitalWhere, $withWhere, [['finished', '=', 1], ['status', '=', 1]]);

        // in条件
        $getWhereIn = [];
        if (!empty($memberIds))
        {
            $getWhereIn['member_id'] = $memberIds;
        }
        if (!empty($petIds))
        {
            $getWhereIn['pet_id'] = $petIds;
        }

        // 获取条目总数
        $getCaseTotalRes = CaseSnapshotModel::getTotalNumber($getWhere, $getWhereIn, $hospitalBrandNotWhere);
        if (empty($getCaseTotalRes) || $getCaseTotalRes <= 0)
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取条目数据
        $getCaseListRes = CaseSnapshotModel::getData(fields    : [
                                                                     'case_base_info',
                                                                     'finished_time',
                                                                     'case_full_info->memberInfo as memberInfo',
                                                                     'case_full_info->petInfo as petInfo'
                                                                 ],
                                                     where     : $getWhere,
                                                     whereIn   : $getWhereIn,
                                                     whereNotIn: $hospitalBrandNotWhere,
                                                     orderBys  : ['finished_time' => 'desc'],
                                                     pageIndex : $page,
                                                     pageSize  : $count);
        if (empty($getCaseListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $returnCaseList = [];
        foreach ($getCaseListRes as $curInfo)
        {
            $curCaseBaseInfo                 = json_decode($curInfo['case_base_info'], true);
            $curCaseBaseInfo['memberInfo']   = json_decode($curInfo['memberInfo'], true);
            $curCaseBaseInfo['petInfo']      = json_decode($curInfo['petInfo'], true);
            $curCaseBaseInfo['finishedTime'] = formatDisplayDateTime($curInfo['finished_time']);

            $returnCaseList[] = $curCaseBaseInfo;
        }

        return self::Success(['total' => $getCaseTotalRes, 'data' => $returnCaseList]);
    }

    /**
     * 获取疾病分类
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetDiseaseCategory(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('获取疾病分类，缺少病历ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取疾病分类，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取疾病分类，缺少医院ID必选参数', 400);
        }

        // 获取病历信息
        $getCaseRes = self::GetValidCaseById($caseId, $publicParams, false, false);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 获取病历关联宠物信息
        $petId     = $getCaseRes->getData('pet_id', 0);
        $getPetRes = PetLogic::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 宠物类别
        $petCategoryId = $getPetRes->getData('category_id', 0);

        // 获取匹配宠物类别的疾病分类
        $getDiseaseCategoryRes = DiseaseCategoryModel::getDiseaseCategory($petCategoryId);

        $returnData = [];
        foreach ($getDiseaseCategoryRes as $curInfo)
        {
            $returnData[] = [
                'id'      => $curInfo['id'],
                'name'    => $curInfo['name'],
                'isOther' => $curInfo['is_other'],
            ];
        }

        return self::Success(['diseaseCategoryOptions' => $returnData]);
    }

    /**
     * 获取病历结束诊断的就诊结果
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetEndCaseTreatmentOutcome(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('获取病历结束诊断的就诊结果，缺少病历ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取病历结束诊断的就诊结果，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取病历结束诊断的就诊结果，缺少医院ID必选参数', 400);
        }

        // 获取病历信息
        $getCaseRes = self::GetUnderWayCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes = $getCaseRes->getData();

        // 获取门诊、住院可用的治疗结果
        $getWhere = ['status' => 1];
        if (CaseSourceTypeEnum::getCaseIsOutpatientBySourceType($getCaseRes['source_type']))
        {
            $getWhere['is_outpatient'] = 1;
        }
        else
        {
            $getWhere['is_inpatient'] = 1;
        }
        $getTreatmentOutcomeRes = TreatmentOutcomeModel::getData(fields  : ['id', 'name'],
                                                                 where   : $getWhere,
                                                                 orderBys: ['order_by' => 'asc']);
        if (empty($getTreatmentOutcomeRes))
        {
            return self::Success();
        }

        // 如果是住院结束诊断，只使用通用
        if (CaseSourceTypeEnum::getCaseIsInpatientBySourceType($getCaseRes['source_type']))
        {
            return self::Success(['data' => $getTreatmentOutcomeRes]);
        }

        // 获取病历下已经支付的处方
        $getPaidCaseRecipeRes = RecipeModel::getWithPaidStatusRecipeByCaseId($caseId,
                                                                             CaseSourceTypeEnum::Outpatient->value,
                                                                             $getCaseRes['source_relation_id'],
                                                                             [1]);
        if (!empty($getPaidCaseRecipeRes))
        {
            return self::Success(['data' => $getTreatmentOutcomeRes]);
        }

        // 获取未支付处方的就诊结果
        $noPaidTreatmentOutcome = TreatmentOutcomeModel::getNoPaidTreatmentOutcome();
        if (!empty($noPaidTreatmentOutcome))
        {
            $getTreatmentOutcomeRes[] = $noPaidTreatmentOutcome;
        }

        return self::Success(['data' => $getTreatmentOutcomeRes]);
    }

    /**
     * 获取有效病历信息
     *
     * @param int   $caseId
     * @param array $publicParams
     * @param bool  $withHospitalId 增加公参中医院ID
     * @param bool  $withDoctorId   增加公参中医生ID
     *
     * @return LogicResult
     */
    public static function GetValidCaseById(int $caseId, array $publicParams, bool $withHospitalId = true, bool $withDoctorId = true): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('获取有效病历信息，缺少病历ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效病历信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效病历信息，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取有效病历信息，缺少医生ID必选参数', 400);
        }

        // 获取病历信息
        $where = [
            'id'     => $caseId,
            'status' => 1,
        ];
        if (!empty($withHospitalId))
        {
            $where['hospital_id'] = $hospitalId;
        }
        $getCaseRes = CasesModel::getData(where: $where);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return self::Fail('病历不存在', 38000);
        }

        // 如果指定医生，对于门诊的病历是必须一致的，因为门诊只能存在一个医生诊断。如果是住院的话，不限制医生，因为可以多个医生同时接诊
        if (!empty($withDoctorId))
        {
            // 病历来源异常
            if (!in_array($getCaseRes['source_type'],
                          [CaseSourceTypeEnum::Outpatient->value, CaseSourceTypeEnum::Inpatient->value]))
            {
                return self::Fail('病历来源类型错误', 38000);
            }

            // 门诊病历，当前医生与病历医生不一致
            if (CaseSourceTypeEnum::getCaseIsOutpatientBySourceType($getCaseRes['source_type']) && $getCaseRes['doctor_id'] != $doctorId)
            {
                return self::Fail('非本医生病历，不可操作', 38002);
            }
        }

        return self::Success($getCaseRes);
    }

    /**
     * 获取正在诊断的病历信息
     *
     * @param int   $caseId
     * @param array $publicParams
     * @param bool  $withHospitalId 增加公参中医院ID
     * @param bool  $withDoctorId   增加公参中医生ID
     *
     * @return LogicResult
     */
    public static function GetUnderWayCaseById(int $caseId, array $publicParams, bool $withHospitalId = true, bool $withDoctorId = true): LogicResult
    {
        if (empty($caseId) || empty($publicParams))
        {
            return self::Fail('caseId、publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 获取病历信息
        $getCaseRes = self::GetValidCaseById($caseId, $publicParams, $withHospitalId, $withDoctorId);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes = $getCaseRes->getData();
        if ($getCaseRes['finished'] == 1)
        {
            return self::Fail('病历已完结，不可操作', 38003);
        }
        if ($getCaseRes['status'] != 1)
        {
            return self::Fail('病历已关闭，不可操作', 38004);
        }

        return self::Success($getCaseRes);
    }

    /**
     * 获取病历信息
     *
     * @param int   $caseId
     * @param array $publicParams
     * @param bool  $withReportResult 是否返回化验报告结果
     *
     * @return LogicResult
     */
    public static function GetCaseDetail(int $caseId, array $publicParams, bool $withReportResult = false): LogicResult
    {
        if (empty($caseId) || empty($publicParams))
        {
            return self::Fail('caseId、publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }

        // 获取病历基础信息
        $getCaseBaseInfoRes = self::GetCaseBaseInfo($caseId, $publicParams);
        if ($getCaseBaseInfoRes->isFail())
        {
            return $getCaseBaseInfoRes;
        }

        $casePetId   = $getCaseBaseInfoRes->getData('petId', 0);
        $getCaseInfo = $getCaseBaseInfoRes->getData('caseInfo', []);
        if (empty($getCaseInfo))
        {
            return self::Fail('病历基础信息不存在', 38000);
        }

        // 获取病历诊断信息
        $getCaseDiagnoseRes = self::GetCaseDiagnose($caseId, $publicParams);
        if ($getCaseDiagnoseRes->isFail())
        {
            return $getCaseDiagnoseRes;
        }

        $getCaseDiagnoseRes = $getCaseDiagnoseRes->getData();

        // 获取病历中宠物信息
        $getPetRes = PetLogic::GetPetDetailFullInfo($casePetId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        $getMemberInfo = $getPetRes->getData('memberInfo', []);
        $getPetInfo    = $getPetRes->getData('petInfo', []);

        // 获取病历中宠物体况信息
        $getCasePetVitalSignRes = self::GetCasePetVitalSign($caseId, $publicParams);
        if ($getCasePetVitalSignRes->isFail())
        {
            return self::Fail('病历宠物关联体征信息不存在', 38006);
        }

        $getCasePetVitalSignRes = $getCasePetVitalSignRes->getData();

        // 需要获取病历下已有结果的化验、影像结果详情
        $getTestResultRes  = [];
        $getImageResultRes = [];
        if (!empty($withReportResult))
        {
            // 获取病历下已经完成的化验项
            $getCaseTestRes = TestModel::getData(where: [
                                                            'case_id'       => $caseId,
                                                            'status'        => 1,
                                                            'start_status'  => 1,
                                                            'result_status' => 1
                                                        ]);
            if (!empty($getCaseTestRes))
            {
                $getTestResultRes = TestLogic::GetTestResultByTestIds(array_column($getCaseTestRes, 'id'),
                                                                      $publicParams,
                                                                      $withReportResult);
                if ($getTestResultRes->isFail())
                {
                    return $getTestResultRes;
                }

                $getTestResultRes = array_values($getTestResultRes->getData());
            }

            // 获取病历下已经完成的影像
            $getCaseImageRes = ImagesModel::getData(where: [
                                                               'case_id'       => $caseId,
                                                               'status'        => 1,
                                                               'start_status'  => 1,
                                                               'result_status' => 1
                                                           ]);
            if (!empty($getCaseImageRes))
            {
                $getImageResultRes = ImageLogic::GetImageResultByImageIds(array_column($getCaseImageRes, 'id'),
                                                                          $publicParams,
                                                                          $withReportResult);
                if ($getImageResultRes->isFail())
                {
                    return $getImageResultRes;
                }

                $getImageResultRes = array_values($getImageResultRes->getData());
            }
        }

        $returnDiagnoseInfo = [
            'caseInfo'         => $getCaseInfo,
            'memberInfo'       => $getMemberInfo,
            'petInfo'          => $getPetInfo,
            'petVitalSignInfo' => $getCasePetVitalSignRes,
            'diagnosisInfo'    => $getCaseDiagnoseRes,
            'testResultList'   => $getTestResultRes,
            'imageResultList'  => $getImageResultRes,
        ];

        return self::Success($returnDiagnoseInfo);
    }

    /**
     * 获取病历基础信息
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCaseBaseInfo(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('caseId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }

        // 获取病历信息
        $getCaseRes = self::GetValidCaseById($caseId, $publicParams, false, false);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes = $getCaseRes->getData();

        // 获取病历关联医院
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $getCaseRes['hospital_id'], true, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        $hospitalInfo   = [];
        $getHospitalRes = $getHospitalRes->getData();
        if (!empty($getHospitalRes))
        {
            $hospitalInfo = [
                'uid'  => $getHospitalRes['uid'] ?? '',
                'name' => $getHospitalRes['aliasName'] ?? '',
            ];
        }

        // 获取病历关联医生
        $doctorInfo   = [];
        $getDoctorRes = UsersModel::getOne($getCaseRes['doctor_id']);
        if (!empty($getDoctorRes))
        {
            $doctorInfo = [
                'uid'  => $getDoctorRes['uid'] ?? '',
                'name' => $getDoctorRes['name'] ?? '',
            ];
        }

        // 病历来源
        $sourceInfo = [
            'id'   => $getCaseRes['source_type'],
            'name' => CaseSourceTypeEnum::getDescription($getCaseRes['source_type']),
        ];

        // 获取病历关联诊断结果
        $treatmentOutcome = [];
        if (!empty($getCaseRes['outcome_id']))
        {
            $getTreatmentOutcomeRes = TreatmentOutcomeModel::getOne($getCaseRes['outcome_id']);
            if (!empty($getTreatmentOutcomeRes))
            {
                $treatmentOutcome = [
                    'id'   => $getTreatmentOutcomeRes['id'],
                    'name' => $getTreatmentOutcomeRes['name'],
                ];
            }
        }

        // 获取处方开具数量
        $getRecipeTotal = RecipeModel::getTotalNumber(['case_id' => $caseId, 'status' => 1]);

        $returnData = [
            'caseCode'      => $getCaseRes['case_code'],
            'recipeNum'     => $getRecipeTotal,
            'hospital'      => $hospitalInfo,
            'doctor'        => $doctorInfo,
            'source'        => $sourceInfo,
            'createTime'    => formatDisplayDateTime($getCaseRes['created_at']),
            'outcomeResult' => $treatmentOutcome,
        ];

        return self::Success([
                                 'memberId' => $getCaseRes['member_id'],
                                 'petId'    => $getCaseRes['pet_id'],
                                 'caseInfo' => $returnData,
                             ]);
    }

    /**
     * 获取病历宠物体征信息
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCasePetVitalSign(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('caseId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 获取病历中宠物体况信息
        $getCasePetVitalSignRes = PetVitalSignModel::getVitalSignByCaseIdOrRegistrationId($caseId);
        if (empty($getCasePetVitalSignRes))
        {
            return self::Fail('病历宠物关联体征信息不存在', 38006);
        }

        $returnData = [
            'birthday'      => $getCasePetVitalSignRes['birthday'],
            'weight'        => $getCasePetVitalSignRes['weight'],
            'physical'      => $getCasePetVitalSignRes['physical'],
            'heartbeat'     => $getCasePetVitalSignRes['heartbeat'],
            'respiration'   => $getCasePetVitalSignRes['respiration'],
            'temperature'   => [
                'id'    => $getCasePetVitalSignRes['temperature_type'],
                'name'  => CaseTemperatureTypeEnum::getDescription($getCasePetVitalSignRes['temperature_type']),
                'value' => $getCasePetVitalSignRes['temperature'],
            ],
            'bloodPressure' => [
                'id'    => $getCasePetVitalSignRes['blood_pressure_type'],
                'name'  => CaseBloodPressureTypeEnum::getDescription($getCasePetVitalSignRes['blood_pressure_type']),
                'value' => $getCasePetVitalSignRes['blood_pressure'],
            ],
            'createTime'    => formatDisplayDateTime($getCasePetVitalSignRes['created_at']),
        ];

        return self::Success($returnData);
    }

    /**
     * 获取病历诊断信息
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCaseDiagnose(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('caseid，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }

        // 获取病历信息
        $getCaseRes = self::GetValidCaseById($caseId, $publicParams, true, false);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes = $getCaseRes->getData();
        $returnData = [
            'pastMedicalHistory'    => $getCaseRes['past_illness'],
            'presentMedicalHistory' => $getCaseRes['present_illness'],
            'chiefComplaint'        => $getCaseRes['chief_complaint'],
            'clinicalExamination'   => $getCaseRes['clinical_examination'],
            'disease'               => [
                'id'   => $getCaseRes['disease_id'],
                'name' => $getCaseRes['disease_name'],
            ],
            'secondDisease'         => [
                'id'   => $getCaseRes['second_disease_id'],
                'name' => $getCaseRes['second_disease_name'],
            ],
            'clinicalDiagnosis'     => $getCaseRes['diagnose'],
            'medicalOrders'         => $getCaseRes['medical_orders'],
        ];

        return self::Success($returnData);
    }

    /**
     * 编辑病历宠物体征信息
     *
     * @param int   $caseId
     * @param array $vitalSignParams
     * @param array $publicParams
     * @param bool  $isEditHistory 是否编辑历史病历 true:是 false:否
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditCasePetVitalSign(int $caseId, array $vitalSignParams, array $publicParams, bool $isEditHistory = false): LogicResult
    {
        if (empty($caseId) || empty($vitalSignParams))
        {
            return self::Fail('编辑病历宠物体征信息，缺少必选参数', 38007);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑病历宠物体征信息，缺少公共参数', 38007);
        }

        // 公共参数
        $hospitalId    = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId))
        {
            return self::Fail('编辑病历宠物体征信息，缺少医院必选参数', 38007);
        }
        if (empty($hospitalOrgId))
        {
            return self::Fail('编辑病历宠物体征信息，缺少医院机构必选参数', 38007);
        }

        // 业务参数
        $petWeight         = (string) Arr::get($vitalSignParams, 'petWeight', 0);
        $physical          = intval(Arr::get($vitalSignParams, 'physical', 0));
        $heartbeat         = intval(Arr::get($vitalSignParams, 'heartbeat', 0));
        $respiration       = intval(Arr::get($vitalSignParams, 'respiration', 0));
        $temperatureType   = intval(Arr::get($vitalSignParams, 'temperatureType', 0));
        $temperature       = (string) Arr::get($vitalSignParams, 'temperature', 0);
        $bloodPressureType = intval(Arr::get($vitalSignParams, 'bloodPressureType', 0));
        $bloodPressure     = trim(Arr::get($vitalSignParams, 'bloodPressure', ''));

        // 体温类型和体温值必须同时存在，血压类型和血压值必须同时存在
        if ((bool) $temperatureType xor (bool) $temperature)
        {
            return self::Fail('体温类型和体温值必须同时存在', 38007);
        }
        if ((bool) $bloodPressureType xor (bool) $bloodPressure)
        {
            return self::Fail('血压类型和血压值必须同时存在', 38007);
        }

        // 如果修改进行中的病历，需要验证病历正在进行并且是当前医生的。如果修改的是历史病历，那么病历是已结束的。
        if ($isEditHistory)
        {
            $getCaseRes = self::GetValidCaseById($caseId, $publicParams, true, false);
        }
        else
        {
            $getCaseRes = self::GetUnderWayCaseById($caseId, $publicParams);
        }

        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes = $getCaseRes->getData();

        // 获取病历宠物关联体征信息
        $getCasePetVitalSignRes = PetVitalSignModel::getVitalSignByCaseIdOrRegistrationId($caseId);
        if (empty($getCasePetVitalSignRes))
        {
            return self::Fail('病历宠物关联体征信息不存在', 38007);
        }

        // 记录修改体况
        $vitalSignId            = $getCasePetVitalSignRes['id'] ?? 0;
        $updateCasePetVitalSign = [];

        // 宠物体重
        if (!empty($petWeight))
        {
            if ($petWeight < 0 || $petWeight > PetLogic::MAX_WEIGHT)
            {
                return self::Fail('宠物体重参数错误。0-100kg区间', 38007);
            }

            $updateCasePetVitalSign['weight'] = sprintf("%.2f", $petWeight);
        }

        // 体况
        if (!empty($physical))
        {
            if ($physical < 0 || $physical > 10)
            {
                return self::Fail('宠物体况参数错误。0-10区间', 38007);
            }

            $updateCasePetVitalSign['physical'] = $physical;
        }

        // 心率
        if (!empty($heartbeat))
        {
            if ($heartbeat < 0)
            {
                return self::Fail('宠物心率参数错误', 38007);
            }

            $updateCasePetVitalSign['heartbeat'] = $heartbeat;
        }

        // 呼吸
        if (!empty($respiration))
        {
            if ($respiration < 0)
            {
                return self::Fail('宠物呼吸参数错误', 38007);
            }

            $updateCasePetVitalSign['respiration'] = $respiration;
        }

        // 体温
        if (!empty($temperature))
        {
            if (empty($temperatureType) || !CaseTemperatureTypeEnum::exists($temperatureType))
            {
                return self::Fail('宠物体温类型参数错误', 38007);
            }

            if ($temperature < 0 || $temperature > 50)
            {
                return self::Fail('宠物体温参数错误。0-50℃区间', 38007);
            }

            $updateCasePetVitalSign['temperature']      = sprintf("%.2f", $temperature);
            $updateCasePetVitalSign['temperature_type'] = $temperatureType;
        }

        // 血压
        if (!empty($bloodPressure))
        {
            if (empty($bloodPressureType) || !CaseBloodPressureTypeEnum::exists($bloodPressureType))
            {
                return self::Fail('宠物血压测量部位参数错误', 38007);
            }

            if ($bloodPressure < 0 || $bloodPressure > 200)
            {
                return self::Fail('宠物血压参数错误。0-200mmHg区间', 38007);
            }

            $updateCasePetVitalSign['blood_pressure']      = sprintf("%.2f", $bloodPressure);
            $updateCasePetVitalSign['blood_pressure_type'] = $bloodPressureType;
        }

        // 无任何更新
        if (empty($updateCasePetVitalSign))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 更新病历宠物关联体征信息
            PetVitalSignModel::updateOne($vitalSignId, $updateCasePetVitalSign);

            // 记录宠物体重变动记录 TODO 1、如果每次修改都有宠物体重，记录会重复。2、是否同步修改宠物体重？？？
            if (array_key_exists('weight', $updateCasePetVitalSign) && $updateCasePetVitalSign['weight'] > 0)
            {
                $editPetWeightParams = [
                    'weight'       => $updateCasePetVitalSign['weight'],
                    'relationId'   => $vitalSignId,
                    'relationType' => CaseSourceTypeEnum::getCaseIsOutpatientBySourceType($getCaseRes['source_type']) ? PetWeightSourceTypeEnum::OutpatientCase->value : PetWeightSourceTypeEnum::InpatientCase->value,
                ];
                PetLogic::EditPetWeight($getCasePetVitalSignRes['pet_id'], $editPetWeightParams, $publicParams);
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新病历宠物关联体征信息异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('更新病历宠物关联体征信息异常', 38007);
        }
    }

    /**
     * 编辑历史病历宠物体征信息
     *
     * @param int   $caseId
     * @param array $vitalSignParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditHistoryCasePetVitalSign(int $caseId, array $vitalSignParams, array $publicParams): LogicResult
    {
        if (empty($caseId) || empty($vitalSignParams))
        {
            return self::Fail('编辑历史病历宠物体征信息，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑历史病历宠物体征信息，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('编辑历史病历宠物体征信息，缺少医院必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('编辑历史病历宠物体征信息，缺少医生必选参数', 400);
        }

        // 历史病历是否存在
        $getCaseRes = CaseSnapshotModel::getData([
                                                     'id',
                                                     'case_id',
                                                     'hospital_id',
                                                     'doctor_id',
                                                     'case_full_info->petVitalSignInfo as petVitalSignInfo'
                                                 ],
                                                 ['case_id' => $caseId, 'status' => 1]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return self::Fail('历史病历不存在', 38000);
        }

        // 验证是否可编辑
        $getCheckEditAbleRes = self::CheckEditAbleHistoryCasePetVitalSignOrDiagnose($getCaseRes, $publicParams);
        if ($getCheckEditAbleRes->isFail())
        {
            return $getCheckEditAbleRes;
        }

        try
        {
            DB::beginTransaction();

            // 调用统一编辑病历体况信息
            $getEditVitalSignRes = self::EditCasePetVitalSign($caseId, $vitalSignParams, $publicParams, true);
            if ($getEditVitalSignRes->isFail())
            {
                DB::rollBack();

                return $getEditVitalSignRes;
            }

            // 获取修改后的病历诊断信息
            $getCasePetVitalSignRes = self::GetCasePetVitalSign($caseId, $publicParams);
            if ($getCasePetVitalSignRes->isFail())
            {
                DB::rollBack();

                return $getCasePetVitalSignRes;
            }

            // 更新病历快照
            $rawJson     = json_encode($getCasePetVitalSignRes->getData(), JSON_UNESCAPED_UNICODE);
            $escapedJson = addslashes($rawJson);

            CaseSnapshotModel::updateOne($getCaseRes['id'], [
                'case_full_info' => DB::raw("JSON_SET(case_full_info, '$.petVitalSignInfo', CAST('$escapedJson' AS JSON))"),
            ]);

            // 记录更新日志
            $insertLog = [
                'hospital_id' => $hospitalId,
                'doctor_id'   => $doctorId,
                'case_id'     => $caseId,
                'before_data' => $getCaseRes['petVitalSignInfo'],
                'after_data'  => $rawJson,
            ];
            CasesCompletedChangeLogModel::insertOne($insertLog);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑历史病历宠物体征信息异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('编辑历史病历宠物体征信息异常', 38007);
        }
    }

    /**
     * 编辑病历诊断信息
     *
     * @param int   $caseId
     * @param array $diagnoseParams
     * @param array $publicParams
     * @param bool  $isEditHistory 是否编辑历史病历 true:是 false:否
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditCaseDiagnose(int $caseId, array $diagnoseParams, array $publicParams, bool $isEditHistory = false): LogicResult
    {
        if (empty($caseId) || empty($diagnoseParams))
        {
            return self::Fail('caseId、diagnoseParams，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 获取正在进行的病历信息
        if ($isEditHistory)
        {
            $getCaseRes = self::GetValidCaseById($caseId, $publicParams, true, false);
        }
        else
        {
            $getCaseRes = self::GetUnderWayCaseById($caseId, $publicParams);
        }

        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 如果修改疾病分类，验证是否存在
        $upDiseaseIds = [];
        if (isset($diagnoseParams['diseaseId']))
        {
            if (!empty($diagnoseParams['diseaseId']))
            {
                $upDiseaseIds[] = $diagnoseParams['diseaseId'];
            }
            else
            {
                $updateCaseDiagnoseData['disease_name'] = '';
            }
        }
        if (isset($diagnoseParams['secondDiseaseId']))
        {
            if (!empty($diagnoseParams['secondDiseaseId']))
            {
                $upDiseaseIds[] = $diagnoseParams['secondDiseaseId'];
            }
            else
            {
                $updateCaseDiagnoseData['second_disease_name'] = '';
            }
        }

        if (!empty($upDiseaseIds))
        {
            $upDiseaseIds  = array_unique($upDiseaseIds);
            $getDiseaseRes = DiseaseCategoryModel::getData(where  : ['status' => 1],
                                                           whereIn: ['id' => $upDiseaseIds],
                                                           keyBy  : 'id');
            if (count($getDiseaseRes) != count($upDiseaseIds))
            {
                return self::Fail('选择的疾病分类不存在', 400);
            }

            // 如果疾病ID变更，同时更新疾病名称
            $firstDiseaseIsOther = false;
            if (isset($diagnoseParams['diseaseId']) && !empty($getDiseaseRes[$diagnoseParams['diseaseId']]))
            {
                // 疾病名称
                $diseaseName = $getDiseaseRes[$diagnoseParams['diseaseId']]['name'] ?? '';

                // 如果选择疾病分类为“其他”，疾病名称使用填写的内容
                $firstDiseaseIsOther = $getDiseaseRes[$diagnoseParams['diseaseId']]['is_other'] ?? false;
                if (!empty($firstDiseaseIsOther))
                {
                    if (empty($diagnoseParams['otherDisease']))
                    {
                        return self::Fail('选择的疾病分类为“其他”，请填写疾病名称', 400);
                    }

                    $diseaseName = $diagnoseParams['otherDisease'];
                }

                // 更新第一疾病分类
                $updateCaseDiagnoseData['disease_id']   = $diagnoseParams['diseaseId'];
                $updateCaseDiagnoseData['disease_name'] = $diseaseName;
            }

            // 如果第二疾病ID变更，同时更新第二疾病名称
            if (isset($diagnoseParams['secondDiseaseId']) && !empty($getDiseaseRes[$diagnoseParams['secondDiseaseId']]))
            {
                $updateCaseDiagnoseData['second_disease_id']   = $diagnoseParams['secondDiseaseId'];
                $updateCaseDiagnoseData['second_disease_name'] = $getDiseaseRes[$diagnoseParams['secondDiseaseId']]['name'] ?? '';
            }

            // 如果第一疾病为“其他”，那么第二疾病分类设置为空
            if (!empty($firstDiseaseIsOther))
            {
                $updateCaseDiagnoseData['second_disease_id']   = 0;
                $updateCaseDiagnoseData['second_disease_name'] = '';
            }
        }

        // 映射关系
        $diagnoseParamsRelationDBField = [
            'pastIllness'         => 'past_illness',         // 既往病史
            'presentIllness'      => 'present_illness',      // 现在病史
            'chiefComplaint'      => 'chief_complaint',      // 主述
            'clinicalExamination' => 'clinical_examination', // 临床检查
            'diagnose'            => 'diagnose',             // 医生诊断
            'medicalOrders'       => 'medical_orders',       // 医嘱
        ];

        $diagnoseParamsFieldKeys = array_keys($diagnoseParamsRelationDBField);
        foreach ($diagnoseParamsFieldKeys as $field)
        {
            if (!isset($diagnoseParams[$field]))
            {
                continue;
            }

            $fieldValue = Arr::get($diagnoseParams, $field);
            if ((is_numeric($fieldValue) && $fieldValue >= 0) || is_string($fieldValue))
            {
                $curFieldDBKey                          = $diagnoseParamsRelationDBField[$field];
                $updateCaseDiagnoseData[$curFieldDBKey] = trim($diagnoseParams[$field]);
            }
        }

        if (empty($updateCaseDiagnoseData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 更新病历诊断信息
            CasesModel::updateOne($caseId, $updateCaseDiagnoseData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新病历诊断信息异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('更新病历诊断信息异常', 38005);
        }
    }

    /**
     * 编辑历史病历诊断信息
     *
     * @param int   $caseId
     * @param array $diagnoseParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditHistoryCaseDiagnose(int $caseId, array $diagnoseParams, array $publicParams): LogicResult
    {
        if (empty($caseId) || empty($diagnoseParams))
        {
            return self::Fail('编辑历史病历诊断信息，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑历史病历诊断信息，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('编辑历史病历诊断信息，缺少医院必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('编辑历史病历诊断信息，缺少医生必选参数', 400);
        }

        // 历史病历是否存在
        $getCaseRes = CaseSnapshotModel::getData([
                                                     'id',
                                                     'case_id',
                                                     'hospital_id',
                                                     'doctor_id',
                                                     'case_full_info->diagnosisInfo as diagnosisInfo'
                                                 ],
                                                 ['case_id' => $caseId, 'status' => 1]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return self::Fail('历史病历不存在', 38000);
        }

        // 验证是否可编辑
        $getCheckEditAbleRes = self::CheckEditAbleHistoryCasePetVitalSignOrDiagnose($getCaseRes, $publicParams);
        if ($getCheckEditAbleRes->isFail())
        {
            return $getCheckEditAbleRes;
        }

        try
        {
            DB::beginTransaction();

            // 调用统一编辑病历诊断信息
            $getEditDiagnoseRes = self::EditCaseDiagnose($caseId, $diagnoseParams, $publicParams, true);
            if ($getEditDiagnoseRes->isFail())
            {
                DB::rollBack();

                return $getEditDiagnoseRes;
            }

            // 获取修改后的病历诊断信息
            $getCaseDiagnoseRes = self::GetCaseDiagnose($caseId, $publicParams);
            if ($getCaseDiagnoseRes->isFail())
            {
                DB::rollBack();

                return $getCaseDiagnoseRes;
            }

            // 更新病历快照
            $rawJson     = json_encode($getCaseDiagnoseRes->getData(), JSON_UNESCAPED_UNICODE);
            $escapedJson = addslashes($rawJson);

            CaseSnapshotModel::updateOne($getCaseRes['id'], [
                'case_full_info' => DB::raw("JSON_SET(case_full_info, '$.diagnosisInfo', CAST('$escapedJson' AS JSON))"),
            ]);

            // 记录更新日志
            $insertLog = [
                'hospital_id' => $hospitalId,
                'doctor_id'   => $doctorId,
                'case_id'     => $caseId,
                'before_data' => $getCaseRes['diagnosisInfo'],
                'after_data'  => $rawJson,
            ];
            CasesCompletedChangeLogModel::insertOne($insertLog);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑历史病历诊断信息异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('编辑历史病历诊断信息异常', 38005);
        }
    }

    /**
     * 获取宠物历史病历
     *
     * @param int   $petId
     * @param array $publicParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return LogicResult
     */
    public static function GetHistoryCasesByPetId(int $petId, array $publicParams, int $iPage = 1, int $iPageSize = 10): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('petId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }

        // 获取宠物信息
        $getPetRes = PetLogic::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 获取可查看当前医院数据，互通的所有品牌
        $getAllowedBrandIdsRes = HospitalLogic::GetAllowedBrandIdsByHospitalId($hospitalId);
        if ($getAllowedBrandIdsRes->isFail())
        {
            return $getAllowedBrandIdsRes;
        }

        $allowedBrandIds = $getAllowedBrandIdsRes->getData('allowedBrandIds', []);
        if (empty($allowedBrandIds))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 从快照获取历史病历总数
        $getWhere               = ['pet_id' => $petId, 'status' => 1];
        $getWhereIn             = ['brand_id' => $allowedBrandIds];
        $getHistoryRecipesTotal = CaseSnapshotModel::getTotalNumber(where: $getWhere, whereIn: $getWhereIn);
        if (empty($getHistoryRecipesTotal))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 从快照获取历史病历列表
        $getHistoryRecipesRes = CaseSnapshotModel::getData(['case_base_info'],
            where    :                                     $getWhere,
            whereIn  :                                     $getWhereIn,
            pageIndex:                                     $iPage,
            pageSize :                                     $iPageSize);
        if (empty($getHistoryRecipesRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $returnHistoryCases = [];
        foreach ($getHistoryRecipesRes as $curInfo)
        {
            if (empty($curInfo['case_base_info']))
            {
                continue;
            }

            $returnHistoryCases[] = json_decode($curInfo['case_base_info'], true);
        }

        return self::Success(['total' => $getHistoryRecipesTotal, 'data' => $returnHistoryCases]);
    }

    /**
     * 获取历史病历详情
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetHistoryCaseDetail(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('查看历史病历详情，缺少病历ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('查看历史病历详情，缺少必选公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('查看历史病历详情，缺少医院ID必选参数', 400);
        }

        // 获取可查看当前医院数据，互通的所有品牌
        $getAllowedBrandIdsRes = HospitalLogic::GetAllowedBrandIdsByHospitalId($hospitalId);
        if ($getAllowedBrandIdsRes->isFail())
        {
            return $getAllowedBrandIdsRes;
        }

        $allowedBrandIds = $getAllowedBrandIdsRes->getData('allowedBrandIds', []);
        if (empty($allowedBrandIds))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取历史病历信息
        $getHistoryCaseDetailRes = CaseSnapshotModel::getData(fields : ['case_id', 'hospital_id', 'case_full_info'],
                                                              where  : ['case_id' => $caseId, 'status' => 1],
                                                              whereIn: ['brand_id' => $allowedBrandIds]);

        $getHistoryCaseDetailRes = $getHistoryCaseDetailRes ? current($getHistoryCaseDetailRes) : [];
        if (empty($getHistoryCaseDetailRes))
        {
            return self::Fail('历史病历不存在', 38000);
        }

        // 获取历史病历详情
        $historyCaseDetailData = json_decode($getHistoryCaseDetailRes['case_full_info'], true);
        if (empty($historyCaseDetailData))
        {
            return self::Fail('历史病历详情不存在', 38000);
        }

        // 获取历史病历处方详情
        $getHistoryCaseRecipeRes = RecipeSnapshotModel::getData(['recipe_info', 'recipe_items'],
                                                                ['case_id' => $caseId, 'status' => 1]);
        if (!empty($getHistoryCaseRecipeRes))
        {
            foreach ($getHistoryCaseRecipeRes as $curInfo)
            {
                if (empty($curInfo['recipe_info']))
                {
                    continue;
                }

                $historyCaseDetailData['recipeInfo'][] = array_merge(json_decode($curInfo['recipe_info'], true),
                                                                     [
                                                                         'items' => json_decode($curInfo['recipe_items'],
                                                                                                true)
                                                                     ]);
            }
        }

        // 默认历史病历不可修改
        $curCaseEditAble = false;

        // 验证是否可编辑
        $getCheckEditAbleRes = self::CheckEditAbleHistoryCasePetVitalSignOrDiagnose($getHistoryCaseDetailRes,
                                                                                    $publicParams);
        if ($getCheckEditAbleRes->isSuccess())
        {
            $curCaseEditAble = true;
        }

        // TODO 默认历史病历中，体况信息可修改
        if (!empty($historyCaseDetailData['petVitalSignInfo']) && is_array($historyCaseDetailData['petVitalSignInfo']))
        {
            $historyCaseDetailData['petVitalSignInfo']['editAble'] = $curCaseEditAble;
        }

        // TODO 默认历史病历中，诊断信息可修改
        if (!empty($historyCaseDetailData['diagnosisInfo']) && is_array($historyCaseDetailData['diagnosisInfo']))
        {
            $historyCaseDetailData['diagnosisInfo']['editAble'] = $curCaseEditAble;
        }

        return self::Success($historyCaseDetailData);
    }

    /**
     * 获取病历汇总
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCaseSummary(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('获取病历汇总，缺少病历ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取病历汇总，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取病历汇总，缺少医院ID必选参数', 400);
        }

        // 获取病历基础信息
        $getCaseRes = self::GetValidCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 汇总处方相关数量、金额
        $getCaseRecipeSummaryRes = RecipeModel::getRecipeSummaryByCaseId($caseId);

        // 汇总病历下化验数量、金额
        $getCaseTestSummaryRes = TestModel::getTestSummaryByCaseId($caseId);

        // 汇总病历下影像数量、金额
        $getCaseImageSummaryRes = ImagesModel::getImageSummaryByCaseId($caseId);

        $returnSummary = [
            'recipeSummary' => [
                'total'       => $getCaseRecipeSummaryRes['recipe_count'] ?? 0,
                'price'       => formatDisplayNumber($getCaseRecipeSummaryRes['total_price'] ?? 0),
                'paidPrice'   => formatDisplayNumber($getCaseRecipeSummaryRes['paid_price'] ?? 0),
                'unpaidPrice' => formatDisplayNumber($getCaseRecipeSummaryRes['unpaid_price'] ?? 0),
            ],
            'testSummary'   => [
                'total'       => $getCaseTestSummaryRes['test_count'] ?? 0,
                'price'       => formatDisplayNumber($getCaseTestSummaryRes['total_price'] ?? 0),
                'paidPrice'   => formatDisplayNumber($getCaseTestSummaryRes['paid_price'] ?? 0),
                'unpaidPrice' => formatDisplayNumber($getCaseTestSummaryRes['unpaid_price'] ?? 0),
            ],
            'imageSummary'  => [
                'total'       => $getCaseImageSummaryRes['image_count'] ?? 0,
                'price'       => formatDisplayNumber($getCaseImageSummaryRes['total_price'] ?? 0),
                'paidPrice'   => formatDisplayNumber($getCaseImageSummaryRes['paid_price'] ?? 0),
                'unpaidPrice' => formatDisplayNumber($getCaseImageSummaryRes['unpaid_price'] ?? 0),
            ],
        ];

        return self::Success(['caseSummary' => $returnSummary]);
    }

    /**
     * 验证病历是否可以结束
     *
     * @param int $hospitalId
     * @param int $sourceType
     * @param int $sourceRelationId
     * @param int $doctorId
     *
     * @return LogicResult
     */
    public static function CheckEndAbleBySourceType(int $hospitalId, int $sourceType, int $sourceRelationId, int $doctorId = 0): LogicResult
    {
        if (empty($hospitalId) || empty($sourceType) || empty($sourceRelationId))
        {
            return self::Fail('验证结束病历，缺少必选参数', 400);
        }

        // 获取关联的病历信息
        $getCaseRes = CasesModel::getCaseByRelationId($hospitalId, $sourceType, $sourceRelationId);
        if (empty($getCaseRes))
        {
            return self::Fail('验证结束病历，关联病历不存在', 38000);
        }

        // 门诊关联的病历，必须接诊医生关闭
        if (CaseSourceTypeEnum::getCaseIsOutpatientBySourceType($sourceType) && $getCaseRes['doctor_id'] != $doctorId)
        {
            return self::Fail('验证结束病历，非本医生病历，不可操作', 38002);
        }
        if ($getCaseRes['finished'] == 1)
        {
            return self::Fail('验证结束病历，病历已完结，不可操作', 38003);
        }
        if ($getCaseRes['status'] != 1)
        {
            return self::Fail('验证结束病历，病历已关闭，不可操作', 38004);
        }

        // 获取病历中宠物体况信息
        $caseId                 = $getCaseRes['id'];
        $getCasePetVitalSignRes = PetVitalSignModel::getVitalSignByCaseIdOrRegistrationId($caseId);
        if (empty($getCasePetVitalSignRes))
        {
            return self::Fail('验证结束病历，病历宠物关联体征信息不存在', 38006);
        }

        // 验证病历信息是否填写完整
        $caseMustField = [
            'chief_complaint'      => ['label' => '主述'],
            'past_illness'         => ['label' => '既往病史', 'tip' => '过敏信息'],
            'present_illness'      => ['label' => '现在病史'],
            'clinical_examination' => ['label' => '临床检查'],
            'diagnose'             => ['label' => '医生诊断'],
            'medical_orders'       => ['label' => '医嘱'],
            'disease_id'           => ['label' => '诊断第一疾病分类', 'tip' => '必选项'],
        ];
        foreach ($caseMustField as $field => $fieldInfo)
        {
            if (empty($getCaseRes[$field]))
            {
                $label = $fieldInfo['label'] ?? $field;
                $tip   = $fieldInfo['tip'] ?? '诊断信息';

                return self::Fail("{$tip}：{$label}，未录入", 38007);
            }
        }

        // 验证体征信息是否填写完整
        $casePetVitalSignMustField = [
            'weight'         => ['label' => '体重'],
            'physical'       => ['label' => '体况'],
            'heartbeat'      => ['label' => '心跳'],
            'respiration'    => ['label' => '呼吸'],
            'temperature'    => ['label' => '体温'],
            'blood_pressure' => ['label' => '血压'],
        ];
        foreach ($casePetVitalSignMustField as $field => $fieldInfo)
        {
            if (empty($getCasePetVitalSignRes[$field]))
            {
                $label = $fieldInfo['label'] ?? $field;
                $tip   = $fieldInfo['tip'] ?? '体况信息';

                return self::Fail("{$tip}：{$label}，未录入", 38008);
            }
        }

        // 判断门诊中是否存在未支付处方
        $getUnpaidRecipeRes = RecipeModel::getWithPaidStatusRecipeByCaseId($caseId,
                                                                           $sourceType,
                                                                           $sourceRelationId,
                                                                           [0, 2]);
        if (!empty($getUnpaidRecipeRes))
        {
            return self::Fail('验证结束病历，存在未支付处方，不可结束诊断', 36005);
        }

        return self::Success(['caseId' => $caseId, 'petVitalSignId' => $getCasePetVitalSignRes['id']]);
    }

    /**
     * 验证是否可编辑历史病历宠物体征信息、诊断信息
     *
     * @param array $caseInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function CheckEditAbleHistoryCasePetVitalSignOrDiagnose(array $caseInfo, array $publicParams): LogicResult
    {
        if (empty($caseInfo) || empty($publicParams))
        {
            return self::Fail('验证是否可编辑历史病历宠物体征信息，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证是否可编辑历史病历宠物体征信息，缺少医院必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('验证是否可编辑历史病历宠物体征信息，缺少医生必选参数', 400);
        }

        if (empty($caseInfo['hospital_id']) || $caseInfo['hospital_id'] != $hospitalId)
        {
            return self::Fail('验证是否可编辑历史病历宠物体征信息，医院不一致', 38007);
        }

        // TODO 是否增加角色验证？？

        return self::Success();
    }
}
