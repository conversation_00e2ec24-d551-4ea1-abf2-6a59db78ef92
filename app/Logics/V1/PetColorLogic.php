<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\PetColorDictModel;

class PetColorLogic extends Logic
{
    /**
     * 获取宠物颜色
     * @return LogicResult
     */
    public static function GetPetColorOptions(): LogicResult
    {
        $getPetColorRes = PetColorDictModel::getData(where: ['status' => 1]);

        $petColorInfos = [];
        foreach ($getPetColorRes as $curInfo)
        {
            $petColorInfos[] = [
                'id'      => $curInfo['id'],
                'name'    => $curInfo['name'],
                'isOther' => $curInfo['is_other'],
            ];
        }

        return self::Success(['petColorOptions' => $petColorInfos]);
    }
}
