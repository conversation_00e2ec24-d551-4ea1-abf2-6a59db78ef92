<?php

namespace App\Logics\V1\BuySheet;

use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\SheetBusinessTypeEnum;
use App\Models\MemberModel;
use App\Models\SheetUnionModel;
use App\Models\UsersModel;
use App\Support\SheetUnion\SheetUnionHelper;
use App\Support\Member\MemberHelper;
use App\Support\User\HospitalUserHelper;


/**
 * 统一购买单处理逻辑
 */
class SheetUnionLogic extends Logic
{
    /**
     * 购买单业务类型与逻辑类映射
     *
     * @var array
     */
    const array SHEET_BUSINESS_LOGIC_MAP = [
        SheetBusinessTypeEnum::Retail->value => RetailSheetLogic::class,
        SheetBusinessTypeEnum::Beauty->value => BeautyServiceSheetLogic::class,
    ];

    /**
     * 获取待支付购买单会员选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetSheetMemberOptions(array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取待支付购买单会员选项，缺少医院ID必选参数', 400);
        }

        $memberIds = SheetUnionModel::GetSheetMemberOptions($hospitalId);
        if (empty($memberIds))
        {
            return self::Success([]);
        }

        return self::Success(MemberHelper::GetMemberOptionsByMemberIds($memberIds));
    }

    /**
     * 获取待支付购买单创建人选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCreateUsersOptions(array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取待支付购买单会员选项，缺少医院ID必选参数', 400);
        }

        $userIds = SheetUnionModel::GetSheetCreateUsersOptions($hospitalId);
        if (empty($userIds))
        {
            return self::Success([]);
        }

        return self::Success(HospitalUserHelper::GetUserOptionsByUserIds($userIds, $hospitalId));
    }

    /**
     * 获取待支付购买单列表
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetUnpaidSheetList(array $params, array $publicParams): LogicResult
    {
        $memberId      = intval(Arr::get($params, 'memberId', 0));
        $memberUid     = trim(Arr::get($params, 'memberUid', ''));
        $createUserId  = intval(Arr::get($params, 'createUserId', 0));
        $createUserUid = trim(Arr::get($params, 'createUserUid', ''));
        $type          = trim(Arr::get($params, 'type', ''));
        $startDate     = trim(Arr::get($params, 'startDate', ''));
        $endDate       = trim(Arr::get($params, 'endDate', ''));
        //公共参数
        $hospitalId    = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($hospitalOrgId))
        {
            return self::Fail('获取待支付购买单列表，缺少医院ID、组织ID必选参数', 400);
        }

        if (!empty($memberUid) || !empty($memberId))
        {
            $member = MemberModel::getOneByIdOrUid(id: $memberId, uid: $memberUid, orgId: $hospitalOrgId);
            if (empty($member))
            {
                return self::Fail('会员不存在', 30001);
            }
            $memberId = $member->id;
        }
        if (!empty($createUserUid) || !empty($createUserId))
        {
            $createUser = UsersModel::getOneByIdOrUid(id: $createUserId, uid: $createUserUid);
            if (empty($createUser))
            {
                return self::Fail('创建人不存在', 10100);
            }
            $createUserId = $createUser->id;
        }
        if (!empty($type) && !SheetBusinessTypeEnum::exists($type))
        {
            return self::Fail('购买单业务类型参数错误', 400);
        }
        if (!empty($startDate) && strtotime($startDate) === false)
        {
            return self::Fail('开始日期格式错误', 400);
        }
        if (!empty($endDate) && strtotime($endDate) === false)
        {
            return self::Fail('结束日期格式错误', 400);
        }
        if (!empty($startDate) && !empty($endDate) && strtotime($startDate) > strtotime($endDate))
        {
            return self::Fail('开始日期不能大于结束日期', 400);
        }

        //构建查询条件
        $filters       = [];
        $businessTypes = [];
        if (!empty($memberId))
        {
            $filters['member_id'] = $memberId;
        }
        if (!empty($createUserId))
        {
            $filters['created_by'] = $createUserId;
        }
        if (!empty($startDate))
        {
            $filters['created_start_time'] = $startDate . ' 00:00:00';
        }
        if (!empty($endDate))
        {
            $filters['created_end_time'] = $endDate . ' 23:59:59';
        }
        if (!empty($type))
        {
            $businessTypes[] = $type;
        }

        $listRes = SheetUnionModel::GetUnpaidSheetList($hospitalId, $filters, $businessTypes);

        if ($listRes->isEmpty())
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $list = $listRes
            ->map(function ($item) {
                return (array) $item;
            })
            ->toArray();

        return self::Success([
                                 'total' => count($list),
                                 'data'  => SheetUnionHelper::FormatSheetListStructure($list, $publicParams)
                             ]);
    }


    /**
     * 检查逻辑类是否符合要求
     *
     * @return bool
     */
    private static function CheckSheetLogicIsValid(): bool
    {
        return array_all(
            self::SHEET_BUSINESS_LOGIC_MAP,
            fn($logic) => is_subclass_of($logic, BuySheetCommonBase::class)
        );
    }

    /**
     * 获取购买单业务逻辑类
     *
     * @param string $businessType
     *
     * @return string|null
     */
    private static function GetSheetLogicClass(string $businessType): ?string
    {
        return self::SHEET_BUSINESS_LOGIC_MAP[$businessType] ?? null;
    }
}
