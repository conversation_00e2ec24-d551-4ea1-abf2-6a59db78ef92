<?php

namespace App\Logics\V1\BuySheet;

use Illuminate\Support\Arr;
use App\Logics\LogicResult;
use App\Enums\BusinessCodePrefixEnum;
use App\Models\RetailSheetModel;
use App\Models\MemberModel;
use App\Models\UsersModel;
use App\Support\Retail\RetailSheetHelper;
use App\Logics\V1\HospitalUserLogic;

class RetailSheetLogic extends BuySheetCommonBase
{
    public static function GenerateSheetCode(): string
    {
        return generateBusinessCodeNumber(BusinessCodePrefixEnum::LSGMD);
    }

    public static function CreateSheet(array $params, array $publicParams): LogicResult
    {
        //公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));

        //业务参数
        $memberId   = intval(Arr::get($params, 'memberId', 0));
        $memberUid  = trim(Arr::get($params, 'memberUid', ''));
        $sellerId   = intval(Arr::get($params, 'sellerId', 0));
        $sellerUid  = trim(Arr::get($params, 'sellerUid', ''));
        $items      = Arr::get($params, 'items', []);
        $totalPrice = trim(Arr::get($params, 'totalPrice', '0.00'));

        //特殊参数
        //是否创建并写数据库，否则返回待写库的数据
        $isCreate = boolval(Arr::get($params, 'isCreate', true));

        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('创建零售购买单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('创建零售购买单，缺少登录用户ID必选参数', 400);
        }
        /*
        if (empty($memberId) && empty($memberUid))
        {
            return self::Fail('创建零售购买单，缺少会员ID/UID必选参数', 400);
        }
        */
        if (empty($sellerId) && empty($sellerUid))
        {
            return self::Fail('创建零售购买单，缺少销售员ID/UID必选参数', 400);
        }
        if (empty($items))
        {
            return self::Fail('创建零售购买单，缺少商品明细必选参数', 400);
        }
        if (!is_numeric($totalPrice) || bccomp($totalPrice, '0', 2) != 1)
        {
            return self::Fail('创建零售购买单，缺少总金额必选参数', 400);
        }

        if (!empty($memberUid) || !empty($memberId))
        {
            $member = MemberModel::getOneByIdOrUid(id: $memberId, uid: $memberUid, orgId: $hospitalOrgId);
            if (empty($member))
            {
                return self::Fail('会员不存在', 30002);
            }
            $memberId = $member->id;
        }

        $seller = UsersModel::getOneByIdOrUid(id: $sellerId, uid: $sellerUid);
        if (empty($seller))
        {
            return self::Fail('销售员不存在', 10100);
        }
        $sellerId = $seller->id;

        $hospitalUserRes = HospitalUserLogic::GetUserHospitalUser($sellerId, $hospitalId);
        if ($hospitalUserRes->isFail())
        {
            return $hospitalUserRes;
        }

        //验证商品明细
        $verifyItemsRes = RetailSheetHelper::GetValidSheetItems($items, $publicParams);
        if ($verifyItemsRes->isFail())
        {
            return $verifyItemsRes;
        }

        //购买单内商品种类
        $addItems = $verifyItemsRes->getData('item', []);
        if (empty($addItems))
        {
            return self::Fail('零售购买单中商品全部无效', 500601);
        }

        //购买单开具商品的基本信息
        $addItemsInfo = $verifyItemsRes->getData('itemsInfo', []);
        if (empty($addItemsInfo))
        {
            return self::Fail('零售购买单中商品全部不存在', 500601);
        }

        //严格验证购买单中不同类型商品
        $getAddRes = RetailSheetHelper::GetAddItems($addItems, $addItemsInfo, $publicParams);
        if ($getAddRes->isFail())
        {
            return $getAddRes;
        }

        //购买单信息
        $totalCalcPrice = 0;
        $itemData       = [];

        $addItemsRes    = $getAddRes->getData();
        $totalCalcPrice = numberAdd([$totalCalcPrice, $addItemsRes['totalPrice']]);
        $itemData       = array_merge($itemData, $addItemsRes['items']);

        if (bccomp($totalCalcPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('零售购买单中商品价格有变动，请刷新重试', 500692);
        }

        // 购买单主数据
        $sheetCode  = self::GenerateSheetCode();
        $insertData = [
            'sheet_code'  => $sheetCode,
            'org_id'      => $hospitalOrgId,
            'brand_id'    => $hospitalBrandId,
            'hospital_id' => $hospitalId,
            'member_id'   => $memberId,
            'price'       => $totalPrice,
            'status'      => 1,
            'created_by'  => $userId,
            'sold_by'     => $sellerId,
            'order_time'  => getCurrentTimeWithMilliseconds(),
        ];

        //按照原始顺序排列数据
        $insertItemsData = [];
        foreach ($items as $value)
        {
            foreach ($itemData as $key => $item)
            {
                // 当前商品顺序与提交不一致
                if ($value['itemUid'] != $item['item_uid'])
                {
                    continue;
                }

                // 删除商品uid
                unset($item['item_uid']);

                $insertItemsData[] = $item;

                unset($itemData[$key]);
                break;
            }
        }

        // 非新增模式情况下，返回组装好写入的数据
        if (!$isCreate)
        {
            return self::Success([
                                     'itemsData'  => $insertItemsData,
                                     'totalPrice' => $totalPrice,
                                 ]);
        }


        $insertRes = RetailSheetModel::DoCreateSheet($insertData, $insertItemsData);
        if (empty($insertRes))
        {
            return self::Fail('创建零售购买单失败', 500698);
        }

        return self::Success(self::GenerateCreateSheetResult($sheetCode, $totalPrice));
    }

    public static function EditSheet(array $params, array $publicParams): LogicResult
    {
        return self::Success();
    }

    public static function DeleteSheet(array $params, array $publicParams): LogicResult
    {
        return self::Success();
    }

    public static function CheckSheetEditOrDelete(array $sheet, array $publicParams): LogicResult
    {
        return self::Success();
    }
}
