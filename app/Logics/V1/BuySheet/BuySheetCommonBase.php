<?php

namespace App\Logics\V1\BuySheet;

use App\Logics\LogicResult;

/**
 * 通用购买单基类
 *
 * 特征：
 *  无自动过期 可重复使用
 *
 */
abstract class BuySheetCommonBase extends BuySheetBase
{
    final protected const bool IS_AUTO_EXPIRED = false;

    /**
     * 编辑购买单
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    abstract public static function EditSheet(array $params, array $publicParams): LogicResult;

    /**
     * 删除购买单
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    abstract public static function DeleteSheet(array $params, array $publicParams): LogicResult;

    /**
     * 检查购买单是否可编辑或删除
     *
     * @param array $sheet
     * @param array $publicParams
     *
     * @return LogicResult
     */
    abstract public static function CheckSheetEditOrDelete(array $sheet, array $publicParams): LogicResult;
}
