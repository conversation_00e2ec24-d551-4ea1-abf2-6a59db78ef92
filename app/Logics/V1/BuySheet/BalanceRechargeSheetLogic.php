<?php

namespace App\Logics\V1\BuySheet;

use Illuminate\Support\Arr;
use App\Logics\LogicResult;
use App\Enums\BusinessCodePrefixEnum;
use App\Models\MemberModel;
use App\Models\UsersModel;
use App\Models\BalanceRechargeSheetModel;
use App\Logics\V1\HospitalUserLogic;
use App\Logics\V1\BalanceRechargeActivityLogic;

/**
 * 会员余额充值购买单
 */
class BalanceRechargeSheetLogic extends BuySheetExpiredBase
{
    /**
     * 余额购买单自动过期时间（秒）
     *
     * @var int
     */
    protected const int AUTO_EXPIRED_AFTER_SECONDS = 3600;

    public static function GenerateSheetCode(): string
    {
        return generateBusinessCodeNumber(BusinessCodePrefixEnum::CZGMD);
    }

    public static function CreateSheet(array $params, array $publicParams): LogicResult
    {
        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        // 业务参数
        $memberUid  = trim(Arr::get($params, 'memberUid', ''));
        $memberId   = intval(Arr::get($params, 'memberId', 0));
        $sellerUid  = trim(Arr::get($params, 'sellerUid', ''));
        $sellerId   = intval(Arr::get($params, 'sellerId', 0));
        $items      = Arr::get($params, 'items', []);
        $totalPrice = trim(Arr::get($params, 'totalPrice', '0.00'));

        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('创建充值购买单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('创建充值购买单，缺少登录用户ID必选参数', 400);
        }
        if (empty($memberUid) && empty($memberId))
        {
            return self::Fail('创建充值购买单，缺少会员UID/ID必选参数', 400);
        }
        if (empty($sellerUid) || empty($sellerId))
        {
            return self::Fail('创建充值购买单，缺少销售员UID/ID必选参数', 400);
        }
        if (empty($items) || !isset($items[0]))
        {
            return self::Fail('创建充值购买单，缺少充值明细必选参数', 400);
        }
        if (count($items) > 1)
        {
            return self::Fail('创建充值购买单，充值明细只能有一条', 400);
        }
        if (!is_numeric($totalPrice) || bccomp($totalPrice, '0', 2) != 1)
        {
            return self::Fail('创建充值购买单，缺少总金额必选参数', 400);
        }

        $member = MemberModel::getOneByIdOrUid(id: $memberId, uid: $memberUid, orgId: $hospitalOrgId);
        $seller = UsersModel::getOneByIdOrUid(id: $sellerId, uid: $sellerUid);
        if (empty($member))
        {
            return self::Fail('会员不存在', 30001);
        }
        if (empty($seller))
        {
            return self::Fail('销售员不存在', 10100);
        }

        $memberId = $member->id;
        $sellerId = $seller->id;

        $hospitalUserRes = HospitalUserLogic::GetUserHospitalUser($sellerId, $hospitalId);
        if ($hospitalUserRes->isFail())
        {
            return $hospitalUserRes;
        }

        // 充值明细：仅支持单活动充值多次
        $rechargeItem    = $items[0];
        $activityUid     = trim(Arr::get($rechargeItem, 'activityUid', ''));
        $activityId      = intval(Arr::get($rechargeItem, 'activityId', 0));
        $quantity        = intval(Arr::get($rechargeItem, 'quantity', 0));
        $balanceRecharge = trim(Arr::get($rechargeItem, 'balanceRecharge', '0.00'));
        $balanceGift     = trim(Arr::get($rechargeItem, 'balanceGift', '0.00'));
        if (empty($activityUid) && empty($activityId))
        {
            return self::Fail('创建充值购买单，缺少活动UID/Id必选参数', 500000);
        }
        if ($quantity <= 0)
        {
            return self::Fail('创建充值购买单，数量必须大于0', 500001);
        }

        // 验证充值活动
        $rechargeActivityRes = BalanceRechargeActivityLogic::VerifyRechargeActivity($hospitalOrgId,
                                                                                    $hospitalId,
                                                                                    $activityId,
                                                                                    $activityUid,
                                                                                    $memberId,
                                                                                    $quantity);
        if ($rechargeActivityRes->isFail())
        {
            return $rechargeActivityRes;
        }

        $activityId = $rechargeActivityRes->getData('id');

        // 验证金额
        if (bccomp($balanceRecharge, $rechargeActivityRes->getData('balanceRecharge'), 2) != 0)
        {
            return self::Fail('创建充值购买单，充值金额不正确', 500030);
        }
        if (bccomp($balanceGift, $rechargeActivityRes->getData('balanceGift', 2)) != 0)
        {
            return self::Fail('创建充值购买单，赠送金额不正确', 500030);
        }
        if (bccomp($totalPrice, numberMul([$quantity, $balanceRecharge]), 2) != 0)
        {
            return self::Fail('创建充值购买单，总金额不正确', 500030);
        }


        $sheetCode = self::GenerateSheetCode();
        // 构造购买单数据
        $sheetData = [
            'sheet_code'  => $sheetCode,
            'org_id'      => $hospitalOrgId,
            'brand_id'    => $hospitalBrandId,
            'hospital_id' => $hospitalId,
            'member_id'   => $memberId,
            'price'       => $totalPrice,
            'status'      => 1,
            'created_by'  => $userId,
            'sold_by'     => $sellerId,
            'order_time'  => getCurrentTimeWithMilliseconds(),
        ];

        // 处理自动过期字段
        if (self::IS_AUTO_EXPIRED && self::AUTO_EXPIRED_AFTER_SECONDS > 0)
        {
            $sheetData[self::AUTO_EXPIRED_FIELD] = self::CalculateExpiredAt();
        }

        $sheetItemData = [
            [
                'uid'              => generateUUID(),
                'sheet_code'       => $sheetCode,
                'sheet_id'         => 0,
                'hospital_id'      => $hospitalId,
                'member_id'        => $memberId,
                'activity_id'      => $activityId,
                'quantity'         => $quantity,
                'balance_recharge' => $balanceRecharge,
                'balance_gift'     => $balanceGift,
                'price'            => numberMul([$quantity, $balanceRecharge]),//因为只能同时购买一种套餐，所以总价=数量*单价
                'status'           => 1,
                'created_by'       => $userId,
            ]
        ];

        $sheetId = BalanceRechargeSheetModel::DoCreateSheet($sheetData, $sheetItemData);
        if (empty($sheetId))
        {
            return self::Fail('创建充值购买单失败', 500040);
        }

        return self::Success(self::GenerateCreateSheetResult($sheetCode, $totalPrice));
    }
}
