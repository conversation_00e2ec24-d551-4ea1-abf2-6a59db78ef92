<?php

namespace App\Logics\V1\BuySheet;

/**
 * 自动过期购买单基类
 *
 * 特征：
 *  有自动过期 不能重复使用
 *
 */
abstract class BuySheetExpiredBase extends BuySheetBase
{
    /**
     * 购买单是否有自动过期字段
     *
     * @var bool
     */
    final protected const bool IS_AUTO_EXPIRED = true;

    /**
     * 自动过期字段名称
     *
     * @var string
     */
    protected const string AUTO_EXPIRED_FIELD = 'expired_at';

    /**
     * 自动过期时间（秒）
     *
     * @var int
     */
    protected const int AUTO_EXPIRED_AFTER_SECONDS = 86400;

    /**
     * 计算过期时间
     *
     * @return string
     */
    public static function CalculateExpiredAt(): string
    {
        // 判断子类是否有自己定义的常量
        $seconds = defined(static::class . '::AUTO_EXPIRED_AFTER_SECONDS')
            ? constant(static::class . '::AUTO_EXPIRED_AFTER_SECONDS')
            : self::AUTO_EXPIRED_AFTER_SECONDS;

        return date('Y-m-d H:i:s', time() + $seconds);
    }
}
