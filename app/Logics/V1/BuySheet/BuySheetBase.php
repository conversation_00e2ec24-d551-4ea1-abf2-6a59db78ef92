<?php

namespace App\Logics\V1\BuySheet;

use App\Logics\Logic;
use App\Logics\LogicResult;

/**
 * 购买单基类
 */
abstract class BuySheetBase extends Logic
{
    /**
     * 购买单是否有自动过期字段
     *
     * @var bool
     */
    protected const bool IS_AUTO_EXPIRED = false;

    /**
     * 自动过期字段名称
     *
     * @var string
     */
    protected const string AUTO_EXPIRED_FIELD = 'expired_at';

    /**
     * 自动过期时间（秒）
     *
     * @var int
     */
    protected const int AUTO_EXPIRED_AFTER_SECONDS = 0;

    /**
     * 生成购买单编码
     *
     * @return string
     */
    abstract public static function GenerateSheetCode(): string;

    /**
     * 生成创建购买单返回结果
     *
     * CreateSheet应该使用此方法构建结果
     *
     * @param string $sheetCode
     * @param string $totalPrice
     *
     * @return string[]
     */
    public static function GenerateCreateSheetResult(string $sheetCode, string $totalPrice): array
    {
        return [
            'sheetCode'  => $sheetCode,
            'totalPrice' => $totalPrice,
        ];
    }

    /**
     * 创建购买单
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    abstract public static function CreateSheet(array $params, array $publicParams): LogicResult;
}
