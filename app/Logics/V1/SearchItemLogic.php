<?php

namespace App\Logics\V1;

use Arr;
use App\Enums\ItemSaleTyeEnum;
use App\Support\Item\ItemHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\ItemSalePriceModel;
use App\Models\ItemSaleTypeModel;

/**
 * meiLiSearch搜索商品部分业务
 * Class SearchItemLogic
 * @package App\Logics\V1
 */
class SearchItemLogic extends Logic
{
    /**
     * 格式化检索的商品信息，如果包含组合的话，会生成组合嵌套结构体
     *
     * @param array $itemLists
     * @param array $publicParams
     * @param bool  $withItemPrice
     * @param bool  $withItemStock
     *
     * @return LogicResult
     */
    public static function FormatItemInfoStructure(array $itemLists, array $publicParams, bool $withItemPrice = false, bool $withItemStock = false): LogicResult
    {
        if (empty($itemLists))
        {
            return self::Success();
        }
        if (empty($publicParams))
        {
            return self::Fail('格式化商品信息，公共参数不存在', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('格式化商品信息，公共参数不存在', 400);
        }

        // 获取所有商品类型
        $getItemSaleTypeRes = ItemSaleTypeModel::getAllSaleType();
        $getItemSaleTypeRes = $getItemSaleTypeRes->isNotEmpty() ? $getItemSaleTypeRes->toArray() : [];

        // 获取商品价格
        if (!empty($withItemPrice))
        {
            // 获取医院信息
            $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
            if ($getHospitalRes->isFail())
            {
                return $getHospitalRes;
            }

            // 医院所属城市
            $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
            $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

            $itemTypeRelationItemIds = [];
            foreach ($itemLists as $item)
            {
                $itemTypeRelationItemIds[$item['item_type']][] = $item['id'];
            }

            // 获取商品价格
            $getItemsPriceRes = [];
            foreach ($itemTypeRelationItemIds as $itemType => $itemIds)
            {
                $getItemsPriceRes[$itemType] = ItemSalePriceModel::getItemSalePrice($itemType, $itemIds, $orgId, $brandId, $hospitalProvinceId, $hospitalCityId, $hospitalId);
            }
        }

        // TODO 获取商品库存
        if (!empty($withItemStock))
        {
        }

        $returnSearchItems = [];
        foreach ($itemLists as $itemInfo)
        {
            // 根据当前商品类型获取价格信息
            $curItemType          = $itemInfo['item_type'] ?? 0;
            $curItemSalePriceRes  = $getItemsPriceRes[$curItemType][$itemInfo['id'] ?? 0] ?? [];
            $curItemSalePriceInfo = $curItemSalePriceRes['sale_price'] ?? [];

            // 商品是否组合
            $curItemIsSuit           = $itemInfo['is_suit'] ?? 0;
            $curItemFirstSaleTypeId  = $itemInfo['first_sale_type']['id'] ?? 0;
            $curItemSecondSaleTypeId = !empty($itemInfo['second_sale_type']) ? ($itemInfo['second_sale_type']['id'] ?? 0) : 0;

            // 商品展示名称
            $curItemDisplayName = ItemHelper::ItemDisplayName($itemInfo);

            // 商品类型，如果是组合不在细分是药品、化验等。
            if ($curItemIsSuit)
            {
                $curItemTypeInfo = ['id' => 0, 'name' => '组合'];
            }
            // 将商品类型单独提取显示
            elseif ($curItemSecondSaleTypeId == ItemSaleTyeEnum::SecondItem->value)
            {
                $curItemTypeInfo = [
                    'id'   => $curItemFirstSaleTypeId,
                    'name' => $getItemSaleTypeRes[$curItemSecondSaleTypeId]['alias_name'] ?? ''
                ];
            }
            else
            {
                $curItemTypeInfo = [
                    'id'   => $curItemFirstSaleTypeId,
                    'name' => $getItemSaleTypeRes[$curItemFirstSaleTypeId]['alias_name'] ?? ''
                ];
            }

            // 商品规格信息
            $curItemPackUnitName = $itemInfo['pack_unit']['name'] ?? '';
            $curItemBulkUnitName = $itemInfo['bulk_unit']['name'] ?? '';
            $curItemUseUnitName  = $itemInfo['use_unit']['name'] ?? '';
            $curItemUseRatio     = $itemInfo['use_ratio'] ?? 0;
            $curItemBulkRatio    = $itemInfo['bulk_ratio'] ?? 0;

            // 如果没有整装单位、散装单位使用计量单位
            if (empty($curItemBulkUnitName))
            {
                $curItemBulkUnitName = $curItemUseUnitName;
            }
            if (empty($curItemPackUnitName))
            {
                $curItemPackUnitName = $curItemUseUnitName;
            }

            // TODO 是否只有一级销售类型是商品药品出库单位需要？？
            // 商品出库单位，1ml/ml、1次、1瓶。（计量比 + 计量单位 + 散装单位 ）
            if ($curItemFirstSaleTypeId == ItemSaleTyeEnum::FirstDrug->value)
            {
                $curItemSpec = formatDisplayNumber($curItemUseRatio) . $curItemUseUnitName . '/' . $curItemBulkUnitName;
            }
            else
            {
                $curItemSpec = 1 . $curItemUseUnitName;
            }

            // 商品是否处方精确计量品、可否整装开具
            $curItemIsPreciseMetering = $itemInfo['is_precise_metering'] ?? false;
            $curItemIsPackSaleAllow   = $itemInfo['is_pack_sale_allow'] ?? false;

            // 商品价格
            $curItemPackSalePrice = $curItemSalePriceInfo['pack_sale_price'] ?? 0;
            $curItemBulkSalePrice = $curItemSalePriceInfo['bulk_sale_price'] ?? 0;

            $tmpItemStructure = [
                'uid'               => $itemInfo['uid'],
                'isSuit'            => $curItemIsSuit,
                'type'              => $curItemTypeInfo,
                'name'              => $curItemDisplayName,
                'itemName'          => $itemInfo['name'],
                'itemBarcode'       => $itemInfo['barcode'],
                'useUnit'           => $curItemUseUnitName,
                'useRatio'          => $curItemUseRatio,
                'spec'              => $curItemSpec,
                'packUnit'          => $curItemPackUnitName,
                'packPrice'         => formatDisplayNumber($curItemPackSalePrice),
                'bulkUnit'          => $curItemBulkUnitName,
                'bulkPrice'         => formatDisplayNumber($curItemBulkSalePrice),
                'bulkRatio'         => $curItemBulkRatio,
                'unitType'          => $itemInfo['unit_type'],
                'quantity'          => $itemInfo['quantity'],
                'isPreciseMetering' => $curItemIsPreciseMetering,
                'isPackSaleAllow'   => $curItemIsPackSaleAllow,
                'stock'             => [],
                'suitItems'         => [],
            ];

            // 处理组合商品中的子项目
            if ($curItemIsSuit && !empty($itemInfo['suit_items']))
            {
                $tmpItemStructure['suitItems'] = self::FormatItemInfoStructure($itemInfo['suit_items'], $publicParams)
                                                     ->getData();
            }
            $returnSearchItems[] = $tmpItemStructure;
        }

        return self::Success($returnSearchItems);
    }
}
