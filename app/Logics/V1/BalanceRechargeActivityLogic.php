<?php

namespace App\Logics\V1;

use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\BalanceRechargeActivitiesModel;
use App\Models\BalanceRechargeActivitiesIncludeCityModel;
use App\Models\BalanceRechargeActivitiesIncludeHospitalModel;
use App\Models\BalanceRechargeOrderModel;

class BalanceRechargeActivityLogic extends Logic
{
    /**
     * 获取当前医院当前客户可用的充值活动
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetRechargeActivities(array $params, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('查找充值活动列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $memberUid       = trim(Arr::get($params, 'memberUid', ''));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('查找充值活动列表，缺少医院必选参数', 400);
        }
        if (empty($memberUid))
        {
            return self::Fail('查找充值活动列表，缺少会员必选参数', 400);
        }

        //判断会员是否存在
        $memberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
        if ($memberRes->isFail())
        {
            return $memberRes;
        }

        $memberId = $memberRes->getData('id', 0);
        if (empty($memberId))
        {
            return self::Fail('会员不存在', 30001);
        }

        //判断医院是否存在
        $hospitalRes = HospitalLogic::GetHospitalBaseInfo(hospitalId: $hospitalId);
        if ($hospitalRes->isFail())
        {
            return $hospitalRes;
        }

        $hospitalCityId = $hospitalRes->getData('addressInfo.cityId', 0);

        //获取医院可用的充值活动
        $activities = BalanceRechargeActivitiesModel::GetRechargeActivities($hospitalOrgId,
                                                                            $hospitalCityId,
                                                                            $hospitalId);
        if ($activities->isEmpty())
        {
            return self::Success([]);
        }

        //获取有总次数限制的活动ID
        $totalLimitActivitiesIds = $activities->where('total_times', '>', 0)
                                              ->pluck('id')
                                              ->toArray();
        //获取有单客户参加次数限制的活动ID
        $memberLimitActivitiesIds = $activities->where('member_times', '>', 0)
                                               ->pluck('id')
                                               ->toArray();

        $hospitalActivitiesUsedCount = [];
        $memberActivitiesUsedCount   = [];
        if (!empty($totalLimitActivitiesIds))
        {
            //获取这些充值活动在医院中已经使用的次数
            $hospitalActivitiesUsedCount = BalanceRechargeOrderModel::GetHospitalRechargeActivitiesUsedCount($hospitalId,
                                                                                                             $totalLimitActivitiesIds);
        }
        if (!empty($memberLimitActivitiesIds))
        {
            //获取这些充值活动在医院中会员已经使用的次数【在本组织下所有医院参加的次数】
            $memberActivitiesUsedCount = BalanceRechargeOrderModel::GetMemberRechargeActivitiesUsedCount($memberId,
                                                                                                         $memberLimitActivitiesIds);
        }

        // 如果两个活动的balance_recharge金额相同，优先展示start_time和end_time都不为null的
        // 按 balance_recharge 正序排列
        $filteredActivities = $activities->groupBy('balance_recharge')
                                         ->flatMap(function ($group) {
                                             // 找出时间不为 null 的
                                             $withTime = $group->filter(function ($item) {
                                                 return !empty($item->start_time) && !empty($item->end_time);
                                             });

                                             // 如果存在时间不为 null 的记录，就只保留这些
                                             if ($withTime->isNotEmpty())
                                             {
                                                 return $withTime;
                                             }

                                             // 否则保留全部
                                             return $group;
                                         })
                                         ->sortBy('balance_recharge')
                                         ->values(); // 重建索引


        //循环过滤并生成结果
        $result = [];
        foreach ($filteredActivities as $activity)
        {
            $activityId = $activity->id;
            $temp       = [
                'uid'             => $activity->uid,
                'title'           => $activity->title,
                'desc'            => $activity->desc,
                'startTime'       => $activity->start_time,
                'endTime'         => $activity->end_time,
                'balanceRecharge' => $activity->balance_recharge,
                'balanceGift'     => $activity->balance_gift,
                'createTime'      => $activity->created_at,
                'isDisabled'      => false,
                'disabledReason'  => '',
            ];
            if ($activity->total_times > 0 && isset($hospitalActivitiesUsedCount[$activityId]) && $hospitalActivitiesUsedCount[$activityId] >= $activity->total_times)
            {
                $temp['isDisabled']     = true;
                $temp['disabledReason'] = '活动总次数已用完';
            }
            if ($activity->member_times > 0 && isset($memberActivitiesUsedCount[$activityId]) && $memberActivitiesUsedCount[$activityId] >= $activity->member_times)
            {
                $temp['isDisabled']     = true;
                $temp['disabledReason'] = '活动个人次数已用完';
            }

            $result[] = $temp;
        }

        return self::Success($result);
    }

    /**
     * 验证在某个医院所选择的充值活动是可用(可同时验证用户下的可用)
     *
     * 活动存在
     * 活动规则符合
     * 次数未用完
     *
     * @param int      $orgId
     * @param int      $hospitalId
     * @param int      $activityId
     * @param string   $activityUid
     * @param int|null $memberId
     * @param int|null $quantity
     * @param bool     $withFullInfo
     *
     * @return LogicResult
     */
    public static function VerifyRechargeActivity(
        int  $orgId, int $hospitalId, int $activityId = 0, string $activityUid = '', ?int $memberId = null,
        ?int $quantity = null,
        bool $withFullInfo = true
    ): LogicResult
    {
        if (empty($hospitalId) || (empty($activityId) && empty($activityUid)))
        {
            return self::Fail('验证充值活动，缺少必选参数', 400);
        }

        //验证活动是否存在
        $activity = BalanceRechargeActivitiesModel::getOneByIdOrUid(id: $activityId, uid: $activityUid, orgId: $orgId);
        if (empty($activity) || $activity->status != 1)
        {
            return self::Fail('充值活动不存在', 500010);
        }

        $activityId = $activity->id;

        //验证活动时间
        if (!empty($activity->start_time) && strtotime($activity->start_time) > time())
        {
            return self::Fail('充值活动未开始', 500011);
        }
        if (!empty($activity->end_time) && strtotime($activity->end_time) < time())
        {
            return self::Fail('充值活动已结束', 500012);
        }

        //验证活动地区和医院限制
        if ($activity->limit_type != 1)
        {
            //验证活动是否在当前医院可用
            $hospitalRes = HospitalLogic::GetHospitalBaseInfo(hospitalId: $hospitalId, withId: true);
            if ($hospitalRes->isFail())
            {
                return $hospitalRes;
            }

            $hospitalOrgId  = $hospitalRes->getData('orgId', 0);
            $hospitalCityId = $hospitalRes->getData('addressInfo.cityId', 0);

            if ($activity->limit_type == 2)
            {
                $activityIncludeCityExists = BalanceRechargeActivitiesIncludeCityModel::isExistsByWhere([
                                                                                                            'org_id'      => $hospitalOrgId,
                                                                                                            'activity_id' => $activityId,
                                                                                                            'city_id'     => $hospitalCityId,
                                                                                                        ]);
                if (!$activityIncludeCityExists)
                {
                    return self::Fail('充值活动在当前城市不可用', 500013);
                }
            }
            elseif ($activity->limit_type == 3)
            {
                $activityIncludeHospitalExists = BalanceRechargeActivitiesIncludeHospitalModel::isExistsByWhere([
                                                                                                                    'org_id'      => $hospitalOrgId,
                                                                                                                    'activity_id' => $activityId,
                                                                                                                    'hospital_id' => $hospitalId,
                                                                                                                ]);
                if (!$activityIncludeHospitalExists)
                {
                    return self::Fail('充值活动在当前医院不可用', 500014);
                }
            }

        }

        //验证活动总次数
        if ($activity->total_times > 0)
        {
            $hospitalActivitiesUsedCount = BalanceRechargeOrderModel::GetHospitalRechargeActivitiesUsedCount($hospitalId,
                                                                                                             [$activityId]);
            if (isset($hospitalActivitiesUsedCount[$activityId]) && $hospitalActivitiesUsedCount[$activityId] >= $activity->total_times)
            {
                return self::Fail('充值活动总次数已用完', 500015);
            }

            if ($quantity > 0)
            {
                if (isset($hospitalActivitiesUsedCount[$activityId]) && $hospitalActivitiesUsedCount[$activityId] + $quantity > $activity->total_times)
                {
                    return self::Fail('充值活动总剩余次数不足', 500017);
                }
            }

        }
        //验证会员参加次数
        if ($activity->member_times > 0 && !empty($memberId))
        {
            $memberActivitiesUsedCount = BalanceRechargeOrderModel::GetMemberRechargeActivitiesUsedCount($memberId,
                                                                                                         [$activityId]);
            if (isset($memberActivitiesUsedCount[$activityId]) && $memberActivitiesUsedCount[$activityId] >= $activity->member_times)
            {
                return self::Fail('充值活动个人次数已用完', 500016);
            }

            if ($quantity > 0)
            {
                if (isset($memberActivitiesUsedCount[$activityId]) && $memberActivitiesUsedCount[$activityId] + $quantity > $activity->member_times)
                {
                    return self::Fail('充值活动个人剩余次数不足', 500018);
                }
            }
        }


        $temp = [
            'uid'             => $activity->uid,
            'title'           => $activity->title,
            'desc'            => $activity->desc,
            'startTime'       => $activity->start_time,
            'endTime'         => $activity->end_time,
            'balanceRecharge' => $activity->balance_recharge,
            'balanceGift'     => $activity->balance_gift,
            'createTime'      => $activity->created_at,
            'isDisabled'      => false,
            'disabledReason'  => '',
        ];

        if ($withFullInfo)
        {
            $temp = array_merge($temp, [
                'id'          => $activity->id,
                'orgId'       => $activity->org_id,
                'totalTimes'  => $activity->total_times,
                'memberTimes' => $activity->member_times,
                'limitType'   => $activity->limit_type,
            ]);
        }


        return self::Success($temp);
    }
}
