<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\PetCategoryDictModel;

class PetCategoryLogic extends Logic
{
    /**
     * 获取宠物分类
     * @return LogicResult
     */
    public static function GetPetCategoryOptions(): LogicResult
    {
        $getPetCategoryRes = PetCategoryDictModel::getData(where: ['status' => 1], orderBys: ['order_by' => 'asc']);

        $petCategoryInfos = [];
        foreach ($getPetCategoryRes as $curInfo)
        {
            $petCategoryInfos[] = [
                'id'      => $curInfo['id'],
                'name'    => $curInfo['name'],
                'isOther' => $curInfo['is_other'],
            ];
        }

        return self::Success(['petCategoryOptions' => $petCategoryInfos]);
    }
}
