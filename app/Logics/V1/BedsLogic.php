<?php

namespace App\Logics\V1;

use Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RoomsModel;
use App\Models\RoomsBedsModel;

class BedsLogic extends Logic
{
    /**
     * 获取病房床位信息
     *
     * @param int $roomId
     *
     * @return LogicResult
     */
    public static function GetRoomBedsOptions(int $roomId, array $publicParams): LogicResult
    {
        if (empty($roomId))
        {
            return self::Fail('获取病房床位信息，缺少病房ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取病房床位信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取病房床位信息，缺少医院ID必选参数', 400);
        }

        // 病房是否存在
        $getRoomRes = RoomsModel::getData(where: ['id' => $roomId, 'hospital_id' => $hospitalId, 'status' => 1]);
        if (empty($getRoomRes))
        {
            return self::Fail('病房不存在', 37000);
        }

        // 获取病房床位信息
        $getBedsRes = RoomsBedsModel::getData(where: ['room_id' => $roomId, 'status' => 1]);
        if (empty($getBedsRes))
        {
            return self::Success(['roomBedsOptions' => []]);
        }

        $returnBedsInfo = [];
        foreach ($getBedsRes as $curInfo)
        {
            $returnBedsInfo[] = [
                'id'       => $curInfo['id'],
                'name'     => $curInfo['name'],
                'sizeType' => $curInfo['size_type'],
                'disable'  => (bool) $curInfo['used'],
            ];
        }

        return self::Success(['roomBedsOptions' => $returnBedsInfo]);
    }
}
