<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\PetLiveStatusDictModel;

class PetLiveStatusLogic extends Logic
{
    /**
     * 获取宠物生存状态
     * @return LogicResult
     */
    public static function GetPetLiveStatusOptions(): LogicResult
    {
        $getPetLiveStatusRes = PetLiveStatusDictModel::getData(where: ['status' => 1]);

        $petLiveStatusInfos = [];
        foreach ($getPetLiveStatusRes as $curInfo)
        {
            $petLiveStatusInfos[] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['petLiveStatusOptions' => $petLiveStatusInfos]);
    }
}
