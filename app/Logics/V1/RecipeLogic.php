<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use App\Facades\SearchFacade;
use App\Enums\BusinessCodePrefixEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Support\Recipe\RecipeHelper;
use App\Support\Concurrent\ConcurrentTask;
use App\Models\TestModel;
use App\Models\NurseModel;
use App\Models\RecipeModel;
use App\Models\ImagesModel;
use App\Models\RecipeItemModel;
use App\Models\HospitalUserModel;
use App\Models\RecipeTemplateModel;
use App\Models\RecipeSnapshotModel;
use App\Models\RecipesAssistantModel;
use App\Models\RecipeTemplatesUseLogModel;

class RecipeLogic extends Logic
{
    /**
     * 获取搜索商品
     *
     * @param string $keyword
     * @param int    $petId
     * @param array  $publicParams
     *
     * @return LogicResult
     */
    public static function GetSearchItems(string $keyword, int $petId, array $publicParams): LogicResult
    {
        if (empty($keyword) || empty($petId))
        {
            return self::Fail('keyword、petId，缺少必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('orgId、brandId、hospitalId，缺少必选参数', 400);
        }

        // 获取宠物信息
        $getPetRes = PetLogic::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 搜索关键词
        $keyword = trimWhitespace($keyword);

        // 搜索商品
        $getSearchItemsRes = SearchFacade::searchRecipeItems(keywords             : $keyword,
                                                             purchaseHospitalId   : $hospitalId,
                                                             supportPetCategoryIds: [$getPetRes->getData('category_id')],
                                                             orgId                : $orgId);
        if (empty($getSearchItemsRes))
        {
            return self::Success(['data' => []]);
        }

        // 过滤搜索商品
        $getFilterSearchItemsRes = self::FilterSearchItems($getSearchItemsRes);
        if ($getFilterSearchItemsRes->isFail())
        {
            return $getFilterSearchItemsRes;
        }

        // 构建商品信息结构
        $searchItems      = $getFilterSearchItemsRes->getData();
        $getFormatItemRes = SearchItemLogic::FormatItemInfoStructure($searchItems, $publicParams, true, true);
        if ($getFormatItemRes->isFail())
        {
            return $getFormatItemRes;
        }

        // TODO 标记商品在不满足的情况下禁用添加到处方
        $getFormatItemRes = $getFormatItemRes->getData();
        foreach ($getFormatItemRes as &$itemInfo)
        {
            // 是否禁用添加处方，true:禁用，不可添加 false:不禁用，可添加
            $itemInfo['disabled'] = false;

            // 价格无效
            if ($itemInfo['packPrice'] <= 0 || $itemInfo['bulkPrice'] <= 0)
            {
                $itemInfo['disabled'] = true;
            }
        }

        return self::Success(['data' => $getFormatItemRes]);
    }

    /**
     * 添加处方
     *
     * @param int   $caseId
     * @param array $addRecipeParams
     * @param array $publicParams
     * @param bool  $isCreate 是否创建处方，true:验证通过后创建 false:验证通过后返回组装好的相关数据
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddRecipe(int $caseId, array $addRecipeParams, array $publicParams, bool $isCreate = true): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('添加处方，缺少病历ID必选参数', 400);
        }
        if (empty($addRecipeParams))
        {
            return self::Fail('添加处方，缺少处方商品必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($orgId) || empty($brandId))
        {
            return self::Fail('hospitalId、orgId、brandId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 业务参数
        $recipeItemsParams = $addRecipeParams['items'];
        $recipeTemplateId  = $addRecipeParams['recipeTemplateId'] ?? 0;

        // 获取病历信息
        $getCaseRes = CaseLogic::GetUnderWayCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 病历信息
        $caseInfo = $getCaseRes->getData();

        // TODO 是否有医生角色
        $getTransferDoctorRoleRes = HospitalUserModel::getHospitalUsers($hospitalId, [$doctorId]);
        if ($getTransferDoctorRoleRes->isEmpty())
        {
            return self::Fail('转诊接诊医生不存在', 35003);
        }

        // 如果使用了处方模版，验证模版是否可用
        if (!empty($recipeTemplateId))
        {
            $getWhere       = [
                'orgId'      => $orgId,
                'hospitalId' => $hospitalId,
                'doctorId'   => $doctorId,
                'templateId' => $recipeTemplateId
            ];
            $getTemplateRes = RecipeTemplateModel::getTemplateList($getWhere);
            if (empty($getTemplateRes))
            {
                return self::Fail('使用的处方模版不存在或已失效', 39100);
            }
        }

        // 获取基本验证通过的处方商品
        $getValidRecipeItemsRes = RecipeHelper::GetValidRecipeItems($caseInfo, $recipeItemsParams, $publicParams);
        if ($getValidRecipeItemsRes->isFail())
        {
            return $getValidRecipeItemsRes;
        }

        // 处方商品种类
        $addDrugItems   = $getValidRecipeItemsRes->getData('drug', []);
        $addTestItems   = $getValidRecipeItemsRes->getData('test', []);
        $addImageItems  = $getValidRecipeItemsRes->getData('image', []);
        $addNursesItems = $getValidRecipeItemsRes->getData('nurses', []);
        $addSuitItems   = $getValidRecipeItemsRes->getData('suit', []);
        if (empty($addDrugItems) && empty($addTestItems) && empty($addImageItems) && empty($addNursesItems) && empty($addSuitItems))
        {
            return self::Fail('保存处方，商品全部无效', 400);
        }

        // 处方内开具商品的基本信息
        $addRecipeItemInfo     = $getValidRecipeItemsRes->getData('itemInfo', []);
        $addRecipeSuitItemInfo = $getValidRecipeItemsRes->getData('suitItemInfo', []);
        if (empty($addRecipeItemInfo) && empty($addRecipeSuitItemInfo))
        {
            return self::Fail('保存处方，商品信息全部不存在', 400);
        }

        // 严格验证处方中不同类型商品
        $task = ConcurrentTask::new();
        if (!empty($addDrugItems))
        {
            $task->addTask('drugs',
                fn() => RecipeHelper::GetAddRecipeDrugs($caseInfo, $addDrugItems, $addRecipeItemInfo, $publicParams));
        }
        if (!empty($addTestItems))
        {
            $task->addTask('tests',
                fn() => RecipeHelper::GetAddRecipeTests($caseInfo, $addTestItems, $addRecipeItemInfo, $publicParams));
        }
        if (!empty($addImageItems))
        {
            $task->addTask('images',
                fn() => RecipeHelper::GetAddRecipeImages($caseInfo, $addImageItems, $addRecipeItemInfo, $publicParams));
        }
        if (!empty($addNursesItems))
        {
            $task->addTask('nurses',
                fn() => RecipeHelper::GetAddRecipeNurses($caseInfo, $addNursesItems, $addRecipeItemInfo, $publicParams));
        }
        if (!empty($addSuitItems))
        {
            $task->addTask('suits',
                fn() => RecipeHelper::GetAddRecipeSuits($caseInfo, $addSuitItems, $addRecipeSuitItemInfo, $publicParams));
        }

        DB::disconnect();
        $getAddRecipeRes = $task->run();
        if (empty($getAddRecipeRes))
        {
            return self::Fail('保存处方，商品信息全部不存在', 400);
        }

        // 严格验证处方中不同类型商品出现异常
        foreach ($getAddRecipeRes as $result)
        {
            if ($result->isException())
            {
                Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存处方，严格验证商品信息异常', [
                    'code'    => $result->getException()
                                        ->getCode(),
                    'message' => $result->getException()
                                        ->getMessage(),
                    'file'    => $result->getException()
                                        ->getFile(),
                    'line'    => $result->getException()
                                        ->getLine(),
                    'trace'   => $result->getException()
                                        ->getTraceAsString(),
                ]);

                return self::Fail('保存处方，商品信息错误', 400);
            }
        }

        // 处方信息
        $recipeTotalPrice = 0;
        $recipeItemData   = [];

        // 处方内化验、影像、处置任务信息
        $recipeTestTaskData   = [];
        $recipeImageTaskData  = [];
        $recipeNursesTaskData = [];

        $addDrugItemsRes   = isset($getAddRecipeRes['drugs']) ? $getAddRecipeRes['drugs']->getData() : [];
        $addTestItemsRes   = isset($getAddRecipeRes['tests']) ? $getAddRecipeRes['tests']->getData() : [];
        $addImageItemsRes  = isset($getAddRecipeRes['images']) ? $getAddRecipeRes['images']->getData() : [];
        $addNursesItemsRes = isset($getAddRecipeRes['nurses']) ? $getAddRecipeRes['nurses']->getData() : [];
        $addSuitItemsRes   = isset($getAddRecipeRes['suits']) ? $getAddRecipeRes['suits']->getData() : [];
        if ($addDrugItemsRes instanceof LogicResult)
        {
            if ($addDrugItemsRes->isFail())
            {
                return $addDrugItemsRes;
            }

            $recipeTotalPrice = numberAdd([$recipeTotalPrice, $addDrugItemsRes->getData('totalPrice', 0)]);
            $recipeItemData   = array_merge($recipeItemData, $addDrugItemsRes->getData('drugItems', []));
        }

        if ($addTestItemsRes instanceof LogicResult)
        {
            if ($addTestItemsRes->isFail())
            {
                return $addTestItemsRes;
            }

            $recipeTotalPrice   = numberAdd([$recipeTotalPrice, $addTestItemsRes->getData('totalPrice', 0)]);
            $recipeItemData     = array_merge($recipeItemData, $addTestItemsRes->getData('testItems', []));
            $recipeTestTaskData = array_merge($recipeTestTaskData, $addTestItemsRes->getData('testTaskItems', []));
        }

        if ($addImageItemsRes instanceof LogicResult)
        {
            if ($addImageItemsRes->isFail())
            {
                return $addImageItemsRes;
            }

            $recipeTotalPrice    = numberAdd([$recipeTotalPrice, $addImageItemsRes->getData('totalPrice', 0)]);
            $recipeItemData      = array_merge($recipeItemData, $addImageItemsRes->getData('imageItems', []));
            $recipeImageTaskData = array_merge($recipeImageTaskData, $addImageItemsRes->getData('imageTaskItems', []));
        }

        if ($addNursesItemsRes instanceof LogicResult)
        {
            if ($addNursesItemsRes->isFail())
            {
                return $addNursesItemsRes;
            }

            $recipeTotalPrice     = numberAdd([$recipeTotalPrice, $addNursesItemsRes->getData('totalPrice', 0)]);
            $recipeItemData       = array_merge($recipeItemData, $addNursesItemsRes->getData('nursesItems', []));
            $recipeNursesTaskData = array_merge($recipeNursesTaskData,
                                                $addNursesItemsRes->getData('nursesTaskItems', []));
        }

        if ($addSuitItemsRes instanceof LogicResult)
        {
            if ($addSuitItemsRes->isFail())
            {
                return $addSuitItemsRes;
            }

            $recipeTotalPrice = numberAdd([$recipeTotalPrice, $addSuitItemsRes->getData('totalPrice', 0)]);
            $suitItemsData    = $addSuitItemsRes->getData('suitItems', []);
            foreach ($suitItemsData as $suitInfo)
            {
                if (empty($suitInfo['suitItems']))
                {
                    continue;
                }

                $recipeItemData[] = $suitInfo['suitItems'];

                // 组合内化验、影像、处置任务
                if (!empty($suitInfo['testTaskItems']))
                {
                    $recipeTestTaskData = array_merge($recipeTestTaskData, $suitInfo['testTaskItems']);
                }
                if (!empty($suitInfo['imageTaskItems']))
                {
                    $recipeImageTaskData = array_merge($recipeImageTaskData, $suitInfo['imageTaskItems']);
                }
                if (!empty($suitInfo['nursesTaskItems']))
                {
                    $recipeNursesTaskData = array_merge($recipeNursesTaskData, $suitInfo['nursesTaskItems']);
                }
            }
        }

        // 处方主数据
        $insertRecipeData = [
            'recipe_code'        => generateBusinessCodeNumber(BusinessCodePrefixEnum::MZCF),
            'case_id'            => $caseId,
            'member_id'          => $caseInfo['member_id'],
            'pet_id'             => $caseInfo['pet_id'],
            'hospital_id'        => $hospitalId,
            'brand_id'           => $brandId,
            'org_id'             => $orgId,
            'doctor_id'          => $doctorId,
            'source_type'        => $caseInfo['source_type'],
            'source_relation_id' => $caseInfo['source_relation_id'],
            'price'              => $recipeTotalPrice,
            'template_id'        => $recipeTemplateId,
        ];

        // 处方使用模版记录
        $insertRecipeTemplateLog = [];
        if (!empty($recipeTemplateId))
        {
            $insertRecipeTemplateLog = [
                'hospital_id' => $hospitalId,
                'template_id' => $recipeTemplateId,
                'recipe_id'   => 0,
                'created_by'  => $doctorId,
            ];
        }

        // 处方内商品
        $insertRecipeItemData = [];
        foreach ($recipeItemsParams as $paramItem)
        {
            foreach ($recipeItemData as $key => $itemInfo)
            {
                // 当前商品顺序与提交不一致
                if ($paramItem['itemUid'] != $itemInfo['item_uid'])
                {
                    continue;
                }

                // 删除商品uid
                unset($itemInfo['item_uid']);

                // 如果是组合，把组合拆成扁平化单项
                $curSuitItems = [];
                if (isset($itemInfo['suit_items']))
                {
                    $curSuitItems = $itemInfo['suit_items'];
                    unset($itemInfo['suit_items']);
                }

                // 当前商品
                $insertRecipeItemData[] = $itemInfo;

                // 如果是组合，组合明细
                if (!empty($curSuitItems))
                {
                    foreach ($curSuitItems as $suitItem)
                    {
                        unset($suitItem['item_uid']);
                        $insertRecipeItemData[] = $suitItem;
                    }
                }

                unset($recipeItemData[$key]);
                break;
            }
        }

        if (!empty($recipeItemData))
        {
            return self::Fail('处方内商品顺序错误', 39008);
        }

        // 非新增处方情况下，返回组装好写入的数据
        if (!$isCreate)
        {
            return self::Success([
                                     'recipeTotalPrice'     => $recipeTotalPrice,
                                     'recipeItemData'       => $insertRecipeItemData,
                                     'recipeTestTaskData'   => $recipeTestTaskData,
                                     'recipeImageTaskData'  => $recipeImageTaskData,
                                     'recipeNursesTaskData' => $recipeNursesTaskData
                                 ]);
        }

        try
        {
            DB::beginTransaction();

            // 处方主信息
            $recipeId = RecipeModel::insertOne($insertRecipeData);

            // 处方使用模版记录
            if (!empty($insertRecipeTemplateLog))
            {
                $insertRecipeTemplateLog['recipe_id'] = $recipeId;
                RecipeTemplatesUseLogModel::insertOne($insertRecipeTemplateLog);
            }

            // 处方商品
            foreach ($insertRecipeItemData as $key => $itemInfo)
            {
                $insertRecipeItemData[$key]['recipe_id'] = $recipeId;
            }

            RecipeItemModel::insert($insertRecipeItemData);

            // 处方内化验任务
            if (!empty($recipeTestTaskData))
            {
                foreach ($recipeTestTaskData as $testTaskKey => $testTaskInfo)
                {
                    $recipeTestTaskData[$testTaskKey]['recipe_id'] = $recipeId;
                }

                TestModel::insert($recipeTestTaskData);
            }

            // 处方内影像任务
            if (!empty($recipeImageTaskData))
            {
                foreach ($recipeImageTaskData as $imageTaskKey => $imageTaskInfo)
                {
                    $recipeImageTaskData[$imageTaskKey]['recipe_id'] = $recipeId;
                }

                ImagesModel::insert($recipeImageTaskData);
            }

            // 处方内处置任务
            if (!empty($recipeNursesTaskData))
            {
                foreach ($recipeNursesTaskData as $nursesTaskKey => $nursesTaskInfo)
                {
                    $recipeNursesTaskData[$nursesTaskKey]['recipe_id'] = $recipeId;
                }

                NurseModel::insert($recipeNursesTaskData);
            }

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存处方异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('处方保存失败', 39008);
        }

        return self::Success(['recipeCode' => $insertRecipeData['recipe_code']]);
    }

    /**
     * 验证处方是否可编辑
     *
     * @param int   $caseId
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function VerifyEditRecipe(int $caseId, int $recipeId, array $publicParams): LogicResult
    {
        if (empty($caseId) || empty($recipeId))
        {
            return self::Fail('验证是否可编辑处方，缺少病历ID、处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证是否可编辑处方，缺少公共参数', 400);
        }

        // 处方是否可编辑
        $checkEditRes = self::CheckRecipeEditOrDelete($recipeId, $publicParams);
        if ($checkEditRes->isFail())
        {
            return $checkEditRes;
        }

        return self::Success();
    }

    /**
     * 编辑处方
     *
     * @param int   $caseId
     * @param int   $recipeId
     * @param array $editRecipeParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditRecipe(int $caseId, int $recipeId, array $editRecipeParams, array $publicParams): LogicResult
    {
        if (empty($caseId) || empty($recipeId))
        {
            return self::Fail('caseId、recipeId，缺少必选参数', 400);
        }
        if (empty($editRecipeParams))
        {
            return self::Fail('paramsRecipeItems，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($orgId) || empty($brandId))
        {
            return self::Fail('hospitalId、orgId、brandId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 处方是否可编辑
        $checkEditRes = self::CheckRecipeEditOrDelete($recipeId, $publicParams);
        if ($checkEditRes->isFail())
        {
            return $checkEditRes;
        }

        // 获取处方编辑后扁平化数据
        $getRecipeItemDataRes = self::AddRecipe($caseId, ['items' => $editRecipeParams], $publicParams, false);
        if ($getRecipeItemDataRes->isFail())
        {
            return $getRecipeItemDataRes;
        }

        $recipeTotalPrice     = $getRecipeItemDataRes->getData('recipeTotalPrice', 0);
        $recipeItemData       = $getRecipeItemDataRes->getData('recipeItemData', []);
        $recipeTestTaskData   = $getRecipeItemDataRes->getData('recipeTestTaskData', []);
        $recipeImageTaskData  = $getRecipeItemDataRes->getData('recipeImageTaskData', []);
        $recipeNursesTaskData = $getRecipeItemDataRes->getData('recipeNursesTaskData', []);
        if (empty($recipeItemData))
        {
            return self::Fail('编辑商品无效，编辑失败', 39008);
        }

        // 获取处方旧的明细
        $selectField         = [
            'uid',
            'hospital_id',
            'member_id',
            'pet_id',
            'item_suit_id',
            'item_sale_type',
            'item_id',
            'unit_type',
            'price',
            'once_use',
            'times',
            'days',
            'quantity',
            'usage_id',
            'usage_name',
            'remark',
            'use_unit',
            'group',
            'suit_group',
            'is_suit',
            'suit_unique_uid'
        ];
        $getOldRecipeItemRes = RecipeItemModel::getData($selectField, ['recipe_id' => $recipeId, 'status' => 1]);
        if (empty($getOldRecipeItemRes))
        {
            return self::Fail('编辑的处方无有效商品，编辑失败', 39009);
        }

        // 获取旧的处方关联的化验任务
        $getOldTestTasks = TestModel::getData(where   : ['recipe_id' => $recipeId, 'status' => 1],
                                              orderBys: ['result_status' => 'desc', 'start_status' => 'desc', 'id' => 'asc']);

        // 获取旧的处方关联的影像任务
        $getOldImageTasks = ImagesModel::getData(where   : ['recipe_id' => $recipeId, 'status' => 1],
                                                 orderBys: ['result_status' => 'desc', 'start_status' => 'desc', 'id' => 'asc']);

        // 获取旧的处方关联的处置任务
        $getOldNursesTasks = NurseModel::getData(where   : ['recipe_id' => $recipeId, 'status' => 1],
                                                 orderBys: ['result_status' => 'desc', 'start_status' => 'desc', 'id' => 'asc']);

        // 使用uid为key，处方项目，关联的化验、影像、处置
        $newRecipeItems = array_column($recipeItemData, null, 'uid');
        $oldRecipeItems = array_column($getOldRecipeItemRes, null, 'uid');

        // 新增的数据、关联数据
        $insertRecipeItems = [];
        $insertTestTasks   = [];
        $insertImageTasks  = [];
        $insertNursesTasks = [];

        // 更新的数据、关联数据
        $updateRecipeItems = [];

        // 删除的关联数据
        $deleteTestTaskIds    = [];
        $deleteImageTasksIds  = [];
        $deleteNursesTasksIds = [];

        // 找出需要新增的处方项目（新的数据存在，老的处方数据不存在）
        $newRecipeItemUids = array_diff(array_keys($newRecipeItems), array_keys($oldRecipeItems));
        array_walk($newRecipeItemUids, function ($uid) use (&$insertRecipeItems, $newRecipeItems) {
            $insertRecipeItems[] = $newRecipeItems[$uid];
        });

        // 找出可能需要更新的项目（两边都存在的项目）
        $commonUids = array_intersect(array_keys($newRecipeItems), array_keys($oldRecipeItems));
        foreach ($commonUids as $uid)
        {
            // 如果无差异，跳过
            $newItem = $newRecipeItems[$uid];
            $oldItem = $oldRecipeItems[$uid];
            if (empty(array_diff_assoc($newItem, $oldItem)))
            {
                continue;
            }

            // 比较两个数组，找出差异
            $curDiffInfo = [];
            foreach ($newItem as $key => $value)
            {
                // 只比较旧数据中存在的字段
                if (array_key_exists($key, $oldItem) && $value != $oldItem[$key])
                {
                    $curDiffInfo[$key] = $value;
                }
            }

            // 如果有差异，添加到更新列表
            if (!empty($curDiffInfo))
            {
                $curDiffInfo['uid']  = $uid;
                $updateRecipeItems[] = $curDiffInfo;
            }
        }

        // 找出需要删除的项目（在旧数据中存在但新数据中不存在）
        $deleteRecipeItemsUids = array_diff(array_keys($oldRecipeItems), array_keys($newRecipeItems));

        // 过滤掉历史处方中化验任务，编辑后未变动项目
        foreach ($getOldTestTasks as $oldTestTasKey => $oldTestTask)
        {
            foreach ($recipeTestTaskData as $newTestTaskKey => $newTestTask)
            {
                if ($oldTestTask['recipe_item_uid'] != $newTestTask['recipe_item_uid'])
                {
                    continue;
                }

                unset($getOldTestTasks[$oldTestTasKey]);
                unset($recipeTestTaskData[$newTestTaskKey]);
                break;
            }
        }

        // 旧的存在的化验任务需要删除，新的需要新增
        if (!empty($recipeTestTaskData))
        {
            $insertTestTasks = $recipeTestTaskData;
        }
        if (!empty($getOldTestTasks))
        {
            $deleteTestTaskIds = array_column($getOldTestTasks, 'id');
        }

        // 过滤掉历史处方中影像任务，编辑后未变动项目
        foreach ($getOldImageTasks as $oldImageTasKey => $oldImageTask)
        {
            foreach ($recipeImageTaskData as $newImageTaskKey => $newImageTask)
            {
                if ($oldImageTask['recipe_item_uid'] != $newImageTask['recipe_item_uid'])
                {
                    continue;
                }

                unset($getOldImageTasks[$oldImageTasKey]);
                unset($recipeImageTaskData[$newImageTaskKey]);
                break;
            }
        }

        // 旧的存在的影像任务需要删除，新的需要新增
        if (!empty($recipeImageTaskData))
        {
            $insertImageTasks = $recipeImageTaskData;
        }
        if (!empty($getOldImageTasks))
        {
            $deleteImageTasksIds = array_column($getOldImageTasks, 'id');
        }

        // 过滤掉历史处方中处置任务，编辑后未变动项目
        foreach ($getOldNursesTasks as $oldNursesTasKey => $oldNursesTask)
        {
            foreach ($recipeNursesTaskData as $newNursesTaskKey => $newNursesTask)
            {
                if ($oldNursesTask['recipe_item_uid'] != $newNursesTask['recipe_item_uid'])
                {
                    continue;
                }

                unset($getOldNursesTasks[$oldNursesTasKey]);
                unset($recipeNursesTaskData[$newNursesTaskKey]);
                break;
            }
        }

        // 旧的存在的处置任务需要删除，新的需要新增
        if (!empty($recipeNursesTaskData))
        {
            $insertNursesTasks = $recipeNursesTaskData;
        }
        if (!empty($getOldNursesTasks))
        {
            $deleteNursesTasksIds = array_column($getOldNursesTasks, 'id');
        }

        // 处方商品无变动，关联业务表也不会变更
        if (empty($insertRecipeItems) && empty($updateRecipeItems) && empty($deleteRecipeItemsUids))
        {
            return self::Success();
        }

        //        // 输出调试（正式环境可以注释）
        //        echo "需要新增的处方项目：\n";
        //        print_r($insertRecipeItems);
        //
        //        echo "需要更新的处方项目：\n";
        //        print_r($updateRecipeItems);
        //
        //        echo "需要删除的处方项目：\n";
        //        print_r($deleteRecipeItemsUids);
        //
        //        echo "需要新增的化验任务：\n";
        //        print_r($insertTestTasks);
        //
        //
        //        echo "需要删除的化验任务：\n";
        //        print_r($deleteTestTaskIds);
        //
        //        echo "需要新增的影像任务：\n";
        //        print_r($insertImageTasks);
        //
        //
        //        echo "需要删除的影像任务：\n";
        //        print_r($deleteImageTasksIds);
        //
        //        echo "需要新增的处置任务：\n";
        //        print_r($insertNursesTasks);
        //
        //
        //        echo "需要删除的处置任务：\n";
        //        print_r($deleteNursesTasksIds);
        //        exit;

        try
        {
            DB::beginTransaction();

            // 存在处方新增数据
            if (!empty($insertRecipeItems))
            {
                foreach ($insertRecipeItems as $key => $itemInfo)
                {
                    $insertRecipeItems[$key]['recipe_id'] = $recipeId;
                }

                RecipeItemModel::insert($insertRecipeItems);
            }

            // 存在处方更新数据
            if (!empty($updateRecipeItems))
            {
                foreach ($updateRecipeItems as $itemInfo)
                {
                    $curDataUid = $itemInfo['uid'];
                    unset($itemInfo['uid']);
                    RecipeItemModel::updateOneByUid($curDataUid, $itemInfo);
                }
            }

            // 存在处方删除数据
            if (!empty($deleteRecipeItemsUids))
            {
                RecipeItemModel::on()
                               ->where(['recipe_id' => $recipeId])
                               ->whereIn('uid', $deleteRecipeItemsUids)
                               ->update(['status' => 0]);
            }

            // 存在处方关联化验任务新增数据
            if (!empty($insertTestTasks))
            {
                foreach ($insertTestTasks as $key => $testTaskInfo)
                {
                    $insertTestTasks[$key]['recipe_id'] = $recipeId;
                }

                TestModel::insert($insertTestTasks);
            }

            // 存在处方关联化验任务删除数据
            if (!empty($deleteTestTaskIds))
            {
                TestModel::on()
                         ->where(['recipe_id' => $recipeId])
                         ->whereIn('id', $deleteTestTaskIds)
                         ->update(['status' => 0]);
            }

            // 存在处方关联影像任务新增数据
            if (!empty($insertImageTasks))
            {
                foreach ($insertImageTasks as $key => $imageTaskInfo)
                {
                    $insertImageTasks[$key]['recipe_id'] = $recipeId;
                }

                ImagesModel::insert($insertImageTasks);
            }

            // 存在处方关联影像任务删除数据
            if (!empty($deleteImageTasksIds))
            {
                ImagesModel::on()
                           ->where(['recipe_id' => $recipeId])
                           ->whereIn('id', $deleteImageTasksIds)
                           ->update(['status' => 0]);
            }

            // 存在处方关联处置任务新增数据
            if (!empty($insertNursesTasks))
            {
                foreach ($insertNursesTasks as $key => $nursesTaskInfo)
                {
                    $insertNursesTasks[$key]['recipe_id'] = $recipeId;
                }

                NurseModel::insert($insertNursesTasks);
            }

            // 存在处方关联处置任务删除数据
            if (!empty($deleteNursesTasksIds))
            {
                NurseModel::on()
                          ->where(['recipe_id' => $recipeId])
                          ->whereIn('id', $deleteNursesTasksIds)
                          ->update(['status' => 0]);
            }

            // 更新处方最新价格
            RecipeModel::updateOne($recipeId, ['price' => $recipeTotalPrice]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑处方异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('编辑处方失败', 39009);
        }
    }

    /**
     * 验证是否可删除处方
     *
     * @param int   $caseId
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function VerifyDeleteRecipe(int $caseId, int $recipeId, array $publicParams): LogicResult
    {
        if (empty($caseId) || empty($recipeId))
        {
            return self::Fail('验证是否可删除处方，缺少病历ID、处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证是否可删除处方，缺少公共参数', 400);
        }

        // 处方是否可删除
        $checkDeleteRes = self::CheckRecipeEditOrDelete($recipeId, $publicParams);
        if ($checkDeleteRes->isFail())
        {
            return $checkDeleteRes;
        }

        return self::Success();
    }

    /**
     * 删除处方
     *
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DeleteRecipe(int $recipeId, array $publicParams): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('删除处方，缺少处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('删除处方，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('删除处方，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('删除处方，缺少医生ID必选参数', 400);
        }

        // 处方是否可删除
        $checkEditRes = self::CheckRecipeEditOrDelete($recipeId, $publicParams);
        if ($checkEditRes->isFail())
        {
            return $checkEditRes;
        }

        $deleteRes = RecipeModel::deleteRecipe($recipeId, $doctorId);
        if (empty($deleteRes))
        {
            return self::Fail('删除处方失败', 39010);
        }

        return self::Success();
    }

    /**
     * 获取病历关联的处方列表
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetRecipeList(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('caseId，缺少必选参数', 400);
        }

        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 获取病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $caseCode = $getCaseRes->getData('case_code');

        // 获取病历关联的处方信息
        $getRecipeRes = RecipeModel::getData(where: ['case_id' => $caseId, 'status' => 1]);
        if (empty($getRecipeRes))
        {
            return self::Success(['data' => []]);
        }

        // 格式化处方信息
        $getFormatRecipeRes = RecipeHelper::FormatRecipeStructure($caseCode, $getRecipeRes, $publicParams);
        if ($getFormatRecipeRes->isFail())
        {
            return $getFormatRecipeRes;
        }

        $returnRecipeList = $getFormatRecipeRes->getData('data', []);
        $returnSummary    = $getFormatRecipeRes->getData('summary', []);

        return self::Success(['summary' => $returnSummary, 'data' => $returnRecipeList]);
    }

    /**
     * 获取有效处方信息
     *
     * @param int   $recipeId
     * @param array $publicParams
     * @param bool  $withHospitalId 增加公参中医院ID
     * @param bool  $withDoctorId   增加公参中医生ID
     *
     * @return LogicResult
     */
    public static function GetValidRecipeById(int $recipeId, array $publicParams, bool $withHospitalId = true, bool $withDoctorId = true): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('获取有效处方信息，缺少处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效处方信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效处方信息，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取有效处方信息，缺少医生ID必选参数', 400);
        }

        // 获取有效处方
        $where = ['id' => $recipeId, 'status' => 1];
        if (!empty($withHospitalId))
        {
            $where['hospital_id'] = $hospitalId;
        }
        if (!empty($withDoctorId))
        {
            $where['doctor_id'] = $doctorId;
        }

        // 获取有效处方信息
        $getRecipeRes = RecipeModel::getData(where: $where);
        if (empty($getRecipeRes))
        {
            return self::Fail('处方不存在', 39000);
        }

        return self::Success(current($getRecipeRes));
    }

    /**
     * 获取处方详情
     *
     * @param int   $recipeId
     * @param array $publicParams
     * @param bool  $withItemPrice 是否返回商品价格 true:需要 false:不需要
     * @param bool  $withItemStock 是否返回商品库存 true:需要 false:不需要
     *
     * @return LogicResult
     */
    public static function GetRecipeDetail(int $recipeId, array $publicParams, bool $withItemPrice = true, bool $withItemStock = true): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('recipeId，缺少必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('orgId、brandId、hospitalId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 获取处方信息
        $getRecipeRes = self::GetValidRecipeById($recipeId, $publicParams);
        if ($getRecipeRes->isFail())
        {
            return $getRecipeRes;
        }

        $getRecipeRes = $getRecipeRes->getData();

        // 获取病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($getRecipeRes['case_id'], $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $caseCode = $getCaseRes->getData('case_code');

        // 获取处方内明细
        $getRecipeItemRes = RecipeItemModel::getData(where: ['recipe_id' => $recipeId, 'status' => 1]);
        if (empty($getRecipeItemRes))
        {
            return self::Fail('处方内明细不存在', 39009);
        }

        $getFormatRecipeItemsRes = RecipeHelper::FormatRecipeItemStructure($getRecipeItemRes,
                                                                           $publicParams,
                                                                           $withItemPrice,
                                                                           $withItemStock,
                                                                           true);
        if ($getFormatRecipeItemsRes->isFail())
        {
            return $getFormatRecipeItemsRes;
        }

        // 格式化处方信息
        $getFormatRecipeRes = RecipeHelper::FormatRecipeStructure($caseCode, [$getRecipeRes], $publicParams);
        if ($getFormatRecipeRes->isFail())
        {
            return $getFormatRecipeRes;
        }

        $returnRecipeInfo                  = current($getFormatRecipeRes->getData('data', []));
        $returnRecipeInfo['items']         = $getFormatRecipeItemsRes->getData('itemList', []);
        $returnRecipeInfo['itemGroupList'] = $getFormatRecipeItemsRes->getData('itemGroupList', []);

        return self::Success($returnRecipeInfo);
    }

    /**
     * 设置无需跟诊
     *
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function RecipeSkipFollowUp(int $recipeId, array $publicParams): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('设置无需跟诊，缺少处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('设置无需跟诊，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('设置无需跟诊，缺少医院ID必选参数', 400);
        }

        // 获取处方信息
        $getRecipeRes = self::GetValidRecipeById($recipeId, $publicParams);
        if ($getRecipeRes->isFail())
        {
            return $getRecipeRes;
        }

        $getRecipeRes = $getRecipeRes->getData();

        // 如果处方已经支付、支付中，同时跟诊状态不是未跟诊的话，不可以在修改
        if ($getRecipeRes['is_paid'] != 0 && $getRecipeRes['is_follow'] != 0)
        {
            return self::Fail('已支付或支付中的处方，设置了跟诊状态，不可修改');
        }

        try
        {
            DB::beginTransaction();

            // 更新处方无需跟诊状态
            RecipeModel::updateOne($recipeId, ['is_follow' => 2]);

            // 如果处方已跟诊，更新跟诊状态无效
            if ($getRecipeRes['is_follow'] == 1)
            {
                RecipesAssistantModel::on()
                                     ->where(['recipe_id' => $recipeId, 'assistant_status' => 1])
                                     ->update(['user_id' => 0, 'assistant_status' => 0]);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 设置无需跟诊异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('设置无需跟诊异常', 40004);
        }
    }

    /**
     * 处方跟诊
     *
     * @param int   $recipeId
     * @param array $assistantIds
     * @param bool  $otherRecipeSyncFollowUp
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function RecipeFollowUpAssistant(int $recipeId, array $assistantIds, array $publicParams, bool $otherRecipeSyncFollowUp = false): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('录入跟诊助理，缺少处方ID必选参数', 400);
        }
        if (empty($assistantIds))
        {
            return self::Fail('录入跟诊助理，缺少跟诊助理必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('录入跟诊助理，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('录入跟诊助理，缺少医院ID必选参数', 400);
        }

        // 获取处方信息
        $getRecipeRes = self::GetValidRecipeById($recipeId, $publicParams);
        if ($getRecipeRes->isFail())
        {
            return $getRecipeRes;
        }

        $getRecipeRes = $getRecipeRes->getData();

        // 验证助理是否存在
        $validAssistantIds = array_filter($assistantIds);
        if (array_filter($validAssistantIds))
        {
            $getAssistantRes = HospitalUserModel::getHospitalUsers($hospitalId, $validAssistantIds);
            if ($getAssistantRes->isEmpty())
            {
                return self::Fail('跟诊助理不存在', 40001);
            }

            // 部分助理不存在
            $getAssistantIds  = $getAssistantRes->pluck('user_id')
                                                ->unique()
                                                ->toArray();
            $diffAssistantIds = array_diff($validAssistantIds, $getAssistantIds);
            if (!empty($diffAssistantIds))
            {
                return self::Fail('部分跟诊助理不存在', 40001);
            }
        }

        // 获取该处方项存在的跟诊助理
        $getRecipeAssistantRes = RecipesAssistantModel::getData(where   : ['recipe_id' => $recipeId],
                                                                orderBys: ['assistant_level' => 'asc'],
                                                                keyBy   : 'assistant_level');

        // 无任何修改
        if (empty($getRecipeAssistantRes) && empty($validAssistantIds))
        {
            return self::Success();
        }

        // 跟诊级助理别对应关系
        $insertData = [];
        $updateData = [];
        foreach ($assistantIds as $level => $userId)
        {
            // 当前是否存在级别跟诊助理
            $curOldAssistantInfo = $getRecipeAssistantRes[$level] ?? [];

            // 无效跟诊助理
            if (empty($userId) && (empty($curOldAssistantInfo) || empty($curOldAssistantInfo['assistant_status'])))
            {
                continue;
            }

            // 修改旧的跟诊记录
            if (!empty($curOldAssistantInfo))
            {
                $updateData[] = [
                    'where' => ['id' => $curOldAssistantInfo['id']],
                    'data'  => [
                        'user_id'          => $userId,
                        'assistant_status' => $userId > 0 ? 1 : 0,
                    ],
                ];
            }
            else
            {
                // 新增当前级别与检测员
                $insertData[] = [
                    'case_id'          => $getRecipeRes['case_id'],
                    'recipe_id'        => $recipeId,
                    'user_id'          => $userId,
                    'assistant_level'  => $level,
                    'assistant_status' => 1,
                ];
            }

            unset($getRecipeAssistantRes[$level]);
        }

        // 旧的跟诊助理存在的修改为无效
        if (!empty($getRecipeAssistantRes))
        {
            $updateData = array_merge($updateData, array_map(function ($item) {
                return [
                    'where' => ['id' => $item['id']],
                    'data'  => [
                        'assistant_status' => 0,
                    ],
                ];
            }, $getRecipeAssistantRes));
        }

        // 没有需要更新的数据
        if (empty($insertData) && empty($updateData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            if (!empty($insertData))
            {
                RecipesAssistantModel::insert($insertData);
            }

            if (!empty($updateData))
            {
                foreach ($updateData as $updateItem)
                {
                    RecipesAssistantModel::updateOne($updateItem['where']['id'], $updateItem['data']);
                }
            }

            // 更新处方跟诊状态
            RecipeModel::updateOne($recipeId, ['is_follow' => !empty($validAssistantIds) ? 1 : 0]);

            // 同步设置更新当前病历下其他处方跟诊与当前一致
            if ($otherRecipeSyncFollowUp)
            {
                $getCaseOtherRecipesRes = RecipeModel::getData(where: [
                                                                          ['id', '!=', $recipeId],
                                                                          ['case_id', '=', $getRecipeRes['case_id']],
                                                                          ['is_follow', '=', 0],
                                                                          ['status', '=', 1]
                                                                      ]);

                foreach ($getCaseOtherRecipesRes as $recipeInfo)
                {
                    $getFollowUpRes = self::RecipeFollowUpAssistant($recipeInfo['id'],
                                                                    $assistantIds,
                                                                    $publicParams);
                    if ($getFollowUpRes->isFail())
                    {
                        DB::rollBack();
                        return $getFollowUpRes;
                    }
                }
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 录入处方跟诊助理异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('录入处方跟诊助理异常', 40004);
        }
    }

    /**
     * 获取宠物历史处方
     *
     * @param int   $petId
     * @param array $publicParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return LogicResult
     */
    public static function GetHistoryRecipesByPetId(int $petId, array $publicParams, int $iPage = 1, int $iPageSize = 10): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('petId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }

        // 获取宠物信息
        $getPetRes = PetLogic::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 获取可查看当前医院数据，互通的所有品牌
        $getAllowedBrandIdsRes = HospitalLogic::GetAllowedBrandIdsByHospitalId($hospitalId);
        if ($getAllowedBrandIdsRes->isFail())
        {
            return $getAllowedBrandIdsRes;
        }

        $allowedBrandIds = $getAllowedBrandIdsRes->getData('allowedBrandIds', []);
        if (empty($allowedBrandIds))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 从快照获取历史处方总数
        $getWhere               = ['pet_id' => $petId, 'status' => 1];
        $getWhereIn             = ['brand_id' => $allowedBrandIds];
        $getHistoryRecipesTotal = RecipeSnapshotModel::getTotalNumber(where: $getWhere, whereIn: $getWhereIn);
        if (empty($getHistoryRecipesTotal))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 从快照获取历史处方列表
        $getHistoryRecipesRes = RecipeSnapshotModel::getData(['recipe_info'],
            where    :                                       $getWhere,
            whereIn  :                                       $getWhereIn,
            pageIndex:                                       $iPage,
            pageSize :                                       $iPageSize);
        if (empty($getHistoryRecipesRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $returnHistoryRecipes = [];
        foreach ($getHistoryRecipesRes as $curInfo)
        {
            if (empty($curInfo['recipe_info']))
            {
                continue;
            }

            $returnHistoryRecipes[] = json_decode($curInfo['recipe_info'], true);
        }

        return self::Success(['total' => $getHistoryRecipesTotal, 'data' => $returnHistoryRecipes]);
    }

    /**
     * 获取历史处方详情
     *
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetHistoryRecipeDetail(int $recipeId, array $publicParams): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('查看历史处方详情，缺少处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('查看历史处方详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('查看历史处方详情，缺少医院ID必选参数', 400);
        }

        // 获取可查看当前医院数据，互通的所有品牌
        $getAllowedBrandIdsRes = HospitalLogic::GetAllowedBrandIdsByHospitalId($hospitalId);
        if ($getAllowedBrandIdsRes->isFail())
        {
            return $getAllowedBrandIdsRes;
        }

        $allowedBrandIds = $getAllowedBrandIdsRes->getData('allowedBrandIds', []);
        if (empty($allowedBrandIds))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取历史处方信息
        $getHistoryRecipeItemRes = RecipeSnapshotModel::getData(fields : ['recipe_info', 'recipe_items'],
                                                                where  : ['recipe_id' => $recipeId, 'status' => 1],
                                                                whereIn: ['brand_id' => $allowedBrandIds]);
        $getHistoryRecipeItemRes = $getHistoryRecipeItemRes ? current($getHistoryRecipeItemRes) : [];
        if (empty($getHistoryRecipeItemRes))
        {
            return self::Fail('历史处方明细不存在', 39000);
        }

        $getHistoryRecipeInfoRes  = json_decode($getHistoryRecipeItemRes['recipe_info'], true);
        $getHistoryRecipeItemList = json_decode($getHistoryRecipeItemRes['recipe_items'], true);
        if (empty($getHistoryRecipeInfoRes) || empty($getHistoryRecipeItemList))
        {
            return self::Fail('历史处方明细不存在', 39000);
        }

        $returnData = array_merge($getHistoryRecipeInfoRes, ['items' => $getHistoryRecipeItemList]);

        return self::Success($returnData);
    }

    /**
     * 验证处方是否可编辑或删除
     *
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function CheckRecipeEditOrDelete(int $recipeId, array $publicParams): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('验证处方是否可操作，缺少处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证处方是否可操作，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证处方是否可操作，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('验证处方是否可操作，缺少医生ID必选参数', 400);
        }

        // 获取处方信息
        $getValidRecipeRes = self::GetValidRecipeById($recipeId, $publicParams);
        if ($getValidRecipeRes->isFail())
        {
            return $getValidRecipeRes;
        }

        // 病历是否正在进行状态
        $caseId     = $getValidRecipeRes->getData('case_id', 0);
        $getCaseRes = CaseLogic::GetUnderWayCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        return self::Success();
    }

    /**
     * 过滤搜索商品
     *
     * @param array $searchItems
     *
     * @return LogicResult
     */
    private static function FilterSearchItems(array $searchItems): LogicResult
    {
        if (empty($searchItems))
        {
            return self::Success();
        }

        $returnSearchItems = [];
        foreach ($searchItems as $itemInfo)
        {
            // 搜索排名分值
            $curItemRankingScore = $itemInfo['ranking_score'] ?? 0;
            if ($curItemRankingScore < 0.5)
            {
                continue;
            }

            $returnSearchItems[] = $itemInfo;
        }

        return self::Success($returnSearchItems);
    }
}
