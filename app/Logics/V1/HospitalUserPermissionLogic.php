<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;

/**
 * 医院操作用户权限验证相关
 * Class HospitalUserPermissionLogic
 * @package App\Logics\V1
 */
class HospitalUserPermissionLogic extends Logic
{
    /**
     * 检查医院会员操作权限
     *
     * @param int $hospitalId
     * @param int $memberId
     *
     * @return LogicResult
     */
    public static function CheckHospitalMemberOperationPermission(int $hospitalId, int $memberId): LogicResult
    {
        if (empty($hospitalId) || empty($memberId))
        {
            return self::Fail('hospitalId、memberId，缺少必选参数', 400);
        }

        // 获取可操作医院的所有品牌
        $getAllowedBrandIdsRes = HospitalLogic::GetAllowedBrandIdsByHospitalId($hospitalId);
        if ($getAllowedBrandIdsRes->isFail())
        {
            return $getAllowedBrandIdsRes;
        }

        // 获取数据
        $allowedBrandIds = $getAllowedBrandIdsRes->getData('allowedBrandIds', []);

        // 获取会员信息
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid($memberId);
        if ($getMemberRes->isFail())
        {
            return $getMemberRes;
        }

        // 如果用户所属品牌，不在可操作医院品牌的白名单中。说明此品牌不互通。
        if (!in_array($getMemberRes->getData('brand_id', 0), $allowedBrandIds))
        {
            return self::Fail('暂无权限操作当前会员', 30006);
        }

        return self::Success();
    }
}
