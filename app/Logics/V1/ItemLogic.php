<?php

namespace App\Logics\V1;

use DB;
use Arr;
use App\Enums\ItemTypeEnum;
use App\Enums\ItemStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\ItemModel;
use App\Models\ItemBarcodeModel;
use App\Models\ItemSalePriceModel;
use App\Models\ItemSaleTypeModel;

class ItemLogic extends Logic
{
    /**
     * 获取商品完整信息
     *
     * @param array $itemIds
     * @param array $itemUids
     * @param array $itemStatus
     * @param array $publicParams
     * @param bool  $withItemPrice
     * @param bool  $withItemStock
     *
     * @return LogicResult
     */
    public static function GetItemFullInfo(array $itemIds = [], array $itemUids = [], array $itemStatus = [ItemStatusEnum::Online], array $publicParams = [], bool $withItemPrice = false, bool $withItemStock = false): LogicResult
    {
        if (empty($itemIds) && empty($itemUids))
        {
            return self::Fail('获取商品信息，缺少商品ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取商品信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('获取商品信息，公共参数不存在', 400);
        }

        // 获取商品信息
        $getWhereIn = [];
        if (!empty($itemIds))
        {
            $getWhereIn['id'] = $itemIds;
        }
        if (!empty($itemUids))
        {
            $getWhereIn['uid'] = $itemUids;
        }
        if (!empty($itemStatus))
        {
            $getWhereIn['status'] = $itemStatus;
        }
        $getItemInfoRes = ItemModel::getData(fields: (['*', DB::raw(ItemModel::getItemDisplayNameField())]), where: ['org_id' => $orgId], whereIn: $getWhereIn);
        if (empty($getItemInfoRes))
        {
            return self::Fail('获取商品信息，商品不存在', 33000);
        }

        // 获取商品项目类型
        $getItemSaleTypeRes = ItemSaleTypeModel::getAllSaleType([0, 1]);
        $getItemSaleTypeRes = $getItemSaleTypeRes->isNotEmpty() ? $getItemSaleTypeRes->keyBy('id')
                                                                                     ->toArray() : [];

        // 获取商品条码
        $arrItemIds        = array_unique(array_column($getItemInfoRes, 'id'));
        $getItemBarcodeRes = ItemBarcodeModel::getData(where: ['org_id' => $orgId, 'status' => 1], whereIn: ['item_id' => $arrItemIds], keyBy: 'item_id');

        // 获取商品价格信息
        if (!empty($withItemPrice))
        {
            // 获取医院信息
            $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
            if ($getHospitalRes->isFail())
            {
                return $getHospitalRes;
            }

            // 医院所属城市
            $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
            $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

            $getItemPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Sku->value, $arrItemIds, $orgId, $brandId, $hospitalProvinceId, $hospitalCityId, $hospitalId);
        }

        // 获取商品库存信息
        if (!empty($withItemStock))
        {
            $getEffectiveQuantityRes = StockItemShelfLogic::GetEffectiveQuantity($arrItemIds, $publicParams);
            if ($getEffectiveQuantityRes->isFail())
            {
                return $getEffectiveQuantityRes;
            }

            $getEffectiveQuantityRes = $getEffectiveQuantityRes->getData();
        }

        $returnItemInfo = [];
        foreach ($getItemInfoRes as $itemInfo)
        {
            // 商品一级项目类型
            $itemInfo['first_sale_type_info'] = $getItemSaleTypeRes[$itemInfo['first_category_id']] ?? [];

            // 商品二级项目类型
            $itemInfo['second_sale_type_info'] = $getItemSaleTypeRes[$itemInfo['second_sale_type_id']] ?? [];

            // 商品条码信息
            $itemInfo['item_barcode_info'] = $getItemBarcodeRes[$itemInfo['id']] ?? [];

            // 商品价格
            $itemInfo['sale_price_info'] = $getItemPriceRes[$itemInfo['id']]['sale_price'] ?? [];

            // 商品库存
            $itemInfo['stock_info'] = $getEffectiveQuantityRes[$itemInfo['id']] ?? [];

            $returnItemInfo[] = $itemInfo;
        }

        return self::Success($returnItemInfo);
    }
}
