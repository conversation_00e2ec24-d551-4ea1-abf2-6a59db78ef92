<?php

namespace App\Logics\V1;

use Illuminate\Support\Arr;
use App\Facades\SearchFacade;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PageEnum;
use App\Enums\SheetStatusEnum;
use App\Enums\BeautyExecuteStatusEnum;
use App\Models\UsersModel;
use App\Models\BeautyServiceSheetModel;
use App\Models\BeautyServiceExecutorModel;
use App\Support\Beauty\BeautySheetHelper;

class BeautyServiceLogic extends Logic
{
    /**
     * 搜索洗美服务商品
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetSearchItems(array $params, array $publicParams): LogicResult
    {
        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        // 业务参数
        $keyword = trim(Arr::get($params, 'keyword', ''));
        $petId   = intval(Arr::get($params, 'petId', 0));

        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('orgId、brandId、hospitalId，缺少必选参数', 400);
        }

        if (empty($keyword) || empty($petId))
        {
            return self::Fail('keywords、petId，缺少必选参数', 400);
        }

        // 获取宠物信息
        $petRes = PetLogic::GetValidPetByIdOrUid($petId);
        if ($petRes->isFail())
        {
            return $petRes;
        }

        // 搜索关键词
        $keyword = trimWhitespace($keyword);

        // 搜索商品
        $getSearchItemsRes = SearchFacade::searchBeautyItems(
            keywords:              $keyword,
            supportPetCategoryIds: [$petRes->getData('category_id')],
            orgId:                 $orgId
        );
        if (empty($getSearchItemsRes))
        {
            return self::Success(['data' => []]);
        }

        // 过滤搜索商品
        $getFilterSearchItemsRes = self::FilterSearchItems($getSearchItemsRes);
        if ($getFilterSearchItemsRes->isFail())
        {
            return $getFilterSearchItemsRes;
        }

        // 构建商品信息结构
        $searchItems      = $getFilterSearchItemsRes->getData();
        $getFormatItemRes = SearchItemLogic::FormatItemInfoStructure($searchItems, $publicParams, true);
        if ($getFormatItemRes->isFail())
        {
            return $getFormatItemRes;
        }

        // 标记商品在不满足的情况下禁用添加到服务
        $getFormatItemRes = $getFormatItemRes->getData();
        foreach ($getFormatItemRes as &$itemInfo)
        {
            // 是否禁用添加，true:禁用，不可添加 false:不禁用，可添加
            $itemInfo['disabled'] = false;

            // 价格无效
            if ($itemInfo['packPrice'] <= 0 || $itemInfo['bulkPrice'] <= 0)
            {
                $itemInfo['disabled'] = true;
            }
        }

        return self::Success(['data' => $getFormatItemRes]);
    }

    /**
     * 获取洗美服务记录列表
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetServiceList(array $params, array $publicParams): LogicResult
    {
        //业务参数
        $page          = intval(Arr::get($params, 'page', PageEnum::DefaultPageIndex));
        $count         = intval(Arr::get($params, 'count', PageEnum::DefaultPageSize));
        $keywords      = trimWhitespace(Arr::get($params, 'keywords', ''));
        $createUserUid = trim(Arr::get($params, 'createUserUid', ''));
        $createUserId  = intval(Arr::get($params, 'createUserId', 0));
        $executeStatus = isset($params['executeStatus']) && is_numeric($params['executeStatus']) ? intval(Arr::get($params,
                                                                                                                   'executeStatus')) : null;
        $startDate     = trim(Arr::get($params, 'startDate', ''));
        $endDate       = trim(Arr::get($params, 'endDate', ''));
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($hospitalId))
        {
            return self::Fail('orgId、brandId、hospitalId，缺少必选参数', 400);
        }
        if ($executeStatus != null && !BeautyExecuteStatusEnum::exists($executeStatus))
        {
            return self::Fail('执行状态参数错误', 400);
        }
        if (!empty($startDate) && strtotime($startDate) === false)
        {
            return self::Fail('开始日期格式错误', 400);
        }
        if (!empty($endDate) && strtotime($endDate) === false)
        {
            return self::Fail('结束日期格式错误', 400);
        }
        if (!empty($startDate) && !empty($endDate) && strtotime($startDate) > strtotime($endDate))
        {
            return self::Fail('开始日期不能大于结束日期', 400);
        }


        if (!empty($createUserUid) || !empty($createUserId))
        {
            $createUser = UsersModel::getOneByIdOrUid(id: $createUserId, uid: $createUserUid);
            if (empty($createUser))
            {
                return self::Fail('创建人不存在', 10100);
            }
            $createUserId = $createUser->id;
        }

        //构建查询条件
        $where = [
            's.hospital_id' => $hospitalId,
            's.status'      => SheetStatusEnum::Paid->value,
        ];

        if ($createUserId > 0)
        {
            $where[] = ['s.created_by', '=', $createUserId];
        }
        if ($executeStatus !== null)
        {
            $where[] = ['s.execute_status', '=', $executeStatus];
        }
        if (!empty($startDate))
        {
            $where[] = ['s.created_at', '>=', $startDate . ' 00:00:00'];
        }
        if (!empty($endDate))
        {
            $where[] = ['s.created_at', '<=', $endDate . ' 23:59:59'];
        }

        $searchResult = BeautyServiceSheetModel::SearchSheet(
            keywords: $keywords,
            where:    $where,
            orderBy:  ['s.id' => 'asc'],
            page:     $page,
            count:    $count
        );

        $total = $searchResult->total();
        $data  = $searchResult->items();

        if (empty($data))
        {
            return self::Success(['total' => $total, 'data' => []]);
        }


        return self::Success([
                                 'total' => $total,
                                 'data'  => BeautySheetHelper::FormatSheetListStructure($data, $publicParams, true)
                             ]);
    }

    /**
     * 洗美服务单执行
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws \Throwable
     */
    public static function FollowServiceExecutor(array $params, array $publicParams): LogicResult
    {
        //业务参数
        $sheetCode    = trim(Arr::get($params, 'sheetCode', ''));
        $sheetId      = intval(Arr::get($params, 'sheetId', 0));
        $executor1Uid = !empty($params['executor1']) ? trim(Arr::get($params, 'executor1', '')) : null;
        $executor1Id  = intval(Arr::get($params, 'executor1Id', 0));
        $executor2Uid = !empty($params['executor2']) ? trim(Arr::get($params, 'executor2', '')) : null;
        $executor2Id  = intval(Arr::get($params, 'executor2Id', 0));
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCode) && empty($sheetId))
        {
            return self::Fail('洗美服务单执行，缺少单据ID/编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('洗美服务单执行，缺少医院ID必选参数', 400);
        }

        $sheetRes = BeautySheetHelper::GetValidSheet($sheetCode, $sheetId, $hospitalId);
        if ($sheetRes->isFail())
        {
            return $sheetRes;
        }
        $sheet   = $sheetRes->getData();
        $sheetId = $sheet['id'];

        //未支付的不允许执行
        if ($sheet['status'] != SheetStatusEnum::Paid->value)
        {
            return self::Fail('洗美服务单未支付，不可执行', 500421);
        }

        //一个月前的不允许再执行
        if (strtotime($sheet['created_at']) < strtotime('-1month'))
        {
            return self::Fail('洗美服务单已超过执行时间限制', 500420);
        }

        if (!empty($executor1Uid) || !empty($executor1Id))
        {
            $executor1 = UsersModel::getOneByIdOrUid(id: $executor1Id, uid: $executor1Uid);
            if (empty($executor1))
            {
                return self::Fail('美容师不存在', 10100);
            }
            $executor1Id = $executor1->id;

            $executor1Exist = HospitalUserLogic::GetUserHospitalUser($executor1Id, $hospitalId);
            if ($executor1Exist->isFail())
            {
                return $executor1Exist;
            }
        }
        if (!empty($executor2Uid) || !empty($executor2Id))
        {
            $executor2 = UsersModel::getOneByIdOrUid(id: $executor2Id, uid: $executor2Uid);
            if (empty($executor2))
            {
                return self::Fail('美容师不存在', 10100);
            }
            $executor2Id = $executor2->id;

            $executor2Exist = HospitalUserLogic::GetUserHospitalUser($executor2Id, $hospitalId);
            if ($executor2Exist->isFail())
            {
                return $executor2Exist;
            }
        }

        // 查询当前执行人数据
        $existingExecutors = BeautyServiceExecutorModel::getData(
            where:    [
                          'sheet_id'    => $sheetId,
                          'hospital_id' => $hospitalId,
                      ],
            orderBys: ['assistant_level' => 'asc'],
            keyBy:    'assistant_level'
        );

        //根据条件构建更新或新增的数据
        $updateSheet        = [];
        $insertExecutorData = [];
        $updateExecutorData = [];

        // 帮助函数：判断是否有效ID
        $isValid = fn($id) => is_numeric($id) && $id > 0;

        if ($isValid($executor1Id) || $isValid($executor2Id))
        {
            // 至少有一个是有效的，执行状态为 已执行
            $updateSheet['execute_status'] = BeautyExecuteStatusEnum::Completed->value;

            foreach ([1 => $executor1Id, 2 => $executor2Id] as $level => $userId)
            {
                if ($isValid($userId))
                {
                    if (!isset($existingExecutors[$level]))
                    {
                        // 不存在：准备插入数据
                        $insertExecutorData[] = [
                            'sheet_id'         => $sheetId,
                            'hospital_id'      => $hospitalId,
                            'user_id'          => $userId,
                            'assistant_level'  => $level,
                            'assistant_status' => BeautyExecuteStatusEnum::Completed->value,
                        ];
                    }
                    elseif ($existingExecutors[$level]['user_id'] != $userId || $existingExecutors[$level]['assistant_status'] != 1)
                    {
                        // 存在，但 user_id 不一样或状态不一致：准备更新
                        $updateExecutorData[] = [
                            'id'               => $existingExecutors[$level]['id'],
                            'user_id'          => $userId,
                            'assistant_status' => BeautyExecuteStatusEnum::Completed->value,
                        ];
                    }
                }
                elseif (isset($existingExecutors[$level]))
                {
                    // 无效参数，但原本有执行人：改为未执行
                    $updateExecutorData[] = [
                        'id'               => $existingExecutors[$level]['id'],
                        'user_id'          => 0,
                        'assistant_status' => BeautyExecuteStatusEnum::Pending->value
                    ];
                }
            }
        }
        else
        {
            // 两个都无效或不存在：执行状态设为 未执行
            $updateSheet['execute_status'] = BeautyExecuteStatusEnum::Pending->value;

            // 若存在执行人，更新状态为未执行
            foreach ([1, 2] as $level)
            {
                if (isset($existingExecutors[$level]))
                {
                    $updateExecutorData[] = [
                        'id'               => $existingExecutors[$level]['id'],
                        'user_id'          => 0,
                        'assistant_status' => BeautyExecuteStatusEnum::Pending->value
                    ];
                }
            }
        }

        if (empty($updateSheet) && empty($insertExecutorData) && empty($updateExecutorData))
        {
            return self::Success();
        }

        $executeRes = BeautyServiceSheetModel::FollowExecutor($sheetId,
                                                              $updateSheet,
                                                              $insertExecutorData,
                                                              $updateExecutorData);

        if (!$executeRes)
        {
            return self::Fail('执行洗美服务单失败', 500420);
        }

        return self::Success();
    }

    /**
     * 过滤搜索商品
     *
     * @param array $searchItems
     *
     * @return LogicResult
     */
    private static function FilterSearchItems(array $searchItems): LogicResult
    {
        if (empty($searchItems))
        {
            return self::Success();
        }

        $returnSearchItems = [];
        foreach ($searchItems as $itemInfo)
        {
            // 搜索排名分值
            $curItemRankingScore = $itemInfo['ranking_score'] ?? 0;
            if ($curItemRankingScore < 0.5)
            {
                continue;
            }

            $returnSearchItems[] = $itemInfo;
        }

        return self::Success($returnSearchItems);
    }
}
