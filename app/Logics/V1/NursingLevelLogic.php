<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RoomsBedsModel;
use App\Models\NursingLevelModel;

class NursingLevelLogic extends Logic
{
    /**
     * 获取护理等级
     * @return LogicResult
     */
    public static function GetNursingLevelOptions(): LogicResult
    {
        $getNursingLevelRes = NursingLevelModel::all();
        if (empty($getNursingLevelRes))
        {
            return self::Success(['nursingLevelOptions' => []]);
        }

        $returnNursingLevelInfo = [];
        foreach ($getNursingLevelRes as $curInfo)
        {
            $returnNursingLevelInfo[] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['nursingLevelOptions' => $returnNursingLevelInfo]);
    }
}
