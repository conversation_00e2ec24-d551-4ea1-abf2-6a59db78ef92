<?php

namespace App\Logics\V1;

use Arr;
use App\Support\Item\ItemHelper;
use App\Enums\PageEnum;
use App\Enums\PurchaseTypeEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\PurchaseOrderModel;
use App\Models\PurchaseOrderItemModel;

class StockTransferLogic extends Logic
{

    /**
     * 获取采购单列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetStockTransferLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取调拨单列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取调拨单列表，缺少公共必选参数', 400);
        }

        // 业务参数
        $iPage     = intval(Arr::get($searchParams, 'page', 0)) ?? PageEnum::DefaultPageIndex->value;
        $iPageSize = intval(Arr::get($searchParams, 'count', 0)) ?? PageEnum::DefaultPageSize->value;

        // 获取调拨单列表
        $searchParams['purchaseType']    = PurchaseTypeEnum::Transfer->value;
        $searchParams['allotHospitalId'] = $hospitalId;
        $getTransferOrderListRes         = PurchaseOrderModel::getTransferOrderListData($searchParams, $iPage, $iPageSize);
        if (empty($getTransferOrderListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount      = $getTransferOrderListRes['total'] ?? 0;
        $transferListRes = $getTransferOrderListRes['data'] ?? [];
        if (empty($totalCount) || empty($transferListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $getFormatTransferListRes = self::FormatTransferOrderStructure($transferListRes);
        if ($getFormatTransferListRes->isFail())
        {
            return $getFormatTransferListRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatTransferListRes->getData()]);
    }

    /**
     * 获取调拨单详情
     *
     * @param int   $transferOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTransferOrderDetail(int $transferOrderId, array $publicParams): LogicResult
    {
        if (empty($transferOrderId))
        {
            return self::Fail('获取调拨单详情，缺少调拨单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取调拨单详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取采购单详情，缺少公共医院ID必选参数', 400);
        }

        // 获取调拨单信息
        $getTransferOrderRes = PurchaseOrderModel::getTransferOrderListData(['purchaseOrderId' => $transferOrderId, 'allotHospitalId' => $hospitalId]);
        $getTransferOrderRes = !empty($getTransferOrderRes['data']) ? current($getTransferOrderRes['data']) : [];
        if (empty($getTransferOrderRes))
        {
            return self::Fail('调拨单不存在', 42010);
        }

        // 获取调拨单商品明细
        $getTransferOrderItemRes = PurchaseOrderItemModel::getData(where: ['purchase_order_id' => $transferOrderId, 'status' => 1]);
        if (empty($getTransferOrderItemRes))
        {
            return self::Fail('调拨单商品不存在', 42011);
        }

        // 获取调拨单商品信息
        $itemIds                = array_column($getTransferOrderItemRes, 'item_id');
        $getTransferItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, itemStatus: [], publicParams: $publicParams, withItemPrice: true, withItemStock: true);
        if ($getTransferItemInfoRes->isFail())
        {
            return $getTransferItemInfoRes;
        }

        // 格式化调拨单商品信息
        $getFormatTransferOrderRes = self::FormatTransferOrderStructure([$getTransferOrderRes]);
        if ($getFormatTransferOrderRes->isFail())
        {
            return $getFormatTransferOrderRes;
        }

        // 格式化调拨单商品信息
        $getFormatPurchaseItemRes = self::FormatTransferItemStructure($getTransferOrderItemRes, $getTransferItemInfoRes->getData());
        if ($getFormatPurchaseItemRes->isFail())
        {
            return $getFormatPurchaseItemRes;
        }

        $getFormatTransferOrderRes          = current($getFormatTransferOrderRes->getData());
        $getFormatTransferOrderRes['items'] = $getFormatPurchaseItemRes->getData();

        return self::Success($getFormatTransferOrderRes);
    }

    /**
     * 格式化调拨单列表
     *
     * @param array $transferListRes
     *
     * @return LogicResult
     */
    private static function FormatTransferOrderStructure(array $transferListRes): LogicResult
    {
        if (empty($transferListRes))
        {
            return self::Success();
        }

        $returnTransferList = [];
        foreach ($transferListRes as $curTransferOrder)
        {
            $tmpTransferOrder = [
                'purchaseCode'   => $curTransferOrder['purchase_code'],
                'hospital'       => [
                    'uid'  => $curTransferOrder['hospital_uid'],
                    'name' => $curTransferOrder['hospital_alias_name'],
                ],
                'outboundStatus' => [
                    'id'   => 0,
                    'name' => '未出库',
                ], // TODO 出库状态
                'purchasePrice'  => formatDisplayNumber($curTransferOrder['purchase_price'], 4),
                'createTime'     => formatDisplayDateTime($curTransferOrder['created_at']),
                'remark'         => $curTransferOrder['remark'],
            ];

            $returnTransferList[] = $tmpTransferOrder;
        }

        return self::Success($returnTransferList);
    }

    /**
     * 格式化调拨单商品信息
     *
     * @param array $transferItemRes
     * @param array $itemInfoList
     *
     * @return LogicResult
     */
    private static function FormatTransferItemStructure(array $transferItemRes, array $itemInfoList): LogicResult
    {
        if (empty($transferItemRes) || empty($itemInfoList))
        {
            return self::Success();
        }

        // 按照商品ID数组
        $itemInfoList = array_column($itemInfoList, null, 'id');

        $returnTransferItemList = [];
        foreach ($transferItemRes as $curTransferItem)
        {
            // 商品信息
            $curItemInfo = $itemInfoList[$curTransferItem['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            $tmpTransferItem = [
                'uid'                  => $curTransferItem['uid'],
                'itemUid'              => $curItemInfo['uid'],
                'itemBarcode'          => $curTransferItem['item_barcode'],
                'itemName'             => ItemHelper::ItemDisplayName($curItemInfo),
                'packQuantity'         => formatDisplayNumber($curTransferItem['pack_quantity']),
                'bulkQuantity'         => formatDisplayNumber($curTransferItem['bulk_quantity']),
                'outboundPackQuantity' => 0, // TODO 出库数量
                'outboundBulkQuantity' => 0, // TODO 出库数量
                'isOutboundComplete'   => 0, // TODO 出库完成
                'itemInfo'             => ItemHelper::FormatItemInfoStructure($curItemInfo),
            ];

            $returnTransferItemList[] = $tmpTransferItem;
        }

        return self::Success($returnTransferItemList);
    }
}
