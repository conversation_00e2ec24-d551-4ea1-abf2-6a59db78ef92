<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Models\StockItemShelfModel;

/**
 * 库存减少操作逻辑类
 *
 * 主要处理各种出库场景的库存减少操作
 * 支持整散比换算、效期优先出库、散装优先策略
 */
class StockItemShelfReduceLogic extends Logic
{
    /**
     * 库存减少操作
     *
     * @param array $reduceStockParams 减少库存的参数
     * @param array $publicParams      公共参数，包含医院ID、操作人等
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function ReduceStockShelfQuantity(array $reduceStockParams, array $publicParams): LogicResult
    {
        if (empty($reduceStockParams))
        {
            return self::Fail('库存减少，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('库存减少，缺少公共必选参数', 400);
        }

        // 公共参数验证
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $userId     = intval(Arr::get($publicParams, '_userId', 0));
        if (empty($hospitalId))
        {
            return self::Fail('库存减少，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('库存减少，缺少操作人ID必选参数', 400);
        }

        // 获取减少库存商品信息
        $itemIds        = array_unique(array_filter(array_column($reduceStockParams, 'itemId')));
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        // 商品信息不完整
        $getItemInfoRes = array_column($getItemInfoRes->getData(), null, 'id');
        if (count($itemIds) != count($getItemInfoRes))
        {
            return self::Fail('库存减少，部分商品信息不存在', 44000);
        }

        $validReduceStockParams = [];
        foreach ($reduceStockParams as $curReduceStockInfo)
        {
            $curCheckParamsRes = self::CheckReduceStockItemQuantityParams($curReduceStockInfo);
            if ($curCheckParamsRes->isFail())
            {
                return $curCheckParamsRes;
            }

            $validReduceStockParams[] = $curCheckParamsRes->getData();
        }

        // 无有效出库信息
        if (empty($validReduceStockParams))
        {
            return self::Fail('库存减少，减少商品信息全部无效', 44001);
        }

        try
        {
            DB::beginTransaction();

            foreach ($validReduceStockParams as $curValidReduceStockInfo)
            {
                $getReduceStockItemShelfRes = self::reduceStockItemShelf($curValidReduceStockInfo, $getItemInfoRes, $publicParams);
                if ($getReduceStockItemShelfRes->isFail())
                {
                    DB::rollBack();

                    return $getReduceStockItemShelfRes;
                }
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 库存减少异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('库存减少异常', 44002);
        }
    }

    /**
     * 减少商品库存
     *
     * @param array $reduceStockInfo 减少库存信息
     * @param array $itemInfoList    商品信息列表
     * @param array $publicParams    公共参数
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function reduceStockItemShelf(array $reduceStockInfo, array $itemInfoList, array $publicParams): LogicResult
    {
        $hospitalId           = intval(Arr::get($publicParams, '_hospitalId', 0));
        $itemId               = $reduceStockInfo['itemId'];
        $itemBarcode          = $reduceStockInfo['itemBarcode'];
        $requiredPackQuantity = $reduceStockInfo['packQuantity'];
        $requiredBulkQuantity = $reduceStockInfo['bulkQuantity'];

        // 获取商品信息
        $itemInfo = $itemInfoList[$itemId] ?? [];
        if (empty($itemInfo))
        {
            return self::Fail('库存减少，商品信息不存在', 44003);
        }

        $bulkRatio = $itemInfo['bulk_ratio'] ?? 1;

        // 计算需要出库的总散装数量
        $requiredTotalBulk = StockQuantityConversionHelper::convertToTotalBulkQuantity($requiredPackQuantity,
                                                                                       $requiredBulkQuantity,
                                                                                       $bulkRatio);

        // 获取该商品的有效库存（按效期排序）
        $getStockListRes = self::getItemStockListByExpire($itemId, $hospitalId);
        if ($getStockListRes->isFail())
        {
            return $getStockListRes;
        }

        $stockList = $getStockListRes->getData();
        if (empty($stockList))
        {
            return self::Fail("商品【{$itemBarcode}】库存不足", 44004);
        }

        // 验证库存是否足够
        $totalAvailableBulk = 0;
        foreach ($stockList as $stock)
        {
            $totalAvailableBulk += StockQuantityConversionHelper::convertToTotalBulkQuantity($stock['effective_pack_quantity'],
                                                                                             $stock['effective_bulk_quantity'],
                                                                                             $bulkRatio);
        }

        if ($totalAvailableBulk < $requiredTotalBulk)
        {
            return self::Fail("商品【{$itemBarcode}】库存不足，需要{$requiredTotalBulk}散装，可用{$totalAvailableBulk}散装", 44005);
        }

        // 按效期优先、散装优先策略分配出库
        $getAllocationRes = self::allocateStockByFIFO($stockList, $requiredTotalBulk, $bulkRatio);
        if ($getAllocationRes->isFail())
        {
            return $getAllocationRes;
        }

        $allocations = $getAllocationRes->getData();

        // 执行库存减少操作
        foreach ($allocations as $allocation)
        {
            $updateRes = self::updateStockRecord($allocation, $reduceStockInfo, $publicParams);
            if ($updateRes->isFail())
            {
                return $updateRes;
            }
        }

        return self::Success();
    }

    /**
     * 获取商品库存列表（按效期排序）
     *
     * @param int $itemId     商品ID
     * @param int $hospitalId 医院ID
     *
     * @return LogicResult
     */
    private static function getItemStockListByExpire(int $itemId, int $hospitalId): LogicResult
    {
        $where = [
            ['item_id', '=', $itemId],
            ['hospital_id', '=', $hospitalId],
            ['status', '=', 1],
        ];

        // 有库存的记录
        $where[] = [
            function ($query) {
                return $query->where('effective_pack_quantity', '>', 0)
                             ->orWhere('effective_bulk_quantity', '>', 0);
            }
        ];

        $stockList = StockItemShelfModel::getData(fields  : ['id', 'shelf_code', 'effective_pack_quantity', 'effective_bulk_quantity', 'expired_date'],
                                                  where   : $where,
                                                  orderBys: [
                                                                'expired_date' => 'asc',  // 效期优先（近期先出）
                                                                'id'           => 'asc'   // 同效期按入库时间排序
                                                            ]);

        return self::Success($stockList);
    }

    /**
     * 按FIFO策略分配库存（效期优先，散装优先）
     * 
     * 核心逻辑：
     * 1. 按效期排序，优先出库即将过期的商品
     * 2. 对于每行库存记录，优先消耗散装库存
     * 3. 散装不够时，拆分整装库存（按整散比换算）
     * 4. 拆分整装后的剩余散装，必须更新到同一行记录中
     *
     * @param array $stockList         库存列表
     * @param int   $requiredTotalBulk 需要的总散装数量
     * @param int   $bulkRatio         整散比
     *
     * @return LogicResult
     */
    private static function allocateStockByFIFO(array $stockList, int $requiredTotalBulk, int $bulkRatio): LogicResult
    {
        $allocations       = [];
        $remainingRequired = $requiredTotalBulk;

        foreach ($stockList as $stock)
        {
            if ($remainingRequired <= 0)
            {
                break;
            }

            $stockId          = $stock['id'];
            $availablePackQty = intval($stock['effective_pack_quantity']);
            $availableBulkQty = intval($stock['effective_bulk_quantity']);

            // 第一步：优先消耗当前行的散装库存
            $useBulkQty        = min($remainingRequired, $availableBulkQty);
            $remainingRequired -= $useBulkQty;

            $usePackQty          = 0;
            $addBackBulkQty      = 0; // 整装拆分后需要加回到当前行的散装数量

            // 第二步：散装不够时，拆分当前行的整装库存
            if ($remainingRequired > 0 && $availablePackQty > 0 && $bulkRatio > 1)
            {
                // 计算需要拆分多少个整装
                $neededPackQty = ceil($remainingRequired / $bulkRatio);
                $usePackQty    = min($neededPackQty, $availablePackQty);
                
                // 拆分的整装总共能提供多少散装
                $convertedBulkFromPack = $usePackQty * $bulkRatio;
                
                // 实际需要使用的散装数量
                $actualUsedBulkFromPack = min($remainingRequired, $convertedBulkFromPack);
                
                // 拆分后剩余的散装需要加回到当前行
                $addBackBulkQty = $convertedBulkFromPack - $actualUsedBulkFromPack;
                
                $remainingRequired -= $actualUsedBulkFromPack;
            }

            // 记录当前行的分配结果
            if ($useBulkQty > 0 || $usePackQty > 0)
            {
                $allocations[] = [
                    'stockId'         => $stockId,
                    'shelfCode'       => $stock['shelf_code'],
                    'usedPackQty'     => $usePackQty,      // 消耗的整装数量
                    'usedBulkQty'     => $useBulkQty,      // 消耗的散装数量  
                    'addBackBulkQty'  => $addBackBulkQty,  // 整装拆分后需要加回的散装数量
                    'originalPackQty' => $availablePackQty,
                    'originalBulkQty' => $availableBulkQty,
                ];
            }
        }

        if ($remainingRequired > 0)
        {
            return self::Fail('库存分配失败，库存不足', 44006);
        }

        return self::Success($allocations);
    }

    /**
     * 更新库存记录
     * 
     * 重要：整装拆分后的剩余散装必须更新到同一行记录中，
     * 因为每行记录可能有不同的效期，不能跨行操作
     *
     * @param array $allocation      分配信息
     * @param array $reduceStockInfo 减少库存信息
     * @param array $publicParams    公共参数
     *
     * @return LogicResult
     */
    private static function updateStockRecord(array $allocation, array $reduceStockInfo, array $publicParams): LogicResult
    {
        $stockId        = $allocation['stockId'];
        $usedPackQty    = $allocation['usedPackQty'];
        $usedBulkQty    = $allocation['usedBulkQty'];
        $addBackBulkQty = $allocation['addBackBulkQty'];

        // 计算更新后的库存数量
        // 新整装数量 = 原整装数量 - 消耗的整装数量
        $newPackQty = $allocation['originalPackQty'] - $usedPackQty;
        
        // 新散装数量 = 原散装数量 - 消耗的散装数量 + 整装拆分后加回的散装数量
        $newBulkQty = $allocation['originalBulkQty'] - $usedBulkQty + $addBackBulkQty;

        $updateData = [
            'effective_pack_quantity' => $newPackQty,
            'effective_bulk_quantity' => $newBulkQty,
        ];

        // 如果库存为0，可以考虑标记状态（根据业务需求）
        if ($newPackQty <= 0 && $newBulkQty <= 0)
        {
            // $updateData['status'] = 0; // 根据业务需求决定是否要标记为无效
        }

        $affectedRows = StockItemShelfModel::updateOne($stockId, $updateData);
        if ($affectedRows <= 0)
        {
            return self::Fail("更新库存记录失败，库存ID：{$stockId}", 44007);
        }

        // 记录出库日志（可选，根据业务需求）
        // self::logStockReduce($allocation, $reduceStockInfo, $publicParams);

        return self::Success();
    }

    /**
     * 验证减少库存参数
     *
     * @param array $curReduceStockInfo
     *
     * @return LogicResult
     */
    private static function CheckReduceStockItemQuantityParams(array $curReduceStockInfo): LogicResult
    {
        $curItemId       = intval(Arr::get($curReduceStockInfo, 'itemId', 0));
        $curItemBarcode  = trimWhitespace(Arr::get($curReduceStockInfo, 'itemBarcode', ''));
        $curPackQuantity = intval(Arr::get($curReduceStockInfo, 'packQuantity', 0));
        $curBulkQuantity = intval(Arr::get($curReduceStockInfo, 'bulkQuantity', 0));
        $curReason       = trimWhitespace(Arr::get($curReduceStockInfo, 'reason', ''));

        if (empty($curItemId) && empty($curItemBarcode))
        {
            return self::Fail('库存减少，商品条码错误', 44008);
        }

        $errorPrefix = '库存减少 【' . $curItemBarcode . '】';
        if ($curPackQuantity <= 0 && $curBulkQuantity <= 0)
        {
            return self::Fail($errorPrefix . '，减少整装数量和减少散装数量不能同时为0或负数', 44009);
        }
        if (empty($curReason))
        {
            return self::Fail($errorPrefix . '，减少原因必填', 44010);
        }

        $returnStockItemInfo = [
            'itemId'       => $curItemId,
            'itemBarcode'  => $curItemBarcode,
            'packQuantity' => $curPackQuantity,
            'bulkQuantity' => $curBulkQuantity,
            'reason'       => $curReason,
        ];

        return self::Success($returnStockItemInfo);
    }
}
