<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\AddressAreaModel;
use App\Models\AddressCityModel;
use App\Models\AddressProvinceModel;
use App\Models\AddressStreetModel;

class AreaLogic extends Logic
{
    /**
     * 获取省市区县各级
     * @return LogicResult
     */
    public static function GetProvinceOptions(): LogicResult
    {
        // 获取所有数据
        $getProvRes   = AddressProvinceModel::all();
        $getCityRes   = AddressCityModel::all();
        $getAreaRes   = AddressAreaModel::all();
        $getStreetRes = AddressStreetModel::all();

        // 初始化映射缓存
        $streetMap = [];
        foreach ($getStreetRes as $s)
        {
            $streetMap[$s->area_id][] = [
                'value' => (int) $s->id,
                'label' => $s->ext_name,
            ];
        }

        $areaMap = [];
        foreach ($getAreaRes as $a)
        {
            $areaMap[$a->city_id][] = [
                'value'    => (int) $a->id,
                'label'    => $a->ext_name,
                'children' => $streetMap[$a->id] ?? [],
            ];
        }

        $cityMap = [];
        foreach ($getCityRes as $c)
        {
            $cityMap[$c->prov_id][] = [
                'value'    => (int) $c->id,
                'label'    => $c->ext_name,
                'children' => $areaMap[$c->id] ?? [],
            ];
        }

        // 最终组装省级数据
        $result = [];
        foreach ($getProvRes as $p)
        {
            $result[] = [
                'value'    => (int) $p->id,
                'label'    => $p->ext_name,
                'children' => $cityMap[$p->id] ?? [],
            ];
        }

        return self::Success(['provinceOptions' => $result]);
    }

    /**
     * 根据ID获取省市区街道信息
     *
     * @param int $provinceId 省份ID
     * @param int $cityId     城市ID
     * @param int $areaId     区县ID
     * @param int $streetId   街道ID
     *
     * @return LogicResult
     */
    public static function GetProvinceCityAreaStreetById(int $provinceId = 0, int $cityId = 0, int $areaId = 0, int $streetId = 0): LogicResult
    {
        $result = [];

        // 如果有街道ID，查询街道及其上级
        if ($streetId > 0)
        {
            $street = AddressStreetModel::getOne($streetId);
            if (empty($street))
            {
                return self::Fail('街道地址信息不存在', 400);
            }

            $result['street'] = [
                'id'   => (int) $street->id,
                'name' => $street->ext_name,
            ];

            if ($areaId > 0 && $areaId != $street->area_id)
            {
                return self::Fail('区域地址匹配不一致', 400);
            }

            // 默认自动获取
            $areaId = $street->area_id;
        }

        // 如果有区县ID，查询区县及其上级
        if ($areaId > 0)
        {
            $area = AddressAreaModel::getOne($areaId);
            if (empty($area))
            {
                return self::Fail('区域地址信息不存在', 400);
            }

            $result['area'] = [
                'id'   => (int) $area->id,
                'name' => $area->ext_name,
            ];

            if ($cityId > 0 && $cityId != $area->city_id)
            {
                return self::Fail('城市地址匹配不一致', 400);
            }

            // 默认自动获取
            $cityId = $area->city_id;
        }

        // 如果有城市ID，查询城市及其上级
        if ($cityId > 0)
        {
            $city = AddressCityModel::getOne($cityId);
            if (empty($city))
            {
                return self::Fail('城市地址信息不存在', 400);
            }

            $result['city'] = [
                'id'   => (int) $city->id,
                'name' => $city->ext_name,
            ];

            if ($provinceId > 0 && $provinceId != $city->prov_id)
            {
                return self::Fail('省份地址匹配不一致', 400);
            }

            // 默认自动获取
            $provinceId = $city->prov_id;
        }

        // 如果有省份ID，查询省份
        if ($provinceId > 0)
        {
            $province = AddressProvinceModel::getOne($provinceId);
            if (empty($province))
            {
                return self::Fail('省份地址信息不存在', 400);
            }

            $result['province'] = [
                'id'   => (int) $province->id,
                'name' => $province->ext_name
            ];
        }

        return self::Success($result);
    }
}
