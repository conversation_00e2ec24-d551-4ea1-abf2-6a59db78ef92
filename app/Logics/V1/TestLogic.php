<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\TestOrImageStatusEnum;
use App\Enums\TestOrImagePaidStatusEnum;
use App\Enums\TestOrImageResultTypeEnum;
use App\Enums\HospitalUserByBusinessEnum;
use App\Models\TestModel;
use App\Models\MemberPetsModel;
use App\Models\RecipeItemModel;
use App\Models\TestsTesterModel;
use App\Models\HospitalUserModel;
use App\Models\TestsResultsModel;
use App\Models\TestsResultsFilesModel;
use App\Models\TestsResultsImageModel;
use App\Models\TestsResultsReportModel;
use App\Models\TestsReportTemplatesModel;
use App\Models\TestsRelationTemplatesModel;
use App\Models\TestsResultsReportDetailModel;
use App\Models\TestsReportTemplatesDimensionsModel;
use App\Models\TestsReportTemplatesIndicatorsModel;

class TestLogic extends Logic
{
    /**
     * 化验项筛选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTestFilterOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取化验列表筛选项，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取化验列表筛选项，缺少医院ID必选参数', 400);
        }

        // 获取医院内医生角色
        $getHospitalDoctorRes = HospitalUserLogic::GetHospitalUsersByBusiness(HospitalUserByBusinessEnum::Doctor->value,
                                                                              $publicParams);

        // 化验检测状态
        $getTestStatusOptions = TestOrImageStatusEnum::options();

        // 化验付款状态
        $getTestPaidStatusOptions = TestOrImagePaidStatusEnum::options();

        // 检测员录入状态
        $testerStatusOptions = [
            [
                'id'   => 0,
                'name' => '未录入',
            ],
            [
                'id'   => 1,
                'name' => '已录入',
            ],
        ];

        return self::Success([
                                 'doctorOptions'         => $getHospitalDoctorRes->getData('data', []),
                                 'testStatusOptions'     => $getTestStatusOptions,
                                 'testPaidStatusOptions' => $getTestPaidStatusOptions,
                                 'testerStatusOptions'   => $testerStatusOptions,
                             ]);
    }

    /**
     * 获取化验列表
     *
     * @param array $searchParams
     * @param array $publicParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return LogicResult
     */
    public static function GetTestList(array $searchParams, array $publicParams, int $iPage, int $iPageSize): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('检测列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('检测列表，缺少医院ID必选参数', 400);
        }

        // 获取当前医院下化验列表
        $searchParams['hospitalId'] = $hospitalId;
        $getTestListRes             = TestModel::getTestListData($searchParams, $iPage, $iPageSize);
        if (empty($getTestListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount  = $getTestListRes['total'] ?? 0;
        $testListRes = $getTestListRes['data'] ?? [];
        if (empty($totalCount) || empty($testListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 格式化化验信息
        $getFormatTestRes = self::FormatTestInfo($testListRes);
        if ($getFormatTestRes->isFail())
        {
            return $getFormatTestRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatTestRes->getData()]);
    }

    /**
     * 获取有效化验信息
     *
     * @param int      $testId
     * @param array    $publicParams
     * @param bool     $withHospitalId 是否指定医院
     * @param int|null $testStatus
     *
     * @return LogicResult
     */
    public static function GetValidTest(int $testId, array $publicParams, bool $withHospitalId = true, ?int $testStatus = 1): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('获取化验项，缺少化验ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取化验项，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取化验项，缺少医院ID必选参数', 400);
        }

        // 根据化验编码查询化验记录
        $getWhere = ['id' => $testId];
        if (!empty($withHospitalId))
        {
            $getWhere['hospital_id'] = $hospitalId;
        }

        $getTestRes = TestModel::getData(where: $getWhere);
        $getTestRes = $getTestRes ? current($getTestRes) : [];
        if (empty($getTestRes))
        {
            return self::Fail('化验项不存在', 40000);
        }
        if (!empty($testStatus) && $getTestRes['status'] != $testStatus)
        {
            return self::Fail('化验项状态已发生变化，不可操作', 40000);
        }

        return self::Success($getTestRes);
    }

    /**
     * 验证化验项是否可操作：开始检测、录入检测员、填写报告结果
     * 1、化验项有效
     * 2、病历未结束（操作人可以是非诊断医生）
     *
     * @param int   $testId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCheckTestOperable(int $testId, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('验证化验项操作，缺少化验ID必选参数', 400);
        }

        $getTestRes = self::GetValidTest($testId, $publicParams);
        if ($getTestRes->isFail())
        {
            return $getTestRes;
        }

        $getTestRes = $getTestRes->getData();

        // 获取关联的病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($getTestRes['case_id'], $publicParams, withDoctorId: false);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 病历是否已经结束
        $getCaseRes = $getCaseRes->getData();
        if (!empty($getCaseRes['finished']))
        {
            return self::Fail('关联病历已结束诊断，不可操作', 40006);
        }

        return self::Success($getTestRes);
    }

    /**
     * 获取化验详情
     *
     * @param int   $testId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTestDetail(int $testId, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('获取化验项详情，缺少化验ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取化验项详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取化验项详情，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取化验项详情，缺少医生ID必选参数', 400);
        }

        // 获取化验信息
        $getTestInfoRes = TestModel::getTestListData(['testId' => $testId, 'hospitalId' => $hospitalId]);
        if (empty($getTestInfoRes) || empty($getTestInfoRes['data']))
        {
            return self::Fail('查看的化验项不存在', 40000);
        }

        $getTestInfoRes = current($getTestInfoRes['data']);

        // 格式化化验信息
        $getFormatTestRes = self::FormatTestInfo([$getTestInfoRes]);
        if ($getFormatTestRes->isFail())
        {
            return $getFormatTestRes;
        }

        // 验证化验关联的病历信息
        $caseId           = $getTestInfoRes['case_id'];
        $getFormatTestRes = current($getFormatTestRes->getData());
        $getCaseRes       = CaseLogic::GetValidCaseById($caseId, $publicParams, false, false);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 化验项非作废，并且未完结病历，并且是本医院和当前医生的病历才可以编辑
        $getCaseRes = $getCaseRes->getData();
        if (!empty($getTestInfoRes['status']) && empty($getCaseRes['finished']) && $getCaseRes['status'] == 1 && $getCaseRes['hospital_id'] == $hospitalId && $getCaseRes['doctor_id'] == $doctorId)
        {
            $getFormatTestRes['editAble'] = true;
        }

        // 获取化验相关结果
        $getTestResultRes = self::GetTestResultByTestIds([$testId], $publicParams);
        if ($getTestResultRes->isFail())
        {
            return $getTestResultRes;
        }

        $getTestResultRes = $getTestResultRes->getData();
        if (!empty($getTestResultRes[$testId]))
        {
            $getFormatTestRes['result'] = $getTestResultRes[$testId];
        }

        return self::Success($getFormatTestRes);
    }

    /**
     * 开始检测
     *
     * @param int   $testId
     * @param array $testerIds
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function StartTest(int $testId, array $testerIds, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('开始检测化验，缺少化验ID参数', 400);
        }
        if (empty($testerIds))
        {
            return self::Fail('开始检测化验，至少需要一个检测员', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('开始检测化验，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = Arr::get($publicParams, '_userId');
        if (empty($hospitalId))
        {
            return self::Fail('开始检测化验，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('开始检测化验，缺少用户ID必选参数', 400);
        }

        // 根据化验编码查询化验记录
        $getTestRes = self::GetCheckTestOperable($testId, $publicParams);
        if ($getTestRes->isFail())
        {
            return $getTestRes;
        }

        // 已经开始检测、存在结果状态
        $getTestRes = $getTestRes->getData();
        if ($getTestRes['start_status'] == 1)
        {
            return self::Fail('化验已开始检测，不可重复开始', 40002);
        }
        if ($getTestRes['result_status'] == 1)
        {
            return self::Fail('化验已存在结果，不可重复开始', 40003);
        }

        // 开始检测
        try
        {
            DB::beginTransaction();

            // 录入检测员
            $verifyTesterIdsRes = self::TestFollowTester($testId, $testerIds, $publicParams);
            if ($verifyTesterIdsRes->isFail())
            {
                DB::rollBack();

                return $verifyTesterIdsRes;
            }

            // 更新化验为开始检测状态
            TestModel::updateOne($testId, ['start_status' => 1, 'start_date' => getCurrentTimeWithMilliseconds()]);

            // 增加处方商品化验使用数量
            RecipeItemModel::on()
                           ->where(['uid' => $getTestRes['recipe_item_uid']])
                           ->whereColumn('used_quantity', '<', 'quantity')
                           ->increment('used_quantity');

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 开始检测化验异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('开始检测化验异常', 40004);
        }
    }

    /**
     * 录入化验检测员
     *
     * @param int   $testId
     * @param array $testerIds
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function TestFollowTester(int $testId, array $testerIds, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('录入化验检测员，缺少化验ID必选参数', 400);
        }
        if (empty($testerIds))
        {
            return self::Fail('录入化验检测员，缺少检测员必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('录入化验检测员，缺少公共参数', 400);
        }

        $testerIds = array_filter($testerIds);
        if (empty($testerIds))
        {
            return self::Fail('化验检测员必选', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('录入化验检测员，缺少医院ID必选参数', 400);
        }

        // 获取化验信息
        $getTestRes = self::GetCheckTestOperable($testId, $publicParams);
        if ($getTestRes->isFail())
        {
            return $getTestRes;
        }

        // 获取化验关联的处方ID、商品ID
        $getTestRes   = $getTestRes->getData();
        $testRecipeId = $getTestRes['recipe_id'];
        $testItemId   = $getTestRes['item_id'];

        // 验证检测员是否存在
        $getTesterRes = HospitalUserModel::getHospitalUsers($hospitalId, $testerIds);
        if ($getTesterRes->isEmpty())
        {
            return self::Fail('化验检测员不存在', 40001);
        }

        // 部分检测员不存在
        $getTesterIds  = $getTesterRes->pluck('user_id')
                                      ->unique()
                                      ->toArray();
        $diffTesterIds = array_diff($testerIds, $getTesterIds);
        if (!empty($diffTesterIds))
        {
            return self::Fail('部分化验检测员不存在', 40001);
        }

        // 获取该化验项存在的检测员
        $getTestTesterRes = TestsTesterModel::getData(where   : ['test_id' => $testId],
                                                      orderBys: ['tester_level' => 'asc'],
                                                      keyBy   : 'tester_level');


        $insertData = [];
        $updateData = [];
        foreach ($testerIds as $level => $userId)
        {
            // 当前级别是否存在检测员
            $curOldTesterInfo = $getTestTesterRes[$level] ?? [];

            // 无效检测员
            if (empty($userId) && (empty($curOldTesterInfo) || empty($curOldTesterInfo['tester_status'])))
            {
                continue;
            }

            // 修改旧的检测员记录
            if (!empty($curOldTesterInfo))
            {
                $updateData[] = [
                    'where' => ['id' => $curOldTesterInfo['id']],
                    'data'  => [
                        'user_id'       => $userId,
                        'tester_status' => $userId > 0 ? 1 : 0,
                    ],
                ];
            }
            else
            {
                // 新增当前级别与检测员
                $insertData[] = [
                    'test_id'       => $testId,
                    'recipe_id'     => $testRecipeId,
                    'item_id'       => $testItemId,
                    'user_id'       => $userId,
                    'tester_level'  => $level,
                    'tester_status' => 1,
                ];
            }

            unset($getTestTesterRes[$level]);
        }

        // 旧的检测员存在的修改为无效
        if (!empty($getTestTesterRes))
        {
            $updateData = array_merge($updateData, array_map(function ($item) {
                return [
                    'where' => ['id' => $item['id']],
                    'data'  => [
                        'tester_status' => 0,
                    ],
                ];
            }, $getTestTesterRes));
        }

        // 没有需要更新的数据
        if (empty($insertData) && empty($updateData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            if (!empty($insertData))
            {
                TestsTesterModel::insert($insertData);
            }

            if (!empty($updateData))
            {
                foreach ($updateData as $updateItem)
                {
                    TestsTesterModel::updateOne($updateItem['where']['id'], $updateItem['data']);
                }
            }

            // 更新化验标记有检测员
            if ($getTestRes['is_tester'] != 1)
            {
                TestModel::updateOne($testId, ['is_tester' => 1]);
            }

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 录入化验检测员异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('录入化验检测员异常', 40004);
        }

        return self::Success();
    }

    /**
     * 保存化验结果
     *
     * @param int   $testId
     * @param array $resultParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function SaveTestResult(int $testId, array $resultParams, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('保存化验结果，缺少化验ID必选参数', 400);
        }
        if (empty($resultParams))
        {
            return self::Fail('保存化验结果，缺少结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存化验结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存化验结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存化验结果，缺少用户ID必选参数', 400);
        }

        // 业务参数
        $resultType = Arr::get($resultParams, 'resultType', '');
        if (empty($resultType) || !TestOrImageResultTypeEnum::exists($resultType))
        {
            return self::Fail('保存化验结果，结果类型参数错误', 400);
        }

        // 获取化验信息
        $getTestRes = self::GetCheckTestOperable($testId, $publicParams);
        if ($getTestRes->isFail())
        {
            return $getTestRes;
        }

        // 化验状态是否可以录入结果
        $getTestRes = $getTestRes->getData();
        if ($getTestRes['start_status'] != 1)
        {
            return self::Fail('化验未开始检测，不可录入结果', 40005);
        }

        // 获取关联的病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($getTestRes['case_id'], $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 病历是否已经结束
        $getCaseRes = $getCaseRes->getData();
        if (!empty($getCaseRes['finished']))
        {
            return self::Fail('病历已结束诊断，不可录入结果', 40006);
        }

        try
        {
            DB::beginTransaction();

            // 匹配填报结果类型并保存 @formatter:off
            match ($resultType)
            {
                'text'            => $getDealRes = self::SaveTestResultDesc($testId, $resultParams, $publicParams),
                'image'           => $getDealRes = self::SaveTestResultImage($testId, $resultParams, $publicParams),
                'file'            => $getDealRes = self::SaveTestResultFile($testId, $resultParams, $publicParams),
                'templateList'    => $getDealRes = self::SaveTestResultTemplateList($testId, $getTestRes['item_id'], $resultParams, $publicParams),
                'templateDetail'  => $getDealRes = self::SaveTestResultTemplateDetail($testId, $getTestRes['item_id'], $resultParams, $publicParams),
            };
            // @formatter:on

            if ($getDealRes->isFail())
            {
                DB::rollBack();

                return $getDealRes;
            }

            // 更新化验结果状态为有结果
            if ($getTestRes['result_status'] != 1)
            {
                TestModel::updateOne($testId, ['result_status' => 1]);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' [统一保存处]保存化验结果异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('[统一保存处]保存化验结果异常', 40014);
        }
    }

    /**
     * 保存化验结果-文字
     *
     * @param int   $testId
     * @param array $resultDescParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function SaveTestResultDesc(int $testId, array $resultDescParams, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('保存化验结果，缺少化验ID必选参数', 400);
        }
        if (empty($resultDescParams))
        {
            return self::Fail('保存化验结果，缺少文字结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存化验结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存化验结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存化验结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultDescParams['textResult']))
        {
            return self::Fail('保存化验结果，缺少文字结果必选参数', 400);
        }

        // 文字结果
        $resultDesc = trim($resultDescParams['textResult']);

        // 获取现有的化验文字结果
        $getDescResultRes = TestsResultsModel::getData(where: ['test_id' => $testId, 'status' => 1]);

        // 无任何修改
        if (empty($getDescResultRes) && empty($resultDesc))
        {
            return self::Success();
        }

        // 旧的文字结果id
        $oldResultDescId = 0;
        if (!empty($getDescResultRes))
        {
            $oldResultDescId = current($getDescResultRes)['id'];

            // 如果新的结果为空，那么直接把文字结果记录给修改无效
            if (empty($resultDesc))
            {
                $newDescData = [
                    'status' => 0,
                ];
            }
            else
            {
                $newDescData = [
                    'result_desc' => addslashes($resultDesc),
                ];
            }
        }
        else
        {
            $newDescData = [
                'test_id'     => $testId,
                'result_desc' => addslashes($resultDesc),
                'created_by'  => $userId,
            ];
        }

        try
        {
            DB::beginTransaction();

            if (!empty($oldResultDescId))
            {
                TestsResultsModel::updateOne($oldResultDescId, $newDescData);
            }
            else
            {
                TestsResultsModel::insertOne($newDescData);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存化验结果-文字结果异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存化验结果异常', 40014);
        }
    }

    /**
     * 保存化验结果-图片
     *
     * @param int   $testId
     * @param array $resultImageParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function SaveTestResultImage(int $testId, array $resultImageParams, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('保存化验结果，缺少化验ID必选参数', 400);
        }
        if (empty($resultImageParams))
        {
            return self::Fail('保存化验结果，缺少图片结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存化验结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存化验结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存化验结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultImageParams['imageList']))
        {
            return self::Fail('保存化验结果，缺少图片结果必选参数', 400);
        }

        // 业务参数, 化验结果图片
        $newImageResultList = Arr::get($resultImageParams, 'imageList', []);

        // 获取现有的化验图片结果
        $getImageResultRes = TestsResultsImageModel::getData(where: ['test_id' => $testId, 'status' => 1], keyBy: 'id');

        // 化验新的图片结果
        $insertImageData = [];
        $deleteImageIds  = [];
        foreach ($newImageResultList as $imageItem)
        {
            // id为0则代表新增，反之代表图片结果id
            $curImageId = $imageItem['id'] ?? 0;
            if (empty($curImageId))
            {
                $insertImageData[] = [
                    'test_id'    => $testId,
                    'image_name' => $imageItem['name'],
                    'image_url'  => $imageItem['url'],
                    'created_by' => $userId,
                ];
                continue;
            }

            // 旧的图片结果还存在，过滤
            if (!empty($getImageResultRes[$curImageId]))
            {
                unset($getImageResultRes[$curImageId]);
            }
        }

        // 旧的图片结果还存在多余，说明新的结果不存在，删除
        if (!empty($getImageResultRes))
        {
            $deleteImageIds = array_keys($getImageResultRes);
        }

        try
        {
            DB::beginTransaction();

            // 新增图片结果
            if (!empty($insertImageData))
            {
                TestsResultsImageModel::insert($insertImageData);
            }

            // 删除图片结果
            if (!empty($deleteImageIds))
            {
                TestsResultsImageModel::on()
                                      ->whereIn('id', $deleteImageIds)
                                      ->update([
                                                   'status'     => 0,
                                                   'deleted_by' => $userId,
                                                   'deleted_at' => getCurrentTimeWithMilliseconds()
                                               ]);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存化验结果-图片结果异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存化验结果异常', 40014);
        }
    }

    /**
     * 保存化验结果-文件
     *
     * @param int   $testId
     * @param array $resultFileParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function SaveTestResultFile(int $testId, array $resultFileParams, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('保存化验结果，缺少化验ID必选参数', 400);
        }
        if (empty($resultFileParams))
        {
            return self::Fail('保存化验结果，缺少结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存化验结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存化验结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存化验结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultFileParams['fileList']))
        {
            return self::Fail('保存化验结果，缺少文件结果必选参数', 400);
        }

        // 业务参数, 化验文件结果
        $newFileResultList = Arr::get($resultFileParams, 'fileList', []);

        // 获取现有的化验文件结果
        $getFileResultRes = TestsResultsFilesModel::getData(where: ['test_id' => $testId, 'status' => 1], keyBy: 'id');

        // 化验新的文件结果
        $insertFileData = [];
        $deleteFileIds  = [];
        foreach ($newFileResultList as $fileItem)
        {
            // id为0则代表新增，反之代表文件结果id
            $curFileId = $fileItem['id'] ?? 0;
            if (empty($curFileId))
            {
                $insertFileData[] = [
                    'test_id'    => $testId,
                    'file_name'  => $fileItem['name'],
                    'file_url'   => $fileItem['url'],
                    'created_by' => $userId,
                ];
                continue;
            }

            // 旧的文件结果还存在，过滤
            if (!empty($getFileResultRes[$curFileId]))
            {
                unset($getFileResultRes[$curFileId]);
            }
        }

        // 旧的文件结果还存在多余，说明新的结果不存在，删除
        if (!empty($getFileResultRes))
        {
            $deleteFileIds = array_keys($getFileResultRes);
        }

        try
        {
            DB::beginTransaction();

            // 新增文件结果
            if (!empty($insertFileData))
            {
                TestsResultsFilesModel::insert($insertFileData);
            }

            // 删除文件结果
            if (!empty($deleteFileIds))
            {
                TestsResultsFilesModel::on()
                                      ->whereIn('id', $deleteFileIds)
                                      ->update([
                                                   'status'     => 0,
                                                   'deleted_by' => $userId,
                                                   'deleted_at' => getCurrentTimeWithMilliseconds()
                                               ]);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存化验结果-文件结果异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存化验结果异常', 40014);
        }
    }

    /**
     * 保存化验结果-模版列表
     *
     * @param int   $testId
     * @param int   $testItemId
     * @param array $resultTemplateListParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function SaveTestResultTemplateList(int $testId, int $testItemId, array $resultTemplateListParams, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('保存化验结果，缺少化验ID必选参数', 400);
        }
        if (empty($testItemId))
        {
            return self::Fail('保存化验结果，缺少化验项ID必选参数', 400);
        }
        if (empty($resultTemplateListParams))
        {
            return self::Fail('保存化验结果，缺少结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存化验结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存化验结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存化验结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultTemplateListParams['templateList']))
        {
            return self::Fail('保存化验结果，缺少模版报告必选参数', 400);
        }

        // 业务参数, 报告单结果
        $newTemplateList = Arr::get($resultTemplateListParams, 'templateList', []);

        // uid字段为空则说明是新增的，新增需要填写模版报告的详情信息。通过结果类型为“模版详情”保存。
        if (array_any($newTemplateList, fn($templateParam) => empty($templateParam['uid'])))
        {
            return self::Fail('新增模版报告结果，请在右侧详情页填写后保存', 40014);
        }

        // 获取现有的化验模版
        $getOldResultRes = TestsResultsReportModel::getData(where: ['test_id' => $testId, 'status' => 1], keyBy: 'id');

        foreach ($getOldResultRes as $oldKey => $oldResult)
        {
            foreach ($newTemplateList as $newKey => $newResult)
            {
                if ($oldResult['uid'] == $newResult['uid'])
                {
                    unset($getOldResultRes[$oldKey]);
                    unset($newTemplateList[$newKey]);
                    break;
                }
            }
        }

        // 无任何修改
        if (empty($getOldResultRes) && empty($newTemplateList))
        {
            return self::Success();
        }

        // 不可通过此保存结果方式来新增新的化验模版
        if (!empty($newTemplateList))
        {
            return self::Fail('新增模版报告结果，请在右侧详情页填写后保存', 40014);
        }

        // 旧的模版结果还存在多余，说明新的结果不存在，删除
        $deleteTemplateIds = [];
        if (!empty($getOldResultRes))
        {
            $deleteTemplateIds = array_keys($getOldResultRes);
        }
        if (empty($deleteTemplateIds))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 删除模版
            TestsResultsReportModel::on()
                                   ->whereIn('id', $deleteTemplateIds)
                                   ->update([
                                                'status'     => 0,
                                                'deleted_by' => $userId,
                                                'deleted_at' => getCurrentTimeWithMilliseconds()
                                            ]);

            // 删除模版详情
            TestsResultsReportDetailModel::on()
                                         ->whereIn('report_result_id', $deleteTemplateIds)
                                         ->update([
                                                      'status'     => 0,
                                                      'deleted_by' => $userId,
                                                      'deleted_at' => getCurrentTimeWithMilliseconds()
                                                  ]);

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存化验结果-模版列表异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存化验结果异常', 40014);
        }

        return self::Success();
    }

    /**
     * 保存化验结果-模版报告详情
     *
     * @param int   $testId
     * @param int   $testItemId
     * @param array $resultTemplateInfoParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function SaveTestResultTemplateDetail(int $testId, int $testItemId, array $resultTemplateInfoParams, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('保存化验结果，缺少化验ID必选参数', 400);
        }
        if (empty($testItemId))
        {
            return self::Fail('保存化验结果，缺少化验项商品ID必选参数', 400);
        }
        if (empty($resultTemplateInfoParams))
        {
            return self::Fail('保存化验结果，缺少结果详情必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存化验结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存化验结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存化验结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultTemplateInfoParams['templateInfo']))
        {
            return self::Fail('保存化验结果，缺少模版详情必选参数', 400);
        }

        // 业务参数-模版信息
        $templateInfoParams = $resultTemplateInfoParams['templateInfo'];
        $newTemplateUid     = Arr::get($templateInfoParams, 'template.uid', '');

        // 业务参数-模版结果信息
        $newReportResultUid  = Arr::get($templateInfoParams, 'result.uid', '');
        $newTemplatePosition = Arr::get($templateInfoParams, 'result.position', '');
        $newTemplateMode     = Arr::get($templateInfoParams, 'result.mode', '');
        $newDimensionsResult = Arr::get($templateInfoParams, 'result.dimensions', []);
        if (empty($newTemplateUid) || empty($newDimensionsResult))
        {
            return self::Fail('保存化验结果，缺少化验结果模版必选参数', 400);
        }

        // 获取化验信息
        $getTestRes = self::GetValidTest($testId, $publicParams);
        if ($getTestRes->isFail())
        {
            return $getTestRes;
        }

        // 病历ID
        $caseId = $getTestRes->getData('case_id', 0);

        // 获取模版信息
        $getItemRelationTemplateRes = TestsRelationTemplatesModel::getTestItemRelationTemplates(itemId         : $testItemId,
                                                                                                templateUids   : [$newTemplateUid],
                                                                                                withValidStatus: false);
        $getItemRelationTemplateRes = $getItemRelationTemplateRes ? current($getItemRelationTemplateRes) : [];
        if (empty($getItemRelationTemplateRes))
        {
            return self::Fail('化验报告模版不存在', 40006);
        }

        // 获取模版维度信息
        $getDimensionRes = TestsReportTemplatesDimensionsModel::getData(where: ['template_id' => $getItemRelationTemplateRes['template_id']],
                                                                        keyBy: 'id');
        if (empty($getDimensionRes))
        {
            return self::Fail('化验报告模版维度不存在', 40006);
        }

        // 获取模版维度下指标信息
        $dimensionIds     = array_column($getDimensionRes, 'id');
        $getIndicatorsRes = TestsReportTemplatesIndicatorsModel::getData(where  : ['template_id' => $getItemRelationTemplateRes['template_id']],
                                                                         whereIn: ['template_dimension_id' => $dimensionIds],
                                                                         keyBy  : 'id');
        if (empty($getIndicatorsRes))
        {
            return self::Fail('化验报告模版指标不存在', 40006);
        }

        // 模版信息
        $templateId             = $getItemRelationTemplateRes['template_id'];
        $templateStatus         = $getItemRelationTemplateRes['template_status'];
        $templateIsNeedPosition = $getItemRelationTemplateRes['template_is_need_position'];
        $templateIsNeedMode     = $getItemRelationTemplateRes['template_is_need_mode'];

        // 验证部位和取样必填时的情况
        if (!empty($templateIsNeedPosition) && empty($newTemplatePosition))
        {
            return self::Fail('化验结果模版，检查部位必填', 40007);
        }
        if (!empty($templateIsNeedMode) && empty($newTemplateMode))
        {
            return self::Fail('化验结果模版，采样方式必填', 40008);
        }

        // 获取存在的化验报告结果，编辑情况下：根据结果UID获取，如果新增：根据模版ID和化验ID获取是否存在
        $getWhere = ['test_id' => $testId, 'template_id' => $templateId, 'status' => 1];
        if (!empty($newReportResultUid))
        {
            $getWhere['uid']  = $newReportResultUid;
            $getTestReportRes = TestsResultsReportModel::getData(where: $getWhere);
            $getTestReportRes = $getTestReportRes ? current($getTestReportRes) : [];
            if (empty($getTestReportRes))
            {
                return self::Fail('化验结果报告单不存在', 40006);
            }
        }
        else
        {
            $getTestReportRes = TestsResultsReportModel::getData(where: $getWhere);
            $getTestReportRes = $getTestReportRes ? current($getTestReportRes) : [];

            // 如果已经存在此模版报告，那么限制不可以在新增此模版报告
            if (!empty($getTestReportRes))
            {
                return self::Fail('此模版报告已经存在，不可重复新增，请修改存在的报告单结果', 40014);
            }
        }

        // 如果此模版为新使用，验证模版状态、化验项关联模版状态是否有效。如果此模版存在结果，那么不判断模版状态是否有效
        if (empty($getTestReportRes) && (empty($templateStatus) || empty($getItemRelationTemplateRes['status'])))
        {
            return self::Fail('化验结果模版已失效，不可使用', 40006);
        }

        // 如果化验项存在报告单结果，获取报告单结果明细。并且设置报告单更新的信息。反之不存在则作为新增报告单
        $oldReportResultId = 0;
        if (!empty($getTestReportRes))
        {
            // 存在的报告单结果id
            $oldReportResultId = $getTestReportRes['id'];

            // 获取报告单明细
            $getTestReportDetailRes = TestsResultsReportDetailModel::getData(where: [
                                                                                        'report_result_id' => $oldReportResultId,
                                                                                        'status'           => 1
                                                                                    ],
                                                                             keyBy: 'template_indicator_id');

            // 设置要更新的报告单信息
            $reportResult = [
                'position' => $newTemplatePosition,
                'mode'     => $newTemplateMode,
            ];
        }
        else
        {
            // 设置要新增的报告单信息
            $reportResult = [
                'uid'         => generateUUID(),
                'case_id'     => $caseId,
                'test_id'     => $testId,
                'template_id' => $templateId,
                'position'    => $newTemplatePosition,
                'mode'        => $newTemplateMode,
                'created_by'  => $userId,
            ];
        }

        // 需要新增、更新的报告单详情
        $insertIndicatorsResult = [];
        $updateIndicatorsResult = [];
        foreach ($newDimensionsResult as $curDimensionsInfo)
        {
            // 模版下的维度是否存在
            if (empty($curDimensionsInfo['dimensionId']) || empty($getDimensionRes[$curDimensionsInfo['dimensionId']]) || !is_array($curDimensionsInfo['indicators']) || empty($curDimensionsInfo['indicators']))
            {
                return self::Fail('化验结果模版，维度参数错误', 40009);
            }

            foreach ($curDimensionsInfo['indicators'] as $curIndicatorInfo)
            {
                // 模版下维度指标是否存在
                if (empty($curIndicatorInfo['indicatorId']) || empty($getIndicatorsRes[$curIndicatorInfo['indicatorId']]) || !isset($curIndicatorInfo['indicatorResult']))
                {
                    return self::Fail('化验结果模版，指标参数错误', 40010);
                }

                // 存在此指标结果，则更新
                if (isset($getTestReportDetailRes[$curIndicatorInfo['indicatorId']]))
                {
                    $updateIndicatorsResult[] = [
                        'where' => [
                            'id' => $getTestReportDetailRes[$curIndicatorInfo['indicatorId']]['id']
                        ],
                        'data'  => [
                            'result'      => trim($curIndicatorInfo['indicatorResult']),
                            'result_tips' => trim($curIndicatorInfo['indicatorResultTips']),
                        ],
                    ];
                }
                else
                {
                    if (trim($curIndicatorInfo['indicatorResult']) == '')
                    {
                        continue;
                    }

                    $insertIndicatorsResult[] = [
                        'report_result_id'      => $oldReportResultId,
                        'test_id'               => $testId,
                        'template_id'           => $templateId,
                        'template_dimension_id' => $curDimensionsInfo['dimensionId'],
                        'template_indicator_id' => $curIndicatorInfo['indicatorId'],
                        'result'                => trim($curIndicatorInfo['indicatorResult']),
                        'result_tips'           => trim($curIndicatorInfo['indicatorResultTips']),
                        'created_by'            => $userId,
                    ];
                }
            }
        }

        // 无任何修改
        if (empty($insertIndicatorsResult) && empty($updateIndicatorsResult))
        {
            return self::Fail('化验结果模版报告，指标结果为空', 40011);
        }

        if (empty($getTestReportRes) && empty($reportResult) && empty($insertIndicatorsResult))
        {
            return self::Fail('化验结果模版报告，数据有误', 40012);
        }

        try
        {
            DB::beginTransaction();

            // 更新报告单
            if (!empty($oldReportResultId))
            {
                TestsResultsReportModel::updateOne($oldReportResultId, $reportResult);
            }
            else
            {
                $oldReportResultId = TestsResultsReportModel::insertOne($reportResult);
            }

            // 新增报告单详情
            if (!empty($insertIndicatorsResult))
            {
                foreach ($insertIndicatorsResult as $key => $insertItem)
                {
                    $insertIndicatorsResult[$key]['report_result_id'] = $oldReportResultId;
                }

                TestsResultsReportDetailModel::insert($insertIndicatorsResult);
            }

            // 更新报告单详情
            if (!empty($updateIndicatorsResult))
            {
                foreach ($updateIndicatorsResult as $updateItem)
                {
                    TestsResultsReportDetailModel::updateOne($updateItem['where']['id'], $updateItem['data']);
                }
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存化验结果-模版详情异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存化验结果异常', 40014);
        }
    }

    /**
     * 获取化验项关联可用的模版列表
     *
     * @param int   $testId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTestRelationTemplateList(int $testId, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('获取化验项关联模版，缺少化验ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取化验项关联模版，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取化验项关联模版，缺少医院ID必选参数', 400);
        }

        // 获取化验信息
        $getTestRes = self::GetValidTest($testId, $publicParams, true, null);
        if ($getTestRes->isFail())
        {
            return $getTestRes;
        }

        // 获取化验项关联的模版
        $itemId                 = $getTestRes->getData('item_id', 0);
        $getRelationTemplateRes = TestsRelationTemplatesModel::getTestItemRelationTemplates($itemId);
        if (empty($getRelationTemplateRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取已存在的模版结果，如果已经存在不可以在新增此模版。可以编辑存在的报告单
        $getExistTemplateRes = TestsResultsReportModel::getData(where: ['test_id' => $testId, 'status' => 1],
                                                                keyBy: 'template_id');

        $returnResult = [];
        foreach ($getRelationTemplateRes as $relationInfo)
        {
            $returnResult[] = [
                'uid'     => $relationInfo['template_uid'],
                'name'    => $relationInfo['template_name'],
                'useAble' => empty($getExistTemplateRes[$relationInfo['template_id']]),
            ];
        }

        return self::Success(['total' => count($returnResult), 'data' => $returnResult]);
    }

    /**
     * 获取化验项关联模版报告单
     *
     * @param int   $testId
     * @param array $templateInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTestTemplateReport(int $testId, array $templateInfo, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('获取化验项关联模版报告单，缺少化验ID必选参数', 400);
        }
        if (empty($templateInfo))
        {
            return self::Fail('获取化验项关联模版报告单，缺少报告单结果ID、模版ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取化验项关联模版报告单，缺少公共必选参数', 400);
        }

        // 业务参数
        $reportResultId = Arr::get($templateInfo, 'reportResultId', 0);
        $templateId     = Arr::get($templateInfo, 'templateId', 0);
        if (empty($reportResultId) && empty($templateId))
        {
            return self::Fail('获取化验项关联模版报告单，缺少报告单结果ID、模版ID必选参数', 400);
        }

        // 获取化验信息
        $getTestRes = self::GetValidTest($testId, $publicParams, false, null);
        if ($getTestRes->isFail())
        {
            return $getTestRes;
        }

        // 获取模版结果信息
        $getTestReportRes = [];
        if (!empty($reportResultId))
        {
            $getTestReportRes = TestsResultsReportModel::getData(where: [
                                                                            'id'      => $reportResultId,
                                                                            'test_id' => $testId,
                                                                            'status'  => 1
                                                                        ]);
            $getTestReportRes = $getTestReportRes ? current($getTestReportRes) : [];
            if (empty($getTestReportRes))
            {
                return self::Fail('化验结果报告单不存在', 40006);
            }

            $templateId = $getTestReportRes['template_id'];
        }

        // 获取模版信息
        $getTemplateRes = [];
        if (!empty($templateId))
        {
            $getTemplateRes = TestsReportTemplatesModel::getOne($templateId);
        }
        if (empty($getTemplateRes))
        {
            return self::Fail('查看的化验模版不存在', 40006);
        }

        // 模版需要警戒值，获取宠物基本信息
        $petCategoryId   = 0;
        $petCategoryName = '';
        if (!empty($getTemplateRes['is_need_reference']))
        {
            $getPetRes     = MemberPetsModel::getOne($getTestRes->getData('pet_id', 0));
            $petCategoryId = $getPetRes['category_id'] ?? 0;
        }

        // 化验项报告单详情
        $getTestReportDetailRes = TestsResultsReportDetailModel::getTestResultReportDetail($testId,
                                                                                           $templateId,
                                                                                           $reportResultId);
        if (empty($getTestReportDetailRes))
        {
            return self::Fail('化验项报告单详情不存在', 40006);
        }

        // 修改数组为集合
        $getTestReportDetailRes = collect($getTestReportDetailRes);

        // 获取模版维度信息
        $dimensionIds    = $getTestReportDetailRes->pluck('template_dimension_id')
                                                  ->unique()
                                                  ->toArray();
        $getDimensionRes = TestsReportTemplatesDimensionsModel::getManyByIds($dimensionIds);

        // 组合维度下指标信息
        $dimensionsResult = [];
        foreach ($getDimensionRes as $curDimension)
        {
            $tmpDimensionData = [
                "dimensionId"   => $curDimension['id'],
                "dimensionName" => $curDimension['name'],
                "indicators"    => [],
            ];

            // 如果该维度下没有指标，则过滤当前维度
            $curDimensionIndicatorResult = $getTestReportDetailRes->where('template_dimension_id',
                                                                          '=',
                                                                          $curDimension['id']);
            if ($curDimensionIndicatorResult->isEmpty())
            {
                continue;
            }

            // 如果该维度已删除，并且该维度下面的指标没有已经填过值得，则跳过
            $curIsHaveReportResult = $curDimensionIndicatorResult->some(fn($item) => data_get($item,
                                                                                              'report_detail.id') > 0);
            if (empty($curDimension['status']) && empty($curIsHaveReportResult))
            {
                continue;
            }

            foreach ($curDimensionIndicatorResult->toArray() as $value)
            {
                // 该指标下没有结果，并且该指标已删除，则跳过
                if (empty($value['report_detail']) && empty($value['status']))
                {
                    continue;
                }

                // 当前宠物参考值
                $curReferenceValue = '';
                if ($petCategoryId == 1)
                {
                    $petCategoryName   = '犬';
                    $curReferenceValue = $value['reference_dog_value'];
                }
                elseif ($petCategoryId == 2)
                {
                    $petCategoryName   = '猫';
                    $curReferenceValue = $value['reference_cat_value'];
                }

                $tmpDimensionData['indicators'][] = [
                    "indicatorId"         => $value['id'],
                    "indicatorName"       => $value['name'],
                    "indicatorUnit"       => $value['unit'],
                    'referenceValue'      => $curReferenceValue,
                    'warningMinValue'     => $value['warning_min_value'],
                    'warningMaxValue'     => $value['warning_max_value'],
                    "indicatorResult"     => $value['report_detail']['result'] ?? '',
                    "indicatorResultTips" => $value['report_detail']['result_tips'] ?? '',
                ];
            }

            $dimensionsResult[] = $tmpDimensionData;
        }

        $returnData = [
            'testCode'    => $getTestRes->getData('test_code'),
            'template'    => [
                'uid'  => $getTemplateRes['uid'],
                'name' => $getTemplateRes['name'],
            ],
            'result'      => [
                'uid'                 => $getTestReportRes['uid'] ?? '',
                'name'                => $getTemplateRes['name'],
                'isNeedPosition'      => $getTemplateRes['is_need_position'],
                'position'            => $getTestReportRes['position'] ?? '',
                'isNeedMode'          => $getTemplateRes['is_need_mode'],
                'mode'                => $getTestReportRes['mode'] ?? '',
                'isNeedWarning'       => $getTemplateRes['is_need_warning'],
                'isMultiDimension'    => $getTemplateRes['is_multi_dimension'],
                'isNeedReference'     => $getTemplateRes['is_need_reference'],
                'indicatorResultTips' => ['+', '-'],
                'dimensions'          => $dimensionsResult,
            ],
            'petCategory' => [
                'id'   => $petCategoryId,
                'name' => $petCategoryName,
            ],
        ];

        return self::Success($returnData);
    }

    /**
     * 批量获取化验结果
     *
     * @param array $testIds
     * @param array $publicParams
     * @param bool  $withReportResult 是否需要返回报告结果
     *
     * @return LogicResult
     */
    public static function GetTestResultByTestIds(array $testIds, array $publicParams, bool $withReportResult = false): LogicResult
    {
        if (empty($testIds))
        {
            return self::Fail('获取化验结果，缺少化验ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取化验结果，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取化验结果，缺少医院ID必选参数', 400);
        }

        // 获取化验项目信息
        $getTestInfoRes = TestModel::getTestItemInfoByTestIds($testIds);
        if (empty($getTestInfoRes))
        {
            return self::Fail('获取化验结果，化验项目不存在', 40000);
        }

        // 获取化验文字结果
        $getDescResultRes = TestsResultsModel::getData(where: ['status' => 1], whereIn: ['test_id' => $testIds]);
        $getDescResultRes = array_column($getDescResultRes, null, 'test_id');

        // 获取化验图片结果
        $getImageResultRes = TestsResultsImageModel::getData(where: ['status' => 1], whereIn: ['test_id' => $testIds]);

        // 获取化验文件结果
        $getFileResultRes = TestsResultsFilesModel::getData(where: ['status' => 1], whereIn: ['test_id' => $testIds]);

        // 获取化验报告结果
        $getReportResultRes = TestsResultsReportModel::getData(where  : ['status' => 1],
                                                               whereIn: ['test_id' => $testIds]);

        // 获取化验报告模版
        if (!empty($getReportResultRes))
        {
            $templateIds = array_unique(array_filter(array_column($getReportResultRes, 'template_id')));
            if (!empty($templateIds))
            {
                $getTemplateRes = TestsReportTemplatesModel::getData(whereIn: ['id' => $templateIds], keyBy: 'id');
            }
        }

        $returnResult = [];
        foreach ($testIds as $testId)
        {
            // 化验项目信息
            $curTestInfo = [];
            foreach ($getTestInfoRes as $testInfo)
            {
                if ($testInfo['id'] != $testId)
                {
                    continue;
                }

                $curTestInfo = [
                    'testCode'   => $testInfo['test_code'],
                    'item'       => [
                        'uid'  => $testInfo['item_uid'],
                        'name' => $testInfo['item_display_name'],
                        'unit' => $testInfo['item_use_unit'],
                    ],
                    'createTime' => formatDisplayDateTime($testInfo['created_at']),
                    'remark'     => $testInfo['remark'] ?? '',
                ];
            }

            // 文字结果
            $curTextResult = $getDescResultRes[$testId]['result_desc'] ?? '';

            // 图片结果
            $curImageResult = [];
            foreach ($getImageResultRes as $imageResult)
            {
                if ($imageResult['test_id'] != $testId)
                {
                    continue;
                }

                $curImageResult[] = [
                    'id'   => $imageResult['id'],
                    'name' => $imageResult['image_name'],
                    'url'  => realPicturePath($imageResult['image_url']),
                ];
            }

            // 文件结果
            $curFileResult = [];
            foreach ($getFileResultRes as $fileResult)
            {
                if ($fileResult['test_id'] != $testId)
                {
                    continue;
                }

                $curFileResult[] = [
                    'id'   => $fileResult['id'],
                    'name' => $fileResult['file_name'],
                    'url'  => realPicturePath($fileResult['file_url']),
                ];
            }

            // 模版结果
            $curReportResult       = [];
            $curReportResultDetail = [];
            foreach ($getReportResultRes as $reportResult)
            {
                if ($reportResult['test_id'] != $testId)
                {
                    continue;
                }

                // 关联的模版信息
                $curTemplateInfo = $getTemplateRes[$reportResult['template_id']] ?? [];
                if (empty($curTemplateInfo))
                {
                    continue;
                }

                $curReportResult[] = [
                    'template' => [
                        'uid'  => $curTemplateInfo['uid'] ?? '',
                        'name' => $curTemplateInfo['name'] ?? '',
                    ],
                    'result'   => [
                        'uid'      => $reportResult['uid'] ?? '',
                        'createAt' => $reportResult['created_at'],
                    ]
                ];

                // 获取报告单详情
                if (!empty($withReportResult))
                {
                    $getReportDetailRes = self::GetTestTemplateReport($testId,
                                                                      ['reportResultId' => $reportResult['id']],
                                                                      $publicParams);
                    if ($getReportDetailRes->isFail())
                    {
                        continue;
                    }

                    $curReportResultDetail[] = $getReportDetailRes->getData();
                }
            }

            $returnResult[$testId] = [
                'testInfo'       => $curTestInfo,
                'textResult'     => $curTextResult,
                'imageList'      => $curImageResult,
                'fileList'       => $curFileResult,
                'templateList'   => $curReportResult,
                'templateDetail' => $curReportResultDetail,
            ];
        }

        return self::Success($returnResult);
    }

    /**
     * 格式化化验信息展示
     *
     * @param array $testLists
     *
     * @return LogicResult
     */
    private static function FormatTestInfo(array $testLists): LogicResult
    {
        if (empty($testLists))
        {
            return self::Success();
        }

        // 获取化验关联的宠物基础信息、宠物对应宠物主基础信息
        $petIds = array_unique(array_column($testLists, 'pet_id'));
        if (!empty($petIds))
        {
            $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
            if ($getPetRes->isFail())
            {
                return $getPetRes;
            }

            $getPetRes = $getPetRes->getData();
        }

        // 获取化验关联的检测员
        $testIds      = array_unique(array_column($testLists, 'id'));
        $getTesterRes = TestsTesterModel::getTestTester($testIds);

        $returnTestList = [];
        foreach ($testLists as $item)
        {
            // 化验关联会员信息
            $curTestMemberInfo = $getPetRes[$item['pet_id']]['memberInfo'] ?? [];

            // 化验关联宠物信息
            $curTestPetInfo = $getPetRes[$item['pet_id']]['petInfo'] ?? [];

            // 化验关联检测员
            $curTesterInfo = [];
            foreach ($getTesterRes as $testerInfo)
            {
                if ($testerInfo['test_id'] != $item['id'])
                {
                    continue;
                }

                $tmpTesterInfo = [
                    'uid'   => $testerInfo['user_uid'],
                    'name'  => $testerInfo['user_name'],
                    'level' => $testerInfo['tester_level'],
                ];

                $curTesterInfo[] = $tmpTesterInfo;
            }

            $tmpTestItem = [
                'testCode'   => $item['test_code'],
                'memberInfo' => $curTestMemberInfo,
                'petInfo'    => $curTestPetInfo,
                'doctor'     => [
                    'uid'  => $item['doctor_uid'],
                    'name' => $item['doctor_name'],
                ],
                'item'       => [
                    'uid'      => $item['item_uid'],
                    'name'     => $item['item_display_name'],
                    'itemName' => $item['item_name'],
                    'unit'     => $item['item_use_unit'],
                ],
                'quantity'   => 1,
                'price'      => formatDisplayNumber($item['price']),
                'status'     => TestOrImageStatusEnum::FormatTestOrImageStatus($item),
                'payStatus'  => [
                    'id'   => $item['is_paid'],
                    'name' => TestOrImagePaidStatusEnum::getDescription($item['is_paid']),
                ],
                'createTime' => formatDisplayDateTime($item['created_at']),
                'tester'     => $curTesterInfo,
                'editAble'   => false, // 默认不可编辑，在化验详情中如果可编辑的话验证通过为true。其它业务处不可编辑
                'remark'     => $item['remark'] ?? '',
            ];

            $returnTestList[] = $tmpTestItem;
        }

        return self::Success($returnTestList);
    }
}
