<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\ItemModel;
use App\Models\StockItemShelfModel;
use App\Models\StockItemShelfReduceModel;
use App\Support\Stock\StockQuantityConversionHelper;
use Arr;
use DB;
use Exception;
use Log;
use Throwable;

class NewStockItemShelfReduceLogic extends Logic
{
    /**
     * 扣减库存主方法
     *
     * @param array $reduceStockParams 需要扣减的商品列表, eg: [['itemId' => 1, 'packQuantity' => 1, 'bulkQuantity' => 2, 'type' => 1, ...]]
     * @param array $publicParams      公共参数
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function reduceStock(array $reduceStockParams, array $publicParams): LogicResult
    {
        if (empty($reduceStockParams))
        {
            return self::Fail('扣减库存，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('扣减库存，缺少公共必选参数', 400);
        }

        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        if (empty($hospitalId))
        {
            return self::Fail('扣减库存，缺少医院ID必选参数', 400);
        }

        $itemIds = array_unique(array_filter(array_column($reduceStockParams, 'itemId')));
        if (empty($itemIds))
        {
            return self::Fail('扣减库存，缺少商品ID', 400);
        }

        $getItemInfoRes = ItemModel::getData(fields: ['id', 'bulk_ratio'], whereIn: ['id' => $itemIds], keyBy: 'id');
        if (count($itemIds) != count($getItemInfoRes))
        {
            return self::Fail('扣减库存，部分商品信息不存在', 43000);
        }

        $getItemPriceRes = StockItemDailyPriceLogic::GetItemNowDailyPrice($itemIds, $publicParams);
        if ($getItemPriceRes->isFail())
        {
            return $getItemPriceRes;
        }
        $priceMap = $getItemPriceRes->getData();

        try
        {
            DB::beginTransaction();

            foreach ($reduceStockParams as $reduceItem)
            {
                $curItemId       = intval(Arr::get($reduceItem, 'itemId', 0));
                $curPackQuantity = intval(Arr::get($reduceItem, 'packQuantity', 0));
                $curBulkQuantity = intval(Arr::get($reduceItem, 'bulkQuantity', 0));

                if (empty($curItemId) || ($curPackQuantity < 0) || ($curBulkQuantity < 0) || ($curPackQuantity == 0 && $curBulkQuantity == 0))
                {
                    throw new Exception('商品ID ' . $curItemId . ' 的扣减数量无效', 400);
                }

                $curItemInfo = $getItemInfoRes[$curItemId] ?? [];
                $bulkRatio   = (int) ($curItemInfo['bulk_ratio'] ?? 0);

                $totalDemandBulk = StockQuantityConversionHelper::convertToTotalBulkQuantity($curPackQuantity, $curBulkQuantity, $bulkRatio);
                if ($totalDemandBulk <= 0)
                {
                    continue;
                }

                $availableShelves = StockItemShelfModel::getData(where: [
                                                                            ['hospital_id', '=', $hospitalId],
                                                                            ['item_id', '=', $curItemId],
                                                                            ['status', '=', 1],
                                                                            [
                                                                                function ($query) {
                                                                                    $nowDate = getNowDateTime('Y-m-d');
                                                                                    $query->where(function ($q) use ($nowDate) {
                                                                                        $q->whereNull('expired_date')
                                                                                          ->orWhere('expired_date', '0000-00-00')
                                                                                          ->orWhere('expired_date', '>', $nowDate);
                                                                                    });
                                                                                }
                                                                            ],
                                                                            [
                                                                                function ($query) {
                                                                                    $query->where('effective_pack_quantity', '>', 0)
                                                                                          ->orWhere('effective_bulk_quantity', '>', 0);
                                                                                }
                                                                            ]
                                                                        ], orderBys: ['expired_date' => 'asc', 'created_at' => 'asc']);

                $totalSupplyBulk = 0;
                foreach ($availableShelves as $shelf)
                {
                    $totalSupplyBulk += StockQuantityConversionHelper::convertToTotalBulkQuantity((int) $shelf['effective_pack_quantity'],
                                                                                                  (int) $shelf['effective_bulk_quantity'],
                                                                                                  $bulkRatio);
                }

                if ($totalSupplyBulk < $totalDemandBulk)
                {
                    throw new Exception('商品ID ' . $curItemId . ' 库存不足', 43001);
                }

                $currentItemPrice = $priceMap[$curItemId] ?? ['packPrice' => 0, 'bulkPrice' => 0];
                $deductionResult  = self::_deductFromShelves($totalDemandBulk, $availableShelves, $bulkRatio, $reduceItem, $publicParams, $currentItemPrice);

                // 使用循环单条更新，代替批量操作，增强兼容性
                foreach ($deductionResult['shelfUpdates'] as $update)
                {
                    StockItemShelfModel::updateOne($update['id'], $update);
                }
                foreach ($deductionResult['reduceRecords'] as $record)
                {
                    StockItemShelfReduceModel::insertOne($record);
                }
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 商品库存扣减异常', ['error' => $throwable->getMessage()]);

            return self::Fail($throwable->getMessage(), $throwable->getCode());
        }
    }

    private static function _deductFromShelves(int $totalDemandBulk, array $availableShelves, int $bulkRatio, array $reduceItem, array $publicParams, array $itemPrice): array
    {
        $shelfUpdates    = [];
        $reduceRecords   = [];
        $remainingDemand = $totalDemandBulk;
        $now             = date('Y-m-d H:i:s');

        $baseReduceRecord = [
            'type'               => Arr::get($reduceItem, 'type', 1),
            'sub_type'           => Arr::get($reduceItem, 'subType', 0),
            'relation_code'      => Arr::get($reduceItem, 'relationCode', ''),
            'relation_id'        => Arr::get($reduceItem, 'relationId', 0),
            'relation_detail_id' => Arr::get($reduceItem, 'relationDetailId', 0),
            'org_id'             => Arr::get($publicParams, '_hospitalOrgId', 0),
            'brand_id'           => Arr::get($publicParams, '_hospitalBrandId', 0),
            'hospital_id'        => Arr::get($publicParams, '_hospitalId', 0),
            'item_id'            => Arr::get($reduceItem, 'itemId', 0),
            'remark'             => Arr::get($reduceItem, 'remark', ''),
            'created_by'         => Arr::get($publicParams, '_userId', 0),
        ];

        foreach ($availableShelves as $shelf)
        {
            if ($remainingDemand <= 0)
            {
                break;
            }

            $shelfPack      = (int) $shelf['effective_pack_quantity'];
            $shelfBulk      = (int) $shelf['effective_bulk_quantity'];
            $shelfTotalBulk = StockQuantityConversionHelper::convertToTotalBulkQuantity($shelfPack, $shelfBulk, $bulkRatio);
            if ($shelfTotalBulk <= 0)
            {
                continue;
            }

            $takeFromShelfBulk = min($remainingDemand, $shelfTotalBulk);

            $deductedFromLoose = min($shelfBulk, $takeFromShelfBulk);
            $shelfBulk         -= $deductedFromLoose;
            $remainsToTake     = $takeFromShelfBulk - $deductedFromLoose;

            if ($remainsToTake > 0 && $shelfPack > 0 && $bulkRatio > 0)
            {
                $packsToBreak = (int) ceil($remainsToTake / $bulkRatio);
                $packsToBreak = min($packsToBreak, $shelfPack);

                $shelfPack          -= $packsToBreak;
                $newlyAvailableBulk = $packsToBreak * $bulkRatio;
                $shelfBulk          += $newlyAvailableBulk;

                $deductedFromPack = min($remainsToTake, $shelfBulk);
                $shelfBulk        -= $deductedFromPack;
            }

            $shelfUpdates[] = [
                'id'                      => $shelf['id'],
                'effective_pack_quantity' => $shelfPack,
                'effective_bulk_quantity' => $shelfBulk,
            ];

            $deductedQuantities = StockQuantityConversionHelper::getPackAndBulkQuantity($takeFromShelfBulk, $bulkRatio);

            $reduceRecords[] = array_merge($baseReduceRecord, [
                'warehouse_id'        => $shelf['warehouse_id'],
                'stock_item_shelf_id' => $shelf['id'],
                'item_barcode'        => $shelf['item_barcode'],
                'pack_quantity'       => $deductedQuantities['packQuantity'],
                'bulk_quantity'       => $deductedQuantities['bulkQuantity'],
                'produce_date'        => $shelf['produce_date'],
                'expired_date'        => $shelf['expired_date'],
                'shelf_life'          => $shelf['shelf_life'],
                'shelf_code'          => $shelf['shelf_code'],
                'pack_price'          => $itemPrice['packPrice'] ?? 0,
                'bulk_price'          => $itemPrice['bulkPrice'] ?? 0,
                'bulk_ratio'          => $bulkRatio,
                'created_at'          => $now,
                'updated_at'          => $now,
            ]);

            $remainingDemand -= $takeFromShelfBulk;
        }

        return ['shelfUpdates' => $shelfUpdates, 'reduceRecords' => $reduceRecords];
    }
}
