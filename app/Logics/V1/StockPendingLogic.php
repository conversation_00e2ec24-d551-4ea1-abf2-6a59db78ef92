<?php

namespace App\Logics\V1;

use Arr;
use App\Enums\PageEnum;
use App\Enums\StockPendingOrderStatusEnum;
use App\Support\Item\ItemHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\UsersModel;
use App\Models\StockPendingOrderModel;
use App\Models\StockPendingOrderDetailModel;

class StockPendingLogic extends Logic
{
    public static function GetCreateStockPendingUserOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取创建拣货出库单用户，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取创建拣货出库单用户，缺少医院ID必选参数', 400);
        }

        $getStockPendingUserRes = StockPendingOrderModel::getData(fields: ['created_by'], where: ['hospital_id' => $hospitalId], group: 'created_by');
        if (empty($getStockPendingUserRes))
        {
            return self::Success();
        }

        $userIds    = array_unique(array_column($getStockPendingUserRes, 'created_by'));
        $getUserRes = UsersModel::getUserByIds($userIds);

        $returnUserOptions = [];
        foreach ($getUserRes as $curUser)
        {
            $returnUserOptions[] = [
                'uid'  => $curUser['uid'],
                'name' => $curUser['name'],
            ];
        }

        return self::Success($returnUserOptions);
    }

    /**
     * 获取拣货出库单列表
     *
     * @param array $searchParams 搜索参数
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function GetPendingOrderLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取拣货出库单列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取拣货出库单列表，缺少医院ID必选参数', 400);
        }

        // 搜索参数处理
        $keywords      = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        $orderCode     = trimWhitespace(Arr::get($searchParams, 'orderCode', ''));
        $startDate     = trimWhitespace(Arr::get($searchParams, 'startDate', ''));
        $endDate       = trimWhitespace(Arr::get($searchParams, 'endDate', ''));
        $createUserUid = trimWhitespace(Arr::get($searchParams, 'createUserUid', ''));
        $status        = Arr::get($searchParams, 'status');
        $page          = intval(Arr::get($searchParams, 'page', PageEnum::DefaultPageIndex->value)) ?: PageEnum::DefaultPageIndex->value;
        $count         = intval(Arr::get($searchParams, 'count', PageEnum::DefaultPageSize->value)) ?: PageEnum::DefaultPageSize->value;
        if (!empty($startDate) && !checkDateIsValid($startDate))
        {
            return self::Fail('获取拣货出库单列表，开始日期格式错误', 400);
        }
        if (!empty($endDate) && !checkDateIsValid($endDate))
        {
            return self::Fail('获取拣货出库单列表，结束日期格式错误', 400);
        }
        if (is_numeric($status) && StockPendingOrderStatusEnum::notExists($status))
        {
            return self::Fail('获取拣货出库单列表，出库状态错误', 400);
        }

        // 构建查询参数
        $queryParams = [
            'hospitalId'    => $hospitalId,
            'keywords'      => $keywords,
            'relationCode'  => $orderCode,
            'startDate'     => $startDate,
            'endDate'       => $endDate,
            'createUserUid' => $createUserUid,
            'status'        => $status,
        ];

        // 获取拣货出库单列表数据
        $pendingOrderListRes = StockPendingOrderModel::getPendingOrderListData($queryParams, $page, $count);
        $totalCount          = $pendingOrderListRes['total'] ?? 0;
        $pendingOrderList    = $pendingOrderListRes['data'] ?? [];
        if ($totalCount <= 0 || empty($pendingOrderList))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $getFormatPendingOrderRes = self::FormatPendingOrder($pendingOrderList);
        if ($getFormatPendingOrderRes->isFail())
        {
            return $getFormatPendingOrderRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatPendingOrderRes->getData()]);
    }

    /**
     * 获取拣货出库单详情
     *
     * @param int   $pendingOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPendingOrderDetail(int $pendingOrderId, array $publicParams): LogicResult
    {
        if (empty($pendingOrderId))
        {
            return self::Fail('获取拣货出库单详情，缺少出库单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取拣货出库单详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取拣货出库单详情，缺少医院ID必选参数', 400);
        }

        // 获取拣货出库单信息
        $getPendingOrderRes = StockPendingOrderModel::getPendingOrderListData(['pendingOrderId' => $pendingOrderId, 'hospitalId' => $hospitalId]);
        $getPendingOrderRes = !empty($getPendingOrderRes['data']) ? current($getPendingOrderRes['data']) : [];
        if (empty($getPendingOrderRes))
        {
            return self::Fail('拣货出库单不存在', 44000);
        }

        // 格式化拣货出库单信息
        $getFormatPendingOrderRes = self::FormatPendingOrder([$getPendingOrderRes]);
        if ($getFormatPendingOrderRes->isFail())
        {
            return $getFormatPendingOrderRes;
        }

        $getFormatPendingOrderRes = $getFormatPendingOrderRes->getData(0);
        if (empty($getFormatPendingOrderRes))
        {
            return self::Fail('拣货出库单不存在', 44000);
        }

        // 获取拣货出库单商品明细
        $getPendingOrderDetailRes = StockPendingOrderDetailModel::getData(where: ['pending_order_id' => $pendingOrderId]);
        if (empty($getPendingOrderDetailRes))
        {
            return self::Fail('拣货出库单商品明细不存在', 44001);
        }

        // 获取商品基础信息
        $itemIds        = array_unique(array_column($getPendingOrderDetailRes, 'item_id'));
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData();
        $getItemInfoRes = array_column($getItemInfoRes, null, 'id');

        $returnPendingOrderDetail = [];
        foreach ($getPendingOrderDetailRes as $curPendingOrderDetail)
        {
            $curItemInfo = $getItemInfoRes[$curPendingOrderDetail['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            $returnPendingOrderDetail[] = [
                'uid'                  => $curPendingOrderDetail['uid'],
                'itemUid'              => $curItemInfo['uid'],
                'itemBarcode'          => $curItemInfo['item_barcode_info']['item_barcode'] ?? '',
                'itemName'             => ItemHelper::ItemDisplayName($curItemInfo),
                'packQuantity'         => $curPendingOrderDetail['pack_quantity'],
                'bulkQuantity'         => $curPendingOrderDetail['bulk_quantity'],
                'outboundPackQuantity' => $curPendingOrderDetail['outbound_pack_quantity'],
                'outboundBulkQuantity' => $curPendingOrderDetail['outbound_bulk_quantity'],
                'itemInfo'             => ItemHelper::FormatItemInfoStructure($curItemInfo),
            ];
        }

        $getFormatPendingOrderRes['items'] = $returnPendingOrderDetail;

        return self::Success($getFormatPendingOrderRes);
    }

    /**
     * 格式化拣货单
     *
     * @param array $arrStockPendingOrder
     *
     * @return LogicResult
     */
    private static function FormatPendingOrder(array $arrStockPendingOrder): LogicResult
    {
        if (empty($arrStockPendingOrder))
        {
            return self::Success();
        }

        $returnPendingOrderList = [];
        foreach ($arrStockPendingOrder as $curPendingOrderInfo)
        {
            $returnPendingOrderList[] = [
                'uid'          => $curPendingOrderInfo['uid'],
                'relationCode' => $curPendingOrderInfo['relation_code'],
                'petInfo'      => [
                    'uid'  => $curPendingOrderInfo['pet_uid'],
                    'name' => $curPendingOrderInfo['pet_name'],
                ],
                'memberInfo'   => [
                    'uid'   => $curPendingOrderInfo['member_uid'],
                    'name'  => $curPendingOrderInfo['member_name'],
                    'phone' => secretCellphone($curPendingOrderInfo['member_phone']),
                ],
                'createUser'   => [
                    'uid'  => $curPendingOrderInfo['user_uid'],
                    'name' => $curPendingOrderInfo['user_name'],
                ],
                'createTime'   => $curPendingOrderInfo['created_at'],
                'status'       => [
                    'id'   => $curPendingOrderInfo['status'],
                    'name' => StockPendingOrderStatusEnum::getDescription($curPendingOrderInfo['status']),
                ]
            ];
        }

        return self::Success($returnPendingOrderList);
    }
}
