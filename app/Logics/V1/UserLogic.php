<?php

namespace App\Logics\V1;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\UsersModel;

class UserLogic extends Logic
{
    /**
     * 获取有效用户信息
     *
     * @param int    $userId
     * @param string $userUid
     *
     * @return LogicResult
     */
    public static function GetValidUserByIdOrUid(int $userId = 0, string $userUid = ''): LogicResult
    {
        if (empty($userId) && empty($userUid))
        {
            return self::Fail('查找有效用户，缺少必选参数', 400);
        }

        $where = ['status' => 1, 'delete_status' => 0];
        if (!empty($userId))
        {
            $where['id'] = $userId;
        }
        if (!empty($userUid))
        {
            $where['uid'] = $userUid;
        }

        $getUserRes = UsersModel::getData(where: $where);
        if (empty($getUserRes))
        {
            return self::Fail('用户无效、不存在', 10100);
        }

        return self::Success(current($getUserRes));
    }

    /**
     * 获取用户信息
     *
     * @param string $uid
     * @param int    $userId
     * @param string $phone
     * @param bool   $withId 是否返回ID：为了安全，通常不返回ID
     * @param bool   $withPassword
     * @param bool   $onWritePdo
     *
     * @return LogicResult
     */
    public static function GetUser(
        string $uid = '',
        int    $userId = 0,
        string $phone = '',
        bool   $withId = false,
        bool   $withPassword = false,
        bool   $onWritePdo = false
    ): LogicResult
    {
        if ($uid == '' && $userId <= 0 && $phone == '')
        {
            return self::Fail('获取用户，缺少必选参数', 400);
        }

        if ($uid != '')
        {
            $user = UsersModel::getOneByUid($uid, $onWritePdo);
        }
        elseif ($userId > 0)
        {
            $user = UsersModel::getOne($userId, $onWritePdo);
        }
        else
        {
            $user = UsersModel::getOneByPhone($phone, $onWritePdo);
        }

        if (empty($user) || $user->delete_status == 1)
        {
            return self::Fail('用户不存在', 10100);
        }

        if ($user->status != 1)
        {
            return self::Fail('用户已禁用', 10102);
        }


        $userInfo = [
            'uid'       => $user->uid,
            'phone'     => $user->phone,
            'phoneHide' => secretCellphone($user->phone),
            'name'      => $user->name,
            'avatar'    => '',//TODO:补充完整头像路径
        ];

        if ($withId)
        {
            $userInfo['id'] = $user->id;
        }
        if ($withPassword)
        {
            $userInfo['password'] = $user->password;
        }

        return self::Success($userInfo);
    }

    /**
     * 添加用户
     *
     * @param array $userInfo
     *
     * @return LogicResult
     */
    public static function AddUser(array $userInfo): LogicResult
    {
        $phone    = trim(Arr::get($userInfo, 'phone', ''));
        $password = trim(Arr::get($userInfo, 'password', ''));
        $name     = trim(Arr::get($userInfo, 'name', ''));
        $avatar   = trim(Arr::get($userInfo, 'avatar', ''));//TODO:处理本地上传的图片连接地址
        $gender   = intval(Arr::get($userInfo, 'gender', 0));
        $birthday = trim(Arr::get($userInfo, 'birthday'));
        $from     = intval(Arr::get($userInfo, 'from', env('SYSTEM_PLATFORM_TYPE_ID', 0)));

        if ($phone == '')
        {
            return self::Fail('新增用户，缺少必选参数', 400);
        }
        if (!checkValidCellphone($phone))
        {
            return self::Fail('用户手机号格式错误', 10000);
        }
        if ($gender < 0 || $gender > 2)
        {
            return self::Fail('新增用户，性别参数错误', 10010);
        }
        if ($birthday != '' && strtotime($birthday) === false)
        {
            return self::Fail('新增用户，生日参数错误', 10011);
        }

        $phoneExists = UsersModel::getOneByPhone($phone, true);
        /*
         * 注意事项：
         * delete_status = 1 代表用户自行删除或注销，可再次注册，但是是新的账号
         * status = 0 代表被管理员禁用，不可再次注册
         */
        if (!empty($phoneExists) && $phoneExists->delete_status == 0)
        {
            return self::Fail('用户已存在，不可重复添加', 10101);
        }


        $userUid = generateUUID();
        $data    = [
            'uid'      => $userUid,
            'phone'    => $phone,
            'password' => $password != '' ? password_hash($password, PASSWORD_BCRYPT) : '',
            'name'     => $name,
            'avatar'   => $avatar,
            'gender'   => $gender,
            'birthday' => $birthday ?: null,
            'from'     => $from,
        ];

        $insertUserId = UsersModel::insertOne($data);
        if ($insertUserId <= 0)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' insert user fail', $data);

            return self::Fail('新增用户失败', 10180);
        }

        return self::Success(['id' => $insertUserId, 'uid' => $userUid]);
    }
}
