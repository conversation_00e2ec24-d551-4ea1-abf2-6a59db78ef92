<?php

namespace App\Logics\V1;

use Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\TestModel;
use App\Models\ImagesModel;
use App\Models\CaseSnapshotModel;
use App\Models\RecipeSnapshotModel;

class PrintLogic extends Logic
{
    /**
     * 获取处方打印信息
     *
     * @param int $recipeId
     * @param     $publicParams
     *
     * @return LogicResult
     */
    public static function GetPrintRecipe(int $recipeId, $publicParams): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('打印处方，缺少处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('打印处方，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('打印处方，缺少公共医院ID必选参数', 400);
        }

        // 优先验证是否历史处方，如果存在历史处方则从快照中获取。注：历史、当前处方打印信息需要保持返回信息一致。如果修改请同步修改
        $getHistoryRecipeTotal = RecipeSnapshotModel::getTotalNumber(['recipe_id' => $recipeId, 'status' => 1]);
        if (!empty($getHistoryRecipeTotal))
        {
            $getPrintRecipeRes = self::GetPrintHistoryRecipe($recipeId, $publicParams);
        }
        else
        {
            $getPrintRecipeRes = self::GetPrintCurrentRecipe($recipeId, $publicParams);
        }

        if ($getPrintRecipeRes->isFail())
        {
            return $getPrintRecipeRes;
        }

        return self::Success($getPrintRecipeRes->getData());
    }

    /**
     * 获取当前处方打印信息
     *
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function GetPrintCurrentRecipe(int $recipeId, array $publicParams): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('打印处方，缺少处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('打印处方，缺少必选参数', 400);
        }

        // 获取处方信息
        $getValidRecipeRes = RecipeLogic::GetValidRecipeById($recipeId, $publicParams);
        if ($getValidRecipeRes->isFail())
        {
            return $getValidRecipeRes;
        }

        // 获取处方详情
        $getRecipeDetailRes = RecipeLogic::GetRecipeDetail($recipeId, $publicParams, false, false);
        if ($getRecipeDetailRes->isFail())
        {
            return $getRecipeDetailRes;
        }

        // 获取处方关联的病历信息
        $caseId           = $getValidRecipeRes->getData('case_id');
        $getCaseDetailRes = CaseLogic::GetCaseDetail($caseId, $publicParams);
        if ($getCaseDetailRes->isFail())
        {
            return $getCaseDetailRes;
        }

        // 信息
        $recipeInfo       = $getValidRecipeRes->getData();
        $recipeDetailInfo = $getRecipeDetailRes->getData();
        $recipeCaseInfo   = $getCaseDetailRes->getData();
        if (empty($recipeInfo) || empty($recipeDetailInfo) || empty($recipeCaseInfo))
        {
            return self::Fail('处方信息不完整，无法打印', 39000);
        }

        // 返回信息
        $returnData = [
            'memberInfo' => $recipeCaseInfo['memberInfo'],
            'petInfo'    => $recipeCaseInfo['petInfo'],
            'diagnosis'  => $recipeCaseInfo['diagnosisInfo']['clinicalDiagnosis'],
        ];
        $returnData = array_merge($recipeDetailInfo, $returnData);

        return self::Success($returnData);
    }

    /**
     * 获取历史处方打印信息
     *
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function GetPrintHistoryRecipe(int $recipeId, array $publicParams): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('打印历史处方，缺少处方ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('打印历史处方，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('打印历史处方，缺少公共医院ID必选参数', 400);
        }

        // 获取历史处方信息
        $getHistoryRecipeItemRes = RecipeSnapshotModel::getData(fields: [
                                                                            'case_id',
                                                                            'recipe_info',
                                                                            'recipe_items',
                                                                            'recipe_items_group'
                                                                        ],
                                                                where : ['recipe_id' => $recipeId, 'status' => 1]);
        $getHistoryRecipeItemRes = $getHistoryRecipeItemRes ? current($getHistoryRecipeItemRes) : [];
        if (empty($getHistoryRecipeItemRes))
        {
            return self::Fail('打印历史处方，处方明细不存在', 39000);
        }

        $caseId                        = $getHistoryRecipeItemRes['case_id'];
        $getHistoryRecipeInfoRes       = json_decode($getHistoryRecipeItemRes['recipe_info'], true);
        $getHistoryRecipeItemList      = json_decode($getHistoryRecipeItemRes['recipe_items'], true);
        $getHistoryRecipeItemGroupList = json_decode($getHistoryRecipeItemRes['recipe_items_group'], true);
        if (empty($getHistoryRecipeInfoRes) || empty($getHistoryRecipeItemList) || empty($getHistoryRecipeItemGroupList))
        {
            return self::Fail('打印历史处方，处方明细不存在', 39000);
        }

        // 获取历史处方病历信息
        $getHistoryCaseRes = CaseSnapshotModel::getData([
                                                            'case_full_info->memberInfo as memberInfo',
                                                            'case_full_info->petInfo as petInfo',
                                                            'case_full_info->diagnosisInfo->clinicalDiagnosis as diagnosis'
                                                        ],
                                                        [
                                                            'case_id' => $caseId,
                                                            'status'  => 1
                                                        ]);
        $getHistoryCaseRes = $getHistoryCaseRes ? current($getHistoryCaseRes) : [];
        if (empty($getHistoryCaseRes))
        {
            return self::Fail('处方明细不存在，关联病历信息不存在', 38000);
        }

        $returnData = array_merge($getHistoryRecipeInfoRes,
                                  [
                                      'memberInfo'    => json_decode($getHistoryCaseRes['memberInfo'], true),
                                      'petInfo'       => json_decode($getHistoryCaseRes['petInfo'], true),
                                      'diagnosis'     => $getHistoryCaseRes['diagnosis'],
                                      'items'         => $getHistoryRecipeItemList,
                                      'itemGroupList' => $getHistoryRecipeItemGroupList,
                                  ]);

        return self::Success($returnData);
    }

    /**
     * 获取病历打印信息
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPrintCase(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('打印病历，缺少病历ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('打印病历，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('打印病历，缺少公共医院ID必选参数', 400);
        }

        // 获取病历信息
        $getCaseDetailRes = CaseSnapshotModel::getData(['case_full_info'],
                                                       ['case_id' => $caseId, 'status' => 1]);
        $getCaseDetailRes = $getCaseDetailRes ? current($getCaseDetailRes) : [];
        if (empty($getCaseDetailRes))
        {
            return self::Fail('打印病历不存在', 38000);
        }

        // 获取病历详情
        $getCaseFullInfoRes = json_decode($getCaseDetailRes['case_full_info'], true);
        if (empty($getCaseFullInfoRes))
        {
            return self::Fail('历史病历详情不存在', 38000);
        }

        // 获取病历处方详情
        $getHistoryCaseRecipeRes = RecipeSnapshotModel::getData(['recipe_info', 'recipe_items', 'recipe_items_group'],
                                                                ['case_id' => $caseId, 'status' => 1]);
        if (!empty($getHistoryCaseRecipeRes))
        {
            foreach ($getHistoryCaseRecipeRes as $curInfo)
            {
                $curRecipeInfo = json_decode($curInfo['recipe_info'], true);
                if (empty($curRecipeInfo))
                {
                    continue;
                }

                $curRecipeItemList      = json_decode($curInfo['recipe_items'], true);
                $curRecipeItemGroupList = json_decode($curInfo['recipe_items_group'], true);
                if (empty($curRecipeItemList) || empty($curRecipeItemGroupList))
                {
                    continue;
                }

                $getCaseFullInfoRes['recipeInfo'][] = array_merge($curRecipeInfo,
                                                                  [
                                                                      'items'         => $curRecipeItemList,
                                                                      'itemGroupList' => $curRecipeItemGroupList,
                                                                  ]);
            }
        }

        return self::Success($getCaseFullInfoRes);
    }

    /**
     * 获取化验报告打印信息
     *
     * @param int   $testId
     * @param int   $reportResultId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPrintTestReportResult(int $testId, int $reportResultId, array $publicParams): LogicResult
    {
        if (empty($testId))
        {
            return self::Fail('打印化验报告，缺少化验ID必选参数', 400);
        }
        if (empty($reportResultId))
        {
            return self::Fail('打印化验报告，缺少报告结果ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('打印化验报告，缺少必选参数', 400);
        }

        // 获取化验信息
        $getTestRes = TestLogic::GetValidTest($testId, $publicParams, false, null);
        if ($getTestRes->isFail())
        {
            return self::Fail('打印化验报告，化验项不存在', 40000);
        }

        $getTestRes = $getTestRes->getData();

        // 获取化验报告单结果
        $getReportDetailRes = TestLogic::GetTestTemplateReport($testId,
                                                               ['reportResultId' => $reportResultId],
                                                               $publicParams);
        if ($getReportDetailRes->isFail())
        {
            return $getReportDetailRes;
        }

        $getReportDetailRes = $getReportDetailRes->getData();


        // 获取化验项目信息
        $getTestItemRes = TestModel::getTestItemInfoByTestIds([$testId]);
        $getTestItemRes = !empty($getTestItemRes) ? current($getTestItemRes) : [];

        // 组合化验基本信息
        $testInfo = [
            'testCode'   => $getTestRes['test_code'],
            'remark'     => $getTestRes['remark'],
            'createTime' => formatDisplayDateTime($getTestRes['created_at']),
            'item'       => [
                'uid'  => $getTestItemRes['item_uid'],
                'name' => $getTestItemRes['item_display_name'],
                'unit' => $getTestItemRes['item_use_unit'],
            ]
        ];

        // 获取化验项目宠物、用户信息
        $getPetRes = PetLogic::GetPetBaseInfoByPetIds([$getTestRes['pet_id']]);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 组合化验关联的宠物、用户信息
        $petInfo    = [];
        $memberInfo = [];
        $getPetRes  = $getPetRes->getData();
        if (!empty($getPetRes[$getTestRes['pet_id']]))
        {
            $petInfo    = $getPetRes[$getTestRes['pet_id']]['petInfo'];
            $memberInfo = $getPetRes[$getTestRes['pet_id']]['memberInfo'];
        }

        // 获取化验关联医院
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $getTestRes['hospital_id'], true, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        // 组合化验关联医院信息
        $hospitalInfo   = [];
        $getHospitalRes = $getHospitalRes->getData();
        if (!empty($getHospitalRes))
        {
            $hospitalInfo = [
                'uid'  => $getHospitalRes['uid'] ?? '',
                'name' => $getHospitalRes['aliasName'] ?? '',
            ];
        }

        return self::Success([
                                 'testInfo'       => $testInfo,
                                 'petInfo'        => $petInfo,
                                 'memberInfo'     => $memberInfo,
                                 'hospitalInfo'   => $hospitalInfo,
                                 'templateResult' => $getReportDetailRes,
                             ]);
    }

    /**
     * 获取化验报告打印信息
     *
     * @param int   $imageId
     * @param int   $reportResultId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPrintImageReportResult(int $imageId, int $reportResultId, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('打印影响报告，缺少影像ID必选参数', 400);
        }
        if (empty($reportResultId))
        {
            return self::Fail('打印影像报告，缺少报告结果ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('打印影像报告，缺少必选参数', 400);
        }

        // 获取化验信息
        $getImageRes = ImageLogic::GetValidImage($imageId, $publicParams, false, null);
        if ($getImageRes->isFail())
        {
            return self::Fail('打印化验报告，化验项不存在', 40000);
        }

        $getImageRes = $getImageRes->getData();

        // 获取影像报告单结果
        $getReportDetailRes = ImageLogic::GetImageTemplateReport($imageId,
                                                                 ['reportResultId' => $reportResultId],
                                                                 $publicParams);
        if ($getReportDetailRes->isFail())
        {
            return $getReportDetailRes;
        }

        $getReportDetailRes = $getReportDetailRes->getData();


        // 获取影像项目信息
        $getImageItemRes = ImagesModel::getImageItemInfoByImageIds([$imageId]);
        $getImageItemRes = !empty($getImageItemRes) ? current($getImageItemRes) : [];

        // 组合影像基本信息
        $imageInfo = [
            'imageCode'  => $getImageRes['image_code'],
            'remark'     => $getImageRes['remark'],
            'createTime' => formatDisplayDateTime($getImageRes['created_at']),
            'item'       => [
                'uid'  => $getImageItemRes['item_uid'],
                'name' => $getImageItemRes['item_display_name'],
                'unit' => $getImageItemRes['item_use_unit'],
            ]
        ];

        // 获取影像项目宠物、用户信息
        $getPetRes = PetLogic::GetPetBaseInfoByPetIds([$getImageRes['pet_id']]);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 组合影像关联的宠物、用户信息
        $petInfo    = [];
        $memberInfo = [];
        $getPetRes  = $getPetRes->getData();
        if (!empty($getPetRes[$getImageRes['pet_id']]))
        {
            $petInfo    = $getPetRes[$getImageRes['pet_id']]['petInfo'];
            $memberInfo = $getPetRes[$getImageRes['pet_id']]['memberInfo'];
        }

        // 获取影像关联医院
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $getImageRes['hospital_id'], true, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        // 组合影像关联医院信息
        $hospitalInfo   = [];
        $getHospitalRes = $getHospitalRes->getData();
        if (!empty($getHospitalRes))
        {
            $hospitalInfo = [
                'uid'  => $getHospitalRes['uid'] ?? '',
                'name' => $getHospitalRes['aliasName'] ?? '',
            ];
        }

        return self::Success([
                                 'imageInfo'      => $imageInfo,
                                 'petInfo'        => $petInfo,
                                 'memberInfo'     => $memberInfo,
                                 'hospitalInfo'   => $hospitalInfo,
                                 'templateResult' => $getReportDetailRes,
                             ]);
    }
}
