<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Enums\InpatientStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RoomsBedsModel;
use App\Models\RoomsModel;
use App\Models\InpatientModel;
use App\Models\RoomsBedsUseRecordModel;

class BedLogic extends Logic
{
    /**
     * 住院-验证床位是否可用
     *
     * @param int   $roomId
     * @param int   $bedId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function IsBedAvailable(int $roomId, int $bedId, array $publicParams): LogicResult
    {
        if (empty($roomId) || empty($bedId))
        {
            return self::Fail('病房ID、床位ID，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('公共参数，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }

        // 病房是否存在
        $getRoomRes = RoomsModel::getData(where: ['id' => $roomId, 'hospital_id' => $hospitalId, 'status' => 1]);
        if (empty($getRoomRes))
        {
            return self::Fail('选择的病房不存在', 37000);
        }

        // 床位是否存在
        $getBedRes = RoomsBedsModel::getData(where: ['id' => $bedId, 'room_id' => $roomId, 'status' => 1]);
        $getBedRes = $getBedRes ? current($getBedRes) : [];
        if (empty($getBedRes))
        {
            return self::Fail('选择的床位不存在', 37001);
        }
        if (!empty($getBedRes['used']))
        {
            return self::Fail('选择的床位已使用，不可使用', 37002);
        }

        return self::Success();
    }

    /**
     * 住院-使用床位
     *
     * @param int   $roomId
     * @param int   $bedId
     * @param int   $inpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function UseBed(int $roomId, int $bedId, int $inpatientId, array $publicParams): LogicResult
    {
        if (empty($roomId) || empty($bedId) || empty($inpatientId))
        {
            return self::Fail('病房ID、床位ID、住院ID，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('公共参数，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId) || empty($doctorId))
        {
            return self::Fail('公共参数，缺少必选参数', 400);
        }

        // 获取住院信息
        $getInpatientRes = InpatientModel::getData(where: ['id' => $inpatientId, 'hospital_id' => $hospitalId]);
        $getInpatientRes = $getInpatientRes ? current($getInpatientRes) : [];
        if (empty($getInpatientRes))
        {
            return self::Fail('住院不存在', 37000);
        }
        if ($getInpatientRes['status'] != InpatientStatusEnum::Inpatient->value)
        {
            return self::Fail('住院状态发生变化，不可使用床位', 37004);
        }

        // 验证床位是否可用
        $getCheckRes = self::IsBedAvailable($roomId, $bedId, $publicParams);
        if ($getCheckRes->isFail())
        {
            return $getCheckRes;
        }

        // 使用床位
        try
        {
            DB::beginTransaction();

            $insertBedUsedLogData = [
                'inpatient_id' => $inpatientId,
                'org_id'       => $hospitalOrgId,
                'brand_id'     => $hospitalBrandId,
                'hospital_id'  => $hospitalId,
                'member_id'    => $getInpatientRes['member_id'],
                'pet_id'       => $getInpatientRes['pet_id'],
                'room_id'      => $roomId,
                'bed_id'       => $bedId,
                'use_type'     => 1,
                'remark'       => '门诊转住院>>入笼',
                'created_by'   => $doctorId,
            ];
            RoomsBedsUseRecordModel::insertOne($insertBedUsedLogData);

            // 更新笼位状态标记使用
            RoomsBedsModel::updateOne($bedId, ['used' => 1]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 使用床位异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('使用床位异常', 37003);
        }
    }

    /**
     * 住院-释放床位
     *
     * @param int   $inpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function ReleaseBed(int $inpatientId, array $publicParams): LogicResult
    {
        if (empty($inpatientId))
        {
            return self::Fail('释放床位，缺少住院ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('释放床位，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId) || empty($doctorId))
        {
            return self::Fail('释放床位，缺少必选参数', 400);
        }

        // 获取住院信息
        $getInpatientRes = InpatientModel::getData(where: ['id' => $inpatientId, 'hospital_id' => $hospitalId]);
        $getInpatientRes = $getInpatientRes ? current($getInpatientRes) : [];
        if (empty($getInpatientRes))
        {
            return self::Fail('释放床位，住院不存在', 37000);
        }

        // 释放床位
        try
        {
            DB::beginTransaction();

            $insertBedUsedLogData = [
                'inpatient_id' => $inpatientId,
                'org_id'       => $hospitalOrgId,
                'brand_id'     => $hospitalBrandId,
                'hospital_id'  => $hospitalId,
                'member_id'    => $getInpatientRes['member_id'],
                'pet_id'       => $getInpatientRes['pet_id'],
                'room_id'      => $getInpatientRes['room_id'],
                'bed_id'       => $getInpatientRes['bed_id'],
                'use_type'     => 2,
                'remark'       => '住院结束>>释放笼位',
                'created_by'   => $doctorId,
            ];
            RoomsBedsUseRecordModel::insertOne($insertBedUsedLogData);

            // 更新笼位状态标记未使用
            RoomsBedsModel::updateOne($getInpatientRes['bed_id'], ['used' => 0]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 释放床位异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('释放床位异常', 37011);
        }
    }
}
