<?php

namespace App\Logics\V1;

use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\UserSmsSendTypeEnum;
use App\Enums\UserSmsTypeEnum;
use App\Enums\RedisKeyEnum;
use App\Models\UserVerifySmsLogModel;

/**
 * 用户验证类短信
 */
class UserVerifySmsLogic extends Logic
{
    /**
     * 发送验证码
     *
     * 根据条件验证用户存在性
     * 验证发送限制
     * 发送验证码
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function SendVerifyCode(array $params, array $publicParams): LogicResult
    {
        $userId   = intval(Arr::get($params, 'userId', 0));
        $phone    = trim(Arr::get($params, 'phone', ''));
        $type     = intval(Arr::get($params, 'type', 0));
        $sendType = intval(Arr::get($params, 'sendType', UserSmsSendTypeEnum::default()->value));
        $uniqueId = trim(Arr::get($params, 'uniqueId', ''));
        $extInfo  = trim(Arr::get($params, 'extInfo', ''));
        $ip       = trim(Arr::get($params, 'ip', ''));

        $token = Arr::has($params, 'token')
            ? trim(Arr::get($params, 'token'))
            : trim(Arr::get($publicParams, 'token'));

        if ($userId <= 0 && $phone == '')
        {
            return self::Fail('发送验证码，缺少必选参数', 400);
        }
        if ($phone != '' && !checkValidCellphone($phone))
        {
            return self::Fail('用户手机号格式错误', 10000);
        }
        if (!UserSmsTypeEnum::exists($type))
        {
            return self::Fail('验证码类型错误', 10500);
        }
        if (!UserSmsSendTypeEnum::exists($sendType))
        {
            return self::Fail('验证码发送方式错误', 10501);
        }

        //先验证用户是否存在
        $userResult = UserLogic::GetUser(userId: $userId, phone: $phone, withId: true);
        if ($userResult->isSuccess())
        {
            $userId = $userResult->getData('id', 0);
            $phone  = $userResult->getData('phone');
        }

        //验证发送限制是否满足
        $checkSendLimitResult = self::CheckSendLimit($phone, $type);
        if ($checkSendLimitResult->isFail())
        {
            return $checkSendLimitResult;
        }

        //发送验证码
        $verifyCode = self::GetVerifyCode($type);
        $smsLogData = [
            "token"        => $token,
            "user_id"      => $userId,
            "hospital_id"  => intval(Arr::get($publicParams, '_hospitalId', 0)),
            "phone"        => $phone,
            "code"         => $verifyCode,
            "type"         => $type,
            "send_type"    => $sendType,
            "unique_id"    => $uniqueId,
            "ext_info"     => $extInfo,
            "verify_times" => 0,
            "ip"           => $ip,
        ];

        $insertLogId = UserVerifySmsLogModel::insertOne($smsLogData);
        if ($insertLogId <= 0)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' insert user verify sms log fail', $smsLogData);

            return self::Fail('用户验证码发送日志记录失败', 10551);
        }

        try
        {
            if ($sendType == UserSmsSendTypeEnum::voice->value)
            {
                $smsTemplate = config("setting.user.verify_sms_template.$type");
                if (!is_array($smsTemplate))
                {
                    throw new Exception("Config error! check[setting.user.verify_sms_template]");
                }

                $sendResult = smsSend(
                    $phone,
                    $sendType,
                    $smsTemplate['id'],
                    str_replace('{{code}}', $verifyCode, $smsTemplate['content']),
                    ["origin" => config("setting.user.verify_sms_origin.$type", 'api')]
                );
            }
            else
            {
                $sendResult = smsSend(
                    $phone,
                    $sendType,
                    '',
                    str_replace('{{code}}', $verifyCode, config("setting.user.verify_sms_content.$type", '')),
                    ["origin" => config("setting.user.verify_sms_origin.$type", 'api')]
                );
            }
        } catch (Exception $e)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' cause exception', [
                'code'    => $e->getCode(),
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
                'trace'   => $e->getTraceAsString(),
            ]);

            return self::Fail('用户验证码发送异常', 10552);
        }

        if (!$sendResult)
        {
            return self::Fail('用户验证码发送失败', 10552);
        }

        return self::Success([
                                 'phone'    => secretCellphone($phone),
                                 'type'     => $type,
                                 'sendType' => $sendType,
                                 'code'     => $verifyCode,
                             ]);
    }

    /**
     * 校验验证码
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function CheckVerifyCode(array $params, array $publicParams): LogicResult
    {
        // TODO 删除掉此代码
        return self::Success();

        $userId   = intval(Arr::get($params, 'userId', 0));
        $phone    = trim(Arr::get($params, 'phone', ''));
        $uniqueId = trim(Arr::get($params, 'uniqueId', ''));
        $extInfo  = trim(Arr::get($params, 'extInfo', ''));
        $type     = intval(Arr::get($params, 'type', 0));
        $code     = trim(Arr::get($params, 'code', ''));

        $token = Arr::has($params, 'token')
            ? trim(Arr::get($params, 'token'))
            : trim(Arr::get($publicParams, 'token'));

        if (($userId <= 0 && $phone == '') || $code == '')
        {
            return self::Fail('校验验证码，缺少必选参数', 400);
        }
        if ($phone != '' && !checkValidCellphone($phone))
        {
            return self::Fail('用户手机号格式错误', 10000);
        }
        if (!UserSmsTypeEnum::exists($type))
        {
            return self::Fail('验证码类型错误', 10500);
        }

        $extWhere = [];
        if (UserSmsTypeEnum::ResetPassword->value == $type)
        {
            if ($userId <= 0 || $uniqueId == '' || $extInfo == '' || $token == '')
            {
                return self::Fail('校验重置密码验证码，缺少必选参数', 400);
            }

            $extWhere = [
                'token'    => $token,
                'uniqueId' => $uniqueId,
                'extInfo'  => $extInfo,
            ];
        }

        if ($phone == '')
        {
            $userResult = UserLogic::GetUser(userId: $userId, phone: $phone, withId: true);
            if ($userResult->isFail())
            {
                return $userResult;
            }
            else
            {
                $phone = $userResult->getData('phone');
            }
        }

        //验证验证限制是否满足
        $checkVerifyLimitResult = self::CheckVerifyLimit($phone, $type);
        if ($checkVerifyLimitResult->isFail())
        {
            return $checkVerifyLimitResult;
        }

        //验证验证码
        $smsLog = UserVerifySmsLogModel::getUserSmsSendLog($phone, $code, $type, $extWhere);
        if (empty($smsLog))
        {
            //记录错误次数
            self::AddVerifyErrorTimes($phone);

            return self::Fail('验证码错误', 10570);
        }

        //验证是否有效期内
        if ($smsLog->verify_times > 0)
        {
            return self::Fail('验证码已失效[已验证过]', 10580);
        }
        if (abs(time() - strtotime($smsLog->created_at)) > config("setting.user.verify_sms_expired.{$type}", 300))
        {
            UserVerifySmsLogModel::incrementUserSmsLogVerifyTimes($smsLog->id);

            return self::Fail('验证码已过期', 10571);
        }

        //验证是否已使用过
        //操作字段used_status

        UserVerifySmsLogModel::incrementUserSmsLogVerifyTimes($smsLog->id);

        return self::Success([
                                 'logId'    => $smsLog->id,
                                 'phone'    => secretCellphone($phone),
                                 'type'     => $type,
                                 'sendType' => $smsLog->send_type,
                                 'code'     => $smsLog->code,
                                 'uniqueId' => $smsLog->unique_id,
                                 'extInfo'  => $smsLog->extinfo,
                             ]);
    }

    /**
     * 验证发送限制是否满足
     *
     * @param string $phone
     * @param int    $type
     *
     * @return LogicResult
     */
    private static function CheckSendLimit(string $phone, int $type): LogicResult
    {
        //获取验证码历史
        $smsSendCount = UserVerifySmsLogModel::getUserSmsSendCount(
            $phone,
            $type,
            abs(time() - config('setting.user.verify_sms_limit_circle', 86400))
        );

        //验证限制周期内发送次数限制（周期是滚动的计算的）
        if ($smsSendCount >= config("setting.user.verify_sms_limit_time.$type", 10))
        {
            return self::Fail('验证码发送次数已超过限制', 10520);
        }

        $checkVerifyLimit = self::CheckVerifyLimit($phone, $type);
        if ($checkVerifyLimit->isFail())
        {
            return $checkVerifyLimit;
        }

        return self::Success();
    }

    /**
     * 验证验证限制是否满足
     *
     * @param string $phone
     * @param int    $type
     *
     * @return LogicResult
     */
    private static function CheckVerifyLimit(string $phone, int $type): LogicResult
    {
        $verifyCodeErrorTimes = Redis::connection(config('setting.user.redis_connection', 'user'))
                                     ->get(RedisKeyEnum::GetUserVerifyCodeErrorKey($phone));
        if (intval($verifyCodeErrorTimes) >= config("setting.user.verify_sms_error_times.$type", 10))
        {
            return self::Fail('验证码错误次数太多，请稍后再试', 10521);
        }

        return self::Success();
    }

    /**
     * 增加验证错误次数记录
     *
     * @param string $phone
     *
     * @return array
     */
    private static function AddVerifyErrorTimes(string $phone): array
    {
        return Redis::connection(config('setting.user.redis_connection', 'user'))
                    ->transaction(function (\Redis $redis) use ($phone) {
                        $redis->incr(RedisKeyEnum::GetUserVerifyCodeErrorKey($phone), 1);
                        $redis->expire(RedisKeyEnum::GetUserVerifyCodeErrorKey($phone),
                                       config("setting.user.verify_sms_error_duration", 3600));
                    });
    }

    /**
     * 生成随机验证码
     *
     * @param int $type
     *
     * @return string
     */
    private static function GetVerifyCode(int $type): string
    {
        return generateRandomNumber(config("setting.user.verify_sms_length.$type", 6));
    }
}
