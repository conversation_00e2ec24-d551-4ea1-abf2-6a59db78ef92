<?php

namespace App\Logics\V1;

use App\Enums\ItemStatusEnum;
use App\Logics\Logic;
use App\Models\ItemModel;
use Arr;

class ItemStockLogic extends Logic
{
    public static function GetItemStockInfo(array $itemIds, array $publicParams)
    {
        if (empty($itemIds))
        {
            return self::Fail('获取商品库存信息，缺少商品ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取商品库存信息，缺少公共必选参数', 400);
        }

        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($hospitalId))
        {
            return self::Fail('获取商品库存信息，缺少医院ID必选参数', 400);
        }

        $getItemsBaseInfoRes = ItemModel::getData(where: ['org_id' => $orgId, 'status' => ItemStatusEnum::Online], whereIn: ['id' => $itemIds]);
        print_r($getItemsBaseInfoRes);
        exit;
    }
}
