<?php

namespace App\Logics\V1;

use Arr;
use App\Enums\HospitalUserByBusinessEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\HospitalUserModel;

/**
 * 医院用户
 */
class HospitalUserLogic extends Logic
{
    /**
     * 获取当前用户可用的医院身份下的医院
     *
     * @param int  $userId
     * @param bool $withId
     * @param bool $withRecommend
     *
     * @return LogicResult
     */
    public static function GetUserAvailableHospitals(int $userId, bool $withId = false, bool $withRecommend = false): LogicResult
    {
        if ($userId <= 0)
        {
            return self::Fail('获取用户可用医院，缺少必选参数', 400);
        }

        $hospitalUsers = HospitalUserModel::getUserHospitalUsers(userId: $userId);
        if (empty($hospitalUsers))
        {
            return self::Success(['hospitals' => []]);
        }

        $hospitalsBaseInfo = HospitalLogic::GetHospitalsBaseInfo(hospitalIds: $hospitalUsers->pluck('hospital_id')
                                                                                            ->toArray(),
                                                                 withId     : $withId,
                                                                 recommend  : $withRecommend ? null : null,//TODO:最新加入的医院和上次登录医院标记
        );

        if ($hospitalsBaseInfo->isFail())
        {
            return $hospitalsBaseInfo;
        }

        return self::Success(['hospitals' => $hospitalsBaseInfo->getData('hospitals')]);
    }


    /**
     * 获取当前用户在某医院身份
     *
     * @param int  $userId
     * @param int  $hospitalId
     * @param bool $withId
     *
     * @return LogicResult
     */
    public static function GetUserHospitalUser(int $userId, int $hospitalId, bool $withId = false): LogicResult
    {
        if ($userId <= 0 || $hospitalId <= 0)
        {
            return self::Fail('获取用户当前医院用户身份，缺少必选参数', 400);
        }

        $hospitalUser = HospitalUserModel::getUserHospitalUser(userId: $userId, hospitalId: $hospitalId, status: null);
        if (empty($hospitalUser))
        {
            return self::Fail('获取用户当前医院用户身份，数据不存在', 10600);
        }

        if ($hospitalUser->status != 1)
        {
            return self::Fail('获取用户当前医院用户身份，已禁用', 10602);
        }

        $hospitalUserInfo = [
            'uid'          => $hospitalUser->uid,
            'isAdmin'      => $hospitalUser->is_admin == 1,
            'workTitle'    => $hospitalUser->work_title,
            'registerCode' => $hospitalUser->register_code,
        ];

        if ($withId)
        {
            $hospitalUserInfo['id']         = $hospitalUser->id;
            $hospitalUserInfo['hospitalId'] = $hospitalUser->hospital_id;
            $hospitalUserInfo['userId']     = $hospitalUser->user_id;
        }

        return self::Success($hospitalUserInfo);
    }

    /**
     * 获取医院下不同业务可用的用户
     *
     * @param string $businessType
     * @param array  $publicParams
     *
     * @return LogicResult
     */
    public static function GetHospitalUsersByBusiness(string $businessType, array $publicParams): LogicResult
    {
        if (empty($businessType))
        {
            return self::Fail('获取医院下业务用户，缺少业务类型必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取医院下业务用户，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取医院下业务用户，缺少医院ID必选参数', 400);
        }

        // 获取业务类型下的角色
        $getBusinessRoleList = HospitalUserByBusinessEnum::getBusinessRoleList($businessType);
        if (empty($getBusinessRoleList))
        {
            return self::Fail('获取医院下业务用户，业务配置错误', 400);
        }

        // 当前指定角色用户 && 排除拥有哪些角色的用户 && 添加下拉默认选项
        $roleIds        = $getBusinessRoleList['role_ids'] ?? [];
        $excludeRoleIds = $getBusinessRoleList['exclude_role_ids'] ?? [];
        $defaultOption  = $getBusinessRoleList['default_option'] ?? [];
        $getUsersRes    = HospitalUserModel::getHospitalUsers($hospitalId,
            roleIds       :                                   $roleIds,
            excludeRoleIds:                                   $excludeRoleIds);
        if (empty($getUsersRes))
        {
            return self::Success(['business' => $businessType, 'data' => []]);
        }

        // 填充默认选项
        $returnUsersInfo = [];
        if (!empty($defaultOption))
        {
            $returnUsersInfo[] = $defaultOption;
        }

        foreach ($getUsersRes as $curInfo)
        {
            $returnUsersInfo[] = [
                'uid'  => $curInfo['user_uid'],
                'name' => $curInfo['user_name'],
            ];
        }

        return self::Success(['business' => $businessType, 'data' => $returnUsersInfo]);
    }
}
