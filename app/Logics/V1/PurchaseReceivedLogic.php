<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use App\Support\Item\ItemHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PurchaseTypeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\PurchaseOrderInboundStatusEnum;
use App\Enums\PurchaseOrderReceivedStatusEnum;
use App\Enums\PurchaseOrderStatusEnum;
use App\Models\PurchaseOrderModel;
use App\Models\PurchaseReceivedModel;
use App\Models\PurchaseOrderItemModel;
use App\Models\PurchaseReceivedDetailModel;
use App\Models\PurchaseReceivedVoucherModel;

class PurchaseReceivedLogic extends Logic
{
    /**
     * 获取可签收的采购单列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetReceivedLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取签收列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取签收列表，缺少公共必选参数', 400);
        }

        // 获取可签收的采购单列表
        $searchParams['purchaseStatus'] = PurchaseOrderStatusEnum::Approved->value;
        $getReceivedListRes             = PurchaseLogic::GetPurchaseLists($searchParams, $publicParams);
        if ($getReceivedListRes->isFail())
        {
            return $getReceivedListRes;
        }

        return self::Success($getReceivedListRes->getData());
    }

    /**
     * 获取有效采购单
     *
     * @param int   $receivedId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidPurchaseReceived(int $receivedId, array $publicParams): LogicResult
    {
        if (empty($receivedId))
        {
            return self::Fail('获取有效到货单，缺少到货单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效到货单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效到货单，缺少医院ID必选参数', 400);
        }

        // 获取采购单信息
        $getReceivedRes = PurchaseReceivedModel::getData(where: ['id' => $receivedId, 'hospital_id' => $hospitalId,]);
        $getReceivedRes = $getReceivedRes ? current($getReceivedRes) : [];
        if (empty($getReceivedRes))
        {
            return self::Fail('到货单不存在或已失效', 42016);
        }

        return self::Success($getReceivedRes);
    }

    /**
     * 获取到货单详情
     *
     * @param int   $purchaseReceivedId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetReceivedDetail(int $purchaseReceivedId, array $publicParams): LogicResult
    {
        if (empty($purchaseReceivedId))
        {
            return self::Fail('获取到货单详情，缺少到货单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取到货单详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取到货单详情，缺少公共医院ID必选参数', 400);
        }

        // 获取到货单信息
        $getReceivedRes = PurchaseReceivedModel::getReceivedListData(['receivedId' => $purchaseReceivedId, 'hospitalId' => $hospitalId]);
        $getReceivedRes = !empty($getReceivedRes['data']) ? current($getReceivedRes['data']) : [];
        if (empty($getReceivedRes))
        {
            return self::Fail('到货单不存在', 42016);
        }

        // 获取到货单明细
        $getReceivedDetailRes = PurchaseReceivedDetailModel::getData(where: ['received_id' => $purchaseReceivedId]);
        if (empty($getReceivedDetailRes))
        {
            return self::Fail('到货单商品不存在', 42017);
        }

        // 获取到货单商品信息
        $itemIds                = array_column($getReceivedDetailRes, 'item_id');
        $getReceivedItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, itemStatus: [], publicParams: $publicParams, withItemStock: true);
        if ($getReceivedItemInfoRes->isFail())
        {
            return $getReceivedItemInfoRes;
        }

        // 格式化到货单信息
        $getFormatReceivedRes = self::FormatReceivedStructure([$getReceivedRes]);
        if ($getFormatReceivedRes->isFail())
        {
            return $getFormatReceivedRes;
        }

        // 格式化采购单商品信息
        $getFormatPurchaseItemRes = self::FormatReceivedDetailStructure($getReceivedDetailRes, $getReceivedItemInfoRes->getData());
        if ($getFormatPurchaseItemRes->isFail())
        {
            return $getFormatPurchaseItemRes;
        }

        $getFormatReceivedRes          = current($getFormatReceivedRes->getData());
        $getFormatReceivedRes['items'] = $getFormatPurchaseItemRes->getData();

        return self::Success($getFormatReceivedRes);
    }

    /**
     * 到货签收
     *
     * @param int   $purchaseOrderId
     * @param array $addReceivedParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddReceived(int $purchaseOrderId, array $addReceivedParams, array $publicParams): LogicResult
    {
        if (empty($purchaseOrderId))
        {
            return self::Fail('到货签收，缺少采购单ID必选参数', 400);
        }
        if (empty($addReceivedParams))
        {
            return self::Fail('到货签收，缺少签收必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('到货签收，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId) || empty($userId))
        {
            return self::Fail('到货签收，缺少用户ID必选参数', 400);
        }

        // 业务参数
        $remark        = trimWhitespace(Arr::get($addReceivedParams, 'remark', ''));
        $pictures      = Arr::get($addReceivedParams, 'pictures', []);
        $receivedItems = Arr::get($addReceivedParams, 'items', []);
        if (empty($remark))
        {
            return self::Fail('到货签收，签收备注必填', 400);
        }
        if (empty($pictures))
        {
            return self::Fail('到货签收，签收凭证必传', 400);
        }
        if (empty($receivedItems))
        {
            return self::Fail('到货签收，缺少签收商品', 400);
        }

        // 获取采购单信息
        $getPurchaseOrderRes = PurchaseLogic::GetValidPurchaseOrder($purchaseOrderId, $publicParams);
        if ($getPurchaseOrderRes->isFail())
        {
            return $getPurchaseOrderRes;
        }

        // 采购单状态是否可签收
        $getPurchaseOrderRes = $getPurchaseOrderRes->getData();
        if ($getPurchaseOrderRes['status'] != PurchaseOrderStatusEnum::Approved->value)
        {
            return self::Fail('采购单状态未审核通过，不可签收', 42018);
        }
        if ($getPurchaseOrderRes['received_status'] == PurchaseOrderReceivedStatusEnum::FullyReceived->value)
        {
            return self::Fail('采购单已全部签收，不可重复签收', 42019);
        }
        if ($getPurchaseOrderRes['inbound_status'] == PurchaseOrderInboundStatusEnum::FullyInbound->value)
        {
            return self::Fail('采购单已全部入库，不可签收', 42020);
        }

        // 获取采购单商品明细
        $getPurchaseOrderItemRes = PurchaseOrderItemModel::getData(where: ['purchase_order_id' => $purchaseOrderId, 'status' => 1]);
        if (empty($getPurchaseOrderItemRes))
        {
            return self::Fail('采购单商品不存在', 42017);
        }

        // 采购总数、已签收数(全部转化成散装数量，好对比)，用于标记签收状态：部分签收、全部签收
        $purchaseItemTotalQuantity = 0;
        $completedReceivedQuantity = 0;

        // 本次签收信息
        $errorMsg               = [];
        $receivedTotalQuantity  = 0;
        $receivedTotalPrice     = 0;
        $insertReceivedItemData = [];
        foreach ($getPurchaseOrderItemRes as $curPurchaseItem)
        {
            // 整散比
            $curItemBulkRatio = $curPurchaseItem['item_bulk_ratio'] ?? 1;

            // 采购总数-散
            $purchaseItemTotalQuantity += StockQuantityConversionHelper::convertToTotalBulkQuantity($curPurchaseItem['pack_quantity'],
                                                                                                    $curPurchaseItem['bulk_quantity'],
                                                                                                    $curItemBulkRatio);

            // 已签收数-散
            $completedReceivedQuantity += StockQuantityConversionHelper::convertToTotalBulkQuantity($curPurchaseItem['received_pack_quantity'],
                                                                                                    $curPurchaseItem['received_bulk_quantity'],
                                                                                                    $curItemBulkRatio);

            foreach ($receivedItems as $curReceivedItem)
            {
                // 数据表示是否一致
                if ($curPurchaseItem['uid'] != $curReceivedItem['purchaseOrderItemUid'])
                {
                    continue;
                }

                // 是否同一个商品
                if ($curPurchaseItem['item_barcode'] != $curReceivedItem['itemBarcode'])
                {
                    $errorMsg[] = '签收商品【' . $curReceivedItem['itemName'] . '】，商品与采购单商品不一致';
                    break;
                }

                // 本次签收数量
                $curSignPackQuantity = intval($curReceivedItem['packQuantityReceived']);
                $curSignBulkQuantity = intval($curReceivedItem['bulkQuantityReceived']);
                if ($curSignPackQuantity <= 0 && $curSignBulkQuantity <= 0)
                {
                    $errorMsg[] = '签收商品【' . $curReceivedItem['itemName'] . '】，签收数量不可同时为0';
                    break;
                }

                // 剩余可签收数
                $getAvailableQuantityRes = StockQuantityConversionHelper::getRemainPackAndBulkQuantity($curPurchaseItem['pack_quantity'],
                                                                                                       $curPurchaseItem['bulk_quantity'],
                                                                                                       $curPurchaseItem['received_pack_quantity'],
                                                                                                       $curPurchaseItem['received_bulk_quantity'],
                                                                                                       $curItemBulkRatio);
                $remainPackQuantity      = $getAvailableQuantityRes['remainPackQuantity'];
                $remainBulkQuantity      = $getAvailableQuantityRes['remainBulkQuantity'];

                // 验证签收数量不超过剩余可签收数量（支持整散比换算）
                $getCheckSufficientRes = StockQuantityConversionHelper::checkQuantityIsSufficient($curSignPackQuantity,
                                                                                                  $curSignBulkQuantity,
                                                                                                  $remainPackQuantity,
                                                                                                  $remainBulkQuantity,
                                                                                                  $curItemBulkRatio);
                if (empty($getCheckSufficientRes))
                {
                    $errorMsg[] = '签收商品【' . $curReceivedItem['itemName'] . '】，超出可签收数量。（剩余可签收整装：' . formatDisplayNumber($remainPackQuantity) . '，剩余可签收散装：' . formatDisplayNumber($remainBulkQuantity) . '）';
                    break;
                }

                // 计算签收金额
                $curPackReceivedPrice = 0;
                $curBulkReceivedPrice = 0;
                if ($curSignPackQuantity > 0)
                {
                    // 整装签收金额 = 整装数量 × 整散比 × 散装单价
                    $curPackReceivedPrice = numberMul([$curSignPackQuantity, $curItemBulkRatio, $curPurchaseItem['bulk_avg_price']], 4);
                }
                if ($curSignBulkQuantity > 0)
                {
                    // 散装签收金额 = 散装数量 × 散装单价
                    $curBulkReceivedPrice = numberMul([$curSignBulkQuantity, $curPurchaseItem['bulk_avg_price']], 4);
                }

                // 有效的签收商品信息
                $insertReceivedItemData[] = [
                    'purchase_order_item_id' => $curPurchaseItem['id'],
                    'item_id'                => $curPurchaseItem['item_id'],
                    'item_barcode'           => $curPurchaseItem['item_barcode'],
                    'pack_quantity'          => $curSignPackQuantity,
                    'bulk_quantity'          => $curSignBulkQuantity,
                    'is_gift'                => $curPurchaseItem['is_gift'],
                    'created_by'             => $userId,
                ];

                // 累计签收数量、金额（转换为散装单位统计）
                $signTotalBulkQuantity = StockQuantityConversionHelper::convertToTotalBulkQuantity($curSignPackQuantity, $curSignBulkQuantity, $curItemBulkRatio);
                $receivedTotalQuantity = numberAdd([$receivedTotalQuantity, $signTotalBulkQuantity], 4);
                $receivedTotalPrice    = numberAdd([$receivedTotalPrice, $curPackReceivedPrice, $curBulkReceivedPrice], 4);
                break;
            }
        }

        // 签收存在错误
        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42021);
        }

        // 验证无有效的签收商品
        if (empty($insertReceivedItemData))
        {
            return self::Fail('签收商品全部无效', 42021);
        }

        // 到货凭证
        $insertReceivedVoucherData = [];
        foreach ($pictures as $curPicture)
        {
            $insertReceivedVoucherData[] = [
                'org_id'      => $orgId,
                'brand_id'    => $brandId,
                'hospital_id' => $hospitalId,
                'image_name'  => $curPicture['name'],
                'image_url'   => $curPicture['url'],
                'created_by'  => $userId,
            ];
        }

        try
        {
            DB::beginTransaction();

            // 签收记录
            $receivedCode       = generateBusinessCodeNumber(BusinessCodePrefixEnum::DHDH);
            $insertReceivedData = [
                'purchase_order_id' => $purchaseOrderId,
                'purchase_code'     => $getPurchaseOrderRes['purchase_code'],
                'purchase_type'     => $getPurchaseOrderRes['purchase_type'],
                'received_code'     => $receivedCode,
                'org_id'            => $orgId,
                'brand_id'          => $brandId,
                'hospital_id'       => $hospitalId,
                'supplier_id'       => $getPurchaseOrderRes['supplier_id'],
                'allot_hospital_id' => $getPurchaseOrderRes['allot_hospital_id'],
                'received_price'    => $receivedTotalPrice,
                'remark'            => $remark,
                'created_by'        => $userId,
            ];
            $receivedId         = PurchaseReceivedModel::insertOne($insertReceivedData);

            // 签收商品详情
            foreach ($insertReceivedItemData as $curReceivedItem)
            {
                $curReceivedItem['uid']           = generateUUID();
                $curReceivedItem['received_code'] = $receivedCode;
                $curReceivedItem['received_id']   = $receivedId;

                $receivedDetailId = PurchaseReceivedDetailModel::insertOne($curReceivedItem);

                // 到货后，记录医院采购商品记录
                $getRecordRes = HospitalItemPurchaseRecordLogic::MarkPurchaseItemRecord($curReceivedItem['item_id'], $receivedDetailId, $publicParams);
                if ($getRecordRes->isFail())
                {
                    DB::rollBack();

                    return $getRecordRes;
                }
            }

            // 签收凭证
            if (!empty($insertReceivedVoucherData))
            {
                foreach ($insertReceivedVoucherData as $key => $voucherInfo)
                {
                    $insertReceivedVoucherData[$key]['received_code'] = $receivedCode;
                    $insertReceivedVoucherData[$key]['received_id']   = $receivedId;
                }
            }
            PurchaseReceivedVoucherModel::insert($insertReceivedVoucherData);

            // 更新采购单商品签收信息
            foreach ($insertReceivedItemData as $curReceivedItem)
            {
                if ($curReceivedItem['pack_quantity'] > 0)
                {
                    $curAffectedRows = PurchaseOrderItemModel::on()
                                                             ->where(['id' => $curReceivedItem['purchase_order_item_id']])
                                                             ->increment('received_pack_quantity', $curReceivedItem['pack_quantity']);
                    if ($curAffectedRows <= 0)
                    {
                        DB::rollBack();

                        return self::Fail('签收商品【' . $curReceivedItem['item_barcode'] . '】，更新整超出可签收限制', 42021);
                    }
                }

                if ($curReceivedItem['bulk_quantity'] > 0)
                {
                    $curAffectedRows = PurchaseOrderItemModel::on()
                                                             ->where(['id' => $curReceivedItem['purchase_order_item_id']])
                                                             ->increment('received_bulk_quantity', $curReceivedItem['bulk_quantity']);
                    if ($curAffectedRows <= 0)
                    {
                        DB::rollBack();

                        return self::Fail('签收商品【' . $curReceivedItem['item_barcode'] . '】，更新散超出可签收限制', 42021);
                    }
                }
            }

            // 是否全部签收
            $receivedCompletedAt = null;
            $receivedStatus      = PurchaseOrderReceivedStatusEnum::PartiallyReceived->value;
            if ($completedReceivedQuantity + $receivedTotalQuantity >= $purchaseItemTotalQuantity)
            {
                $receivedStatus      = PurchaseOrderReceivedStatusEnum::FullyReceived->value;
                $receivedCompletedAt = getCurrentTimeWithMilliseconds();
            }

            // 更新采购单签收状态、签收金额
            $curAffectedRows = PurchaseOrderModel::on()
                                                 ->where(['id' => $purchaseOrderId])
                                                 ->whereRaw('received_price + ? <= purchase_price', [$receivedTotalPrice])
                                                 ->update([
                                                              'received_status'      => $receivedStatus,
                                                              'received_price'       => DB::raw('received_price + ' . $receivedTotalPrice),
                                                              'received_complete_at' => $receivedCompletedAt
                                                          ]);
            if ($curAffectedRows <= 0)
            {
                DB::rollBack();

                return self::Fail('签收失败，更新采购单签收金额散超限', 42021);
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 到货签收异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('到货签收异常', 42021);
        }
    }

    /**
     * 格式化到货单列表
     *
     * @param array $receivedList
     *
     * @return LogicResult
     */
    private static function FormatReceivedStructure(array $receivedList): LogicResult
    {
        if (empty($receivedList))
        {
            return self::Success();
        }

        $returnReceivedList = [];
        foreach ($receivedList as $curReceived)
        {
            $tmpReceived = [
                'receivedCode'    => $curReceived['received_code'],
                'purchaseCode'    => $curReceived['purchase_code'],
                'purchaseType'    => [
                    'id'   => $curReceived['purchase_type'],
                    'name' => PurchaseTypeEnum::getDescription($curReceived['purchase_type']),
                ],
                'supplierName'    => PurchaseLogic::FormatPurchaseSupplierName($curReceived)
                                                  ->getData('supplierName', ''),
                'supplier'        => [
                    'uid'  => $curReceived['supplier_uid'],
                    'name' => $curReceived['supplier_name'],
                ],
                'fromHospital'    => [
                    'uid'  => $curReceived['hospital_uid'],
                    'name' => $curReceived['hospital_alias_name'],
                ],
                'receivedInPrice' => $curReceived['received_price'] <= 0 ? '' : formatDisplayNumber($curReceived['received_price'], 4),
                'inboundStatus'   => [
                    'id'   => $curReceived['inbound_status'],
                    'name' => PurchaseOrderInboundStatusEnum::getDescription($curReceived['inbound_status']),
                ],
                'createUser'      => [
                    'uid'  => $curReceived['user_uid'],
                    'name' => $curReceived['user_name'],
                ],
                'createTime'      => formatDisplayDateTime($curReceived['created_at']),
                'remark'          => $curReceived['remark'],
            ];

            $returnReceivedList[] = $tmpReceived;
        }

        return self::Success($returnReceivedList);
    }

    /**
     * 格式化到货单商品信息
     *
     * @param array $receivedDetailList
     * @param array $itemInfoList
     *
     * @return LogicResult
     */
    private static function FormatReceivedDetailStructure(array $receivedDetailList, array $itemInfoList): LogicResult
    {
        if (empty($receivedDetailList) || empty($itemInfoList))
        {
            return self::Success();
        }

        // 按照商品ID数组
        $itemInfoList = array_column($itemInfoList, null, 'id');

        $returnReceivedDetailList = [];
        foreach ($receivedDetailList as $curReceivedDetail)
        {
            // 商品信息
            $curItemInfo = $itemInfoList[$curReceivedDetail['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            $tmpReceivedDetail = [
                'uid'                 => $curReceivedDetail['uid'],
                'itemUid'             => $curItemInfo['uid'],
                'itemBarcode'         => $curReceivedDetail['item_barcode'],
                'itemName'            => ItemHelper::ItemDisplayName($curItemInfo),
                'packQuantity'        => formatDisplayNumber($curReceivedDetail['pack_quantity']),
                'bulkQuantity'        => formatDisplayNumber($curReceivedDetail['bulk_quantity']),
                'inboundPackQuantity' => formatDisplayNumber($curReceivedDetail['inbound_pack_quantity']),
                'inboundBulkQuantity' => formatDisplayNumber($curReceivedDetail['inbound_bulk_quantity']),
                'isGift'              => $curReceivedDetail['is_gift'],
                'isInboundComplete'   => $curReceivedDetail['is_inbound_complete'],
                'itemInfo'            => ItemHelper::FormatItemInfoStructure($curItemInfo),
            ];

            $returnReceivedDetailList[] = $tmpReceivedDetail;
        }

        return self::Success($returnReceivedDetailList);
    }
}
