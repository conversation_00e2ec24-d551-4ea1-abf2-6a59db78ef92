<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\ItemUsageModel;

/**
 * 项目用药
 * Class ItemUsageLogic
 * @package App\Logics\V1
 */
class ItemUsageLogic extends Logic
{
    /**
     * 获取项目用药方式
     *
     * @return LogicResult
     */
    public static function GetRecipeItemUsageOptions(): LogicResult
    {
        $getUsageOptionsRes = ItemUsageModel::getData(where: ['status' => 1]);

        $usageOptions = [];
        foreach ($getUsageOptionsRes as $curInfo)
        {
            $usageOptions[] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['usageOptions' => $usageOptions]);
    }
}
