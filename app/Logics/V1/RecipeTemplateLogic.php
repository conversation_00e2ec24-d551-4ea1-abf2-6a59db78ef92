<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Support\Recipe\RecipeHelper;
use App\Enums\ItemUnitTypeEnum;
use App\Enums\RecipeTemplateTypeEnum;
use App\Enums\RecipeTemplateSourceEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RecipeItemModel;
use App\Models\RecipeTemplateModel;
use App\Models\RecipeTemplateConfigureModel;
use App\Models\RecipeTemplatesDetailModel;

class RecipeTemplateLogic extends Logic
{
    /**
     * 获取模版筛选项
     * @return LogicResult
     */
    public static function GetTemplateOptions(): LogicResult
    {
        return self::Success([
                                 'templateOptions' => array_merge([['id' => 0, 'name' => '全部模版']],
                                                                  RecipeTemplateTypeEnum::options())
                             ]);
    }

    /**
     * 获取模版列表
     *
     * @param int   $typeId
     * @param array $publicParams
     * @param array $searchParams
     *
     * @return LogicResult
     */
    public static function GetTemplateList(int $typeId, array $publicParams, array $searchParams = []): LogicResult
    {
        if ($typeId < 0)
        {
            return self::Fail('获取处方模版列表，缺少模版ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取处方模版列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($orgId) || empty($brandId))
        {
            return self::Fail('获取处方模版列表，缺少医院组织ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取处方模版列表，缺少医生ID必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('获取处方模版列表，缺少医院ID必选参数', 400);
        }

        $searchParams       = [
            'orgId'      => $orgId,
            'brandId'    => $brandId,
            'hospitalId' => $hospitalId,
            'doctorId'   => $doctorId,
            'type'       => $typeId,
            'keyword'    => $searchParams['keyword'] ?? ''
        ];
        $getTemplateListRes = RecipeTemplateModel::getTemplateList($searchParams);
        if (empty($getTemplateListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $returnTemplateList = [];
        foreach ($getTemplateListRes as $curInfo)
        {
            // 个人模版、个人创建的医院模版，可以删除
            $curDeleteAble = self::CheckDeleteRecipeTemplate($curInfo, $doctorId);

            $returnTemplateList[] = [
                'uid'        => $curInfo['uid'],
                'name'       => $curInfo['name'],
                'desc'       => $curInfo['desc'],
                'type'       => [
                    'id'   => $curInfo['type'],
                    'name' => RecipeTemplateTypeEnum::getDescription($curInfo['type']),
                ],
                'deleteAble' => $curDeleteAble,
            ];
        }

        return self::Success(['total' => count($returnTemplateList), 'data' => $returnTemplateList]);
    }

    /**
     * 获取模版商品详情
     *
     * @param int   $templateId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTemplateDetail(int $templateId, array $publicParams): LogicResult
    {
        if (empty($templateId))
        {
            return self::Fail('获取处方模版详情，缺少模版ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取处方模版详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($orgId) || empty($brandId))
        {
            return self::Fail('获取处方模版详情，缺少医院组织ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取处方模版详情，缺少医生ID必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('获取处方模版详情，缺少医院ID必选参数', 400);
        }

        // 获取处方模版
        $getWhere       = [
            'orgId'      => $orgId,
            'hospitalId' => $hospitalId,
            'doctorId'   => $doctorId,
            'templateId' => $templateId
        ];
        $getTemplateRes = RecipeTemplateModel::getTemplateList($getWhere);
        $getTemplateRes = $getTemplateRes ? current($getTemplateRes) : [];
        if (empty($getTemplateRes))
        {
            return self::Fail('处方模版不存在', 39100);
        }

        // 获取处方模版详情
        $getTemplateDetailRes = RecipeTemplatesDetailModel::getData(where: [
                                                                               'template_id' => $templateId,
                                                                               'status'      => 1
                                                                           ]);
        if (empty($getTemplateDetailRes))
        {
            return self::Fail('处方模版商品不存在', 39101);
        }

        // 格式化处方内商品
        $getFormatRecipeItemsRes = RecipeHelper::FormatRecipeItemStructure($getTemplateDetailRes,
                                                                           $publicParams,
                                                                           true,
                                                                           true);
        if ($getFormatRecipeItemsRes->isFail())
        {
            return $getFormatRecipeItemsRes;
        }

        $getFormatRecipeItemsRes = $getFormatRecipeItemsRes->getData('itemList', []);
        if (empty($getFormatRecipeItemsRes))
        {
            return self::Success();
        }

        // 删除处方内商品的uid相关，因为如果存在的话对于开处方来说是编辑，使用模版的情况下属于是新增
        foreach ($getFormatRecipeItemsRes as &$curInfo)
        {
            // 删除uid相关
            $curInfo['uid']          = '';
            $curInfo['suitUniqueId'] = '';
            $curInfo['isSuitItem']   = false;

            // 根据商品使用整散，重新赋值最新价格
            if ($curInfo['unitType'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value)
            {
                $curInfo['price']      = formatDisplayNumber($curInfo['itemInfo']['bulkPrice']);
                $curInfo['totalPrice'] = formatDisplayNumber(numberMul([$curInfo['price'], $curInfo['quantity']]));
            }
            else
            {
                $curInfo['price']      = formatDisplayNumber($curInfo['itemInfo']['price']);
                $curInfo['totalPrice'] = formatDisplayNumber(numberMul([$curInfo['packPrice'], $curInfo['quantity']]));
            }

            // 组合子项
            $curSuitItems = $curInfo['suitItems'] ?? [];
            if (!empty($curSuitItems))
            {
                foreach ($curSuitItems as &$curSuitItem)
                {
                    $curSuitItem['uid']          = '';
                    $curSuitItem['suitUniqueId'] = '';
                    $curSuitItem['isSuitItem']   = true;
                }
            }

            $curInfo['suitItems'] = $curSuitItems;
        }

        $returnData = [
            'uid'   => $getTemplateRes['uid'],
            'type'  => [
                'id'   => $getTemplateRes['type'],
                'name' => RecipeTemplateTypeEnum::getDescription($getTemplateRes['type']),
            ],
            'name'  => $getTemplateRes['name'],
            'desc'  => $getTemplateRes['desc'],
            'items' => $getFormatRecipeItemsRes,
        ];

        return self::Success($returnData);
    }

    /**
     * 添加处方模版
     *
     * @param array $addTemplateParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddRecipeTemplate(array $addTemplateParams, array $publicParams): LogicResult
    {
        if (empty($addTemplateParams))
        {
            return self::Fail('添加处方模版，缺少模版必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加处方模版，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($orgId) || empty($brandId))
        {
            return self::Fail('添加处方模版，缺少医院组织ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('添加处方模版，缺少医生ID必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('添加处方模版，缺少医院ID必选参数', 400);
        }

        // 业务参数
        $recipeId     = $addTemplateParams['recipeId'] ?? 0;
        $templateName = $addTemplateParams['name'] ?? '';
        $isPrivate    = $addTemplateParams['isPrivate'] ?? false;

        // 获取处方信息
        $getRecipeRes = RecipeLogic::GetValidRecipeById($recipeId, $publicParams);
        if ($getRecipeRes->isFail())
        {
            return $getRecipeRes;
        }

        // 获取处方商品
        $getRecipeDetailRes = RecipeItemModel::getData(where: ['recipe_id' => $recipeId, 'status' => 1]);
        if (empty($getRecipeDetailRes))
        {
            return self::Fail('处方明细不存在', 39009);
        }

        $oldSuitUniqueIdRelationNew = collect($getRecipeDetailRes)
            ->pluck('suit_unique_uid')
            ->filter()
            ->unique()
            ->mapWithKeys(fn($oldSuitUid) => [$oldSuitUid => generateUUID()]);

        $insertRecipeTemplateDetailData = [];
        foreach ($getRecipeDetailRes as $recipeItem)
        {
            $insertRecipeTemplateDetailData[] = [
                'template_id'     => 0,
                'recipe_item_uid' => $recipeItem['uid'],
                'item_suit_id'    => $recipeItem['item_suit_id'],
                'item_id'         => $recipeItem['item_id'],
                'item_sale_type'  => $recipeItem['item_sale_type'],
                'unit_type'       => $recipeItem['unit_type'],
                'once_use'        => $recipeItem['once_use'],
                'times'           => $recipeItem['times'],
                'days'            => $recipeItem['days'],
                'quantity'        => $recipeItem['quantity'],
                'usage_id'        => $recipeItem['usage_id'],
                'usage_name'      => $recipeItem['usage_name'],
                'remark'          => $recipeItem['remark'],
                'use_unit'        => $recipeItem['use_unit'],
                'used_quantity'   => $recipeItem['used_quantity'],
                'group'           => $recipeItem['group'],
                'suit_group'      => $recipeItem['suit_group'],
                'is_suit'         => $recipeItem['is_suit'],
                'suit_unique_uid' => !empty($recipeItem['suit_unique_uid']) ? $oldSuitUniqueIdRelationNew[$recipeItem['suit_unique_uid']] : '',
            ];
        }

        try
        {
            DB::beginTransaction();

            // 写入模版主信息
            $insertTemplateData = [
                'uid'         => generateUUID(),
                'hospital_id' => $hospitalId,
                'name'        => $templateName,
                'type'        => $isPrivate ? RecipeTemplateTypeEnum::Personal->value : RecipeTemplateTypeEnum::Hospital->value,
                'source'      => RecipeTemplateSourceEnum::His->value,
                'recipe_id'   => $recipeId,
                'created_by'  => $doctorId,
            ];
            $templateId         = RecipeTemplateModel::insertOne($insertTemplateData);

            // 写入模版商品
            foreach ($insertRecipeTemplateDetailData as $key => $itemInfo)
            {
                $insertRecipeTemplateDetailData[$key]['template_id'] = $templateId;
            }

            RecipeTemplatesDetailModel::insert($insertRecipeTemplateDetailData);

            // 写入模版可见信息
            $insertTemplateConfigureData = [
                'template_id' => $templateId,
                'org_id'      => $orgId,
                'brand_id'    => $brandId,
                'hospital_id' => $hospitalId,
                'doctor_id'   => $isPrivate ? $doctorId : 0,
            ];
            RecipeTemplateConfigureModel::insertOne($insertTemplateConfigureData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加处方模版异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加处方模版异常', 39102);
        }
    }

    /**
     * 删除处方模版
     *
     * @param int   $recipeTemplateId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DeleteRecipeTemplate(int $recipeTemplateId, array $publicParams): LogicResult
    {
        if (empty($recipeTemplateId))
        {
            return self::Fail('删除处方模版，缺少模版ID必选参数', 400);
        }

        if (empty($publicParams))
        {
            return self::Fail('删除处方模版，缺少公共必选参数', 400);
        }

        // 公共参数
        $doctorId = intval(Arr::get($publicParams, '_userId'));
        if (empty($doctorId))
        {
            return self::Fail('删除处方模版，缺少医生ID必选参数', 400);
        }

        // 获取模版信息
        $getTemplateRes = RecipeTemplateModel::getOne($recipeTemplateId);
        if (empty($getTemplateRes))
        {
            return self::Fail('处方模版不存在', 39100);
        }

        // 已删除
        if ($getTemplateRes['status'] != 1)
        {
            return self::Success();
        }

        // 是否可删除
        $getTemplateRes = $getTemplateRes->toArray();
        if (!self::CheckDeleteRecipeTemplate($getTemplateRes, $doctorId))
        {
            return self::Fail('暂无删除模版权限', 39103);
        }

        try
        {
            DB::beginTransaction();

            // 删除模版
            RecipeTemplateModel::updateOne($recipeTemplateId, ['status' => 0]);

            // 删除模版商品
            RecipeTemplatesDetailModel::on()
                                      ->where(['template_id' => $recipeTemplateId])
                                      ->update(['status' => 0]);

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 删除处方模版异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('删除处方模版异常', 39104);
        }
    }

    /**
     * 验证是否可删除模版
     *
     * @param array $templateInfo
     * @param int   $doctorId
     *
     * @return bool
     */
    private static function CheckDeleteRecipeTemplate(array $templateInfo, int $doctorId): bool
    {
        if (empty($templateInfo) || empty($doctorId))
        {
            return false;
        }

        if ($templateInfo['source'] == RecipeTemplateSourceEnum::His->value && $templateInfo['created_by'] == $doctorId && in_array($templateInfo['type'],
                                                                                                                                    [
                                                                                                                                        RecipeTemplateTypeEnum::Personal->value,
                                                                                                                                        RecipeTemplateTypeEnum::Hospital->value
                                                                                                                                    ]))
        {
            return true;
        }

        return false;
    }
}
