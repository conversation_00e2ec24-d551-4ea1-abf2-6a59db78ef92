<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RegistrationTypeModel;

class RegistrationTypeLogic extends Logic
{
    /**
     * 获取挂号类型
     *
     * @param int|null $isShow 是否只获取展示的类型。1-展示 0-不展示 null-全部
     *
     * @return LogicResult
     */
    public static function GetRegistrationTypeOptions(?int $isShow = 1): LogicResult
    {
        $getWhere = ['status' => 1];
        if (!is_null($isShow))
        {
            $getWhere['is_show'] = $isShow;
        }
        $getRegistrationTypeRes = RegistrationTypeModel::getData(where: $getWhere);

        $registrationTypeInfo = [];
        foreach ($getRegistrationTypeRes as $curInfo)
        {
            $registrationTypeInfo[] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['registrationTypeOptions' => $registrationTypeInfo]);
    }
}
