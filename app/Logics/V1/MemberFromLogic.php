<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\MemberFromModel;

class MemberFromLogic extends Logic
{
    /**
     * 获取会员注册来源渠道
     * @return LogicResult
     */
    public static function GetMemberFromList(): LogicResult
    {
        $getMemberFromRes = MemberFromModel::getAllByIds();

        $memberFromInfos = [];
        foreach ($getMemberFromRes as $curInfo)
        {
            $memberFromInfos[] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['memberFromOptions' => $memberFromInfos]);
    }
}
