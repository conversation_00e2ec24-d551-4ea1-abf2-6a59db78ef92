<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\PetBreedDictModel;

class PetBreedLogic extends Logic
{
    /**
     * 获取宠物品种
     * @return LogicResult
     */
    public static function GetPetBreedOptions(): LogicResult
    {
        $getPetBreedRes = PetBreedDictModel::getData(where: ['status' => 1]);

        $petBreedInfos = [];
        foreach ($getPetBreedRes as $curInfo)
        {
            $petBreedInfos[$curInfo['pet_category_id']][] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['petBreedOptions' => $petBreedInfos]);
    }
}
