<?php

namespace App\Logics;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\Response;
use RuntimeException;
use Throwable;

/**
 * PDF生成服务
 */
class PdfServiceLogic extends Logic
{
    /**
     * 服务请求
     *
     * 简单的多服务节点随机请求（负载均衡）
     * 简单的多服务节点故障转移（失败尝试下一个节点）
     *
     * @param string $method
     * @param string $uri
     * @param array  $options
     *
     * @return Response|null
     * @throws RuntimeException
     */
    private static function Request(string $method, string $uri, array $options = []): ?Response
    {
        $timeout = config('pdfservice.timeout', 3);
        $nodes   = config('pdfservice.nodes');
        shuffle($nodes); // 简单随机负载均衡，也可以轮询

        foreach ($nodes as $node)
        {
            try
            {
                $response = Http::timeout($timeout)
                                ->retry(1, 100)
                                ->{$method}("{$node}{$uri}", $options);

                if ($response->successful())
                {
                    if (!self::IsResponsePdfStream($response))
                    {
                        Log::warning(__CLASS__ . '::' . __METHOD__ . "Node {$node} failed: Not PDF stream",
                                     ['response' => $response->body()]);

                        return null;
                    }

                    return $response;
                }

                Log::warning(__CLASS__ . '::' . __METHOD__ . "Node {$node} failed: HTTP {$response->status()}",
                             ['response' => $response->body()]);
            } catch (Throwable $throwable)
            {
                Log::error(__CLASS__ . '::' . __METHOD__ . "Request to {$node}{$uri} failed: " . $throwable->getMessage());
                // 尝试下一个节点
            }
        }

        throw new RuntimeException("All service nodes failed for URI: {$uri}");
    }

    /**
     * 判断是否为PDF流
     *
     * @param Response $response
     *
     * @return bool
     */
    private static function IsResponsePdfStream(Response $response): bool
    {
        if (str_starts_with($response->header('Content-Type'), 'application/pdf'))
        {
            return true;
        }

        return false;
    }

    /**
     * 生成处方PDF
     *
     * @param array $recipeData
     * @param array $setting
     *
     * @return string|null
     */
    public static function GenerateRecipePdf(array $recipeData, array $setting): ?string
    {
        try
        {
            $response = self::Request('post', '/pdf/recipe', [
                'setting' => $setting,
                'recipe'  => $recipeData
            ]);
            if (empty($response))
            {
                return null;
            }

        } catch (Throwable $throwable)
        {
            return null;
        }

        return $response->body();
    }

    /**
     * 生成病历PDF
     *
     * @param array $caseData
     * @param array $setting
     *
     * @return string|null
     */
    public static function GenerateCasePdf(array $caseData, array $setting): ?string
    {
        try
        {
            $response = self::Request('post', '/pdf/case', [
                'setting'  => $setting,
                'caseInfo' => $caseData
            ]);
            if (empty($response))
            {
                return null;
            }

        } catch (Throwable $throwable)
        {
            return null;
        }

        return $response->body();
    }

    /**
     * 生成化验报告PDF
     *
     * @param array $testReportData
     * @param array $setting
     *
     * @return string|null
     */
    public static function GenerateTestReportPdf(array $testReportData, array $setting): ?string
    {
        try
        {
            $response = self::Request('post', '/pdf/test/template', [
                'setting' => $setting,
                'data'    => $testReportData
            ]);
            if (empty($response))
            {
                return null;
            }

        } catch (Throwable $throwable)
        {
            return null;
        }

        return $response->body();
    }

    /**
     * 生成影像报告PDF
     *
     * @param array $imageReportData
     * @param array $setting
     *
     * @return string|null
     */
    public static function GenerateImageReportPdf(array $imageReportData, array $setting): ?string
    {
        try
        {
            $response = self::Request('post', '/pdf/image/template', [
                'setting' => $setting,
                'data'    => $imageReportData
            ]);
            if (empty($response))
            {
                return null;
            }

        } catch (Throwable $throwable)
        {
            return null;
        }

        return $response->body();
    }
}
