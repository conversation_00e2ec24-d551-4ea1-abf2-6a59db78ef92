<?php

namespace App\Logics;

use Illuminate\Support\Arr;

class LogicResult
{

    private int $code;
    private string $message;
    private mixed $data;

    public function __construct(int $code, string $message, $data)
    {
        $this->code    = $code;
        $this->message = $message;
        $this->data    = $data;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    /**
     * 获取主数据
     *
     * @param string $dataPath 使用「.」符号从深度嵌套的数组中根据指定键检索值 a.b.c
     * @param mixed  $default  默认值
     * @param array  $except   排除的键（只能排除顶层的键，不支持以[.]号分割的嵌套结构排除）
     *
     * @return mixed
     */
    public function getData(string $dataPath = '', mixed $default = null, array $except = []): mixed
    {
        if (empty($except))
        {
            if ($dataPath != '')
            {
                return Arr::get($this->data, $dataPath, $default);
            }

            return $this->data;
        }
        else
        {
            if ($dataPath != '')
            {
                return Arr::get(Arr::except($this->data, $except), $dataPath, $default);
            }

            return Arr::except($this->data, $except);
        }
    }

    public function isSuccess(): bool
    {
        return $this->code == 200;
    }

    public function isFail(): bool
    {
        return $this->code != 200;
    }

    public function toArray(): array
    {
        return [
            'code'    => $this->code,
            'message' => $this->message,
            'data'    => $this->data,
        ];
    }

    public function __toString()
    {
        return json_encode($this->toArray());
    }
}
