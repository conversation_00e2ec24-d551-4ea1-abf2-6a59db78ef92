<?php

namespace App\Logics;

abstract class Logic
{

    /**
     * 返回失败结果
     *
     * @param string     $message
     * @param int        $code
     * @param array|null $data
     *
     * @return LogicResult
     */
    public static function Fail(string $message = '失败', int $code = 500, ?array $data = null): LogicResult
    {
        return self::_BuildResult($code, $message, $data);
    }

    /**
     * 返回成功结果
     *
     * @param array  $data
     * @param int    $code
     * @param string $message
     *
     * @return LogicResult
     */
    public static function Success(array $data = [], int $code = 200, string $message = '成功'): LogicResult
    {
        return self::_BuildResult($code, $message, $data);
    }

    /**
     * 构建结果对象
     *
     * @param int    $code
     * @param string $message
     * @param array|null  $data
     *
     * @return LogicResult
     */
    private static function _BuildResult(int $code, string $message, array|null $data): LogicResult
    {
        return new LogicResult($code, $message, $data);
    }

}
