<?php

namespace App\Logics\CloudPrinterTemplate;

abstract class BaseTemplate
{
    /**
     * 生成模板代码
     *
     * @return string
     */
    abstract public function build(): string;

    /**
     * 拆分行
     * 每行显示$rowWidth个字符宽度，中文算2个，英文1个
     *
     * @param string $content
     * @param int    $rowWidth
     *
     * @return array
     */
    public function explodeTextRow(string $content, int $rowWidth = 24): array
    {
        $textArray  = mb_str_split($content);
        $row        = [];
        $rowIndex   = 0;
        $lenCounter = 0;
        foreach ($textArray as $text)
        {
            if ($lenCounter + mb_strlen($text, 'gb2312') > $rowWidth)
            {
                $rowIndex ++;
                $lenCounter = 0;
            }

            if (!isset($row[$rowIndex]))
            {
                $row[$rowIndex] = '';
            }

            $row[$rowIndex] .= $text;
            $lenCounter     += mb_strlen($text, 'gb2312');
        }

        return $row;
    }
}
