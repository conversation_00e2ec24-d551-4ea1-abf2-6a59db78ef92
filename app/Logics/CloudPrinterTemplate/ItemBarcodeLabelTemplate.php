<?php

namespace App\Logics\CloudPrinterTemplate;

/**
 * 商品条码标签模板
 *
 * 【适用】宽度40mm×高度30mm 标签纸
 *
 * $template = (new \App\Logics\CloudPrinterTemplate\ItemBarcodeLabelTemplate())
 * ->setPrintTime()
 * ->setPrintCount()
 * ->setBarcode('00850015660009')
 * ->setContent('安捷 V-check CPL 犬胰腺特异性脂肪酶检测试剂盒（二代机读）10片/盒')
 * ->build();
 *
 * var_dump(\App\Services\GPrinter\GPrinter::print('00000000000000000', $template));
 */
class ItemBarcodeLabelTemplate extends BaseTemplate
{
    private string $title = 'VetAxis';
    private string $printTime = '';
    private int $printCount = 1;
    private string $barcode = '';
    private string $content = '';

    public function __construct()
    {

    }

    public function setPrintTime(string $printTime = ''): self
    {
        $this->printTime = $printTime != '' ? $printTime : date('Y-m-d H:i');

        return $this;
    }

    public function setPrintCount(int $printCount = 1): self
    {
        $this->printCount = $printCount;

        return $this;
    }

    public function setBarcode(string $barcode): self
    {
        $this->barcode = $barcode;

        return $this;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function build(): string
    {
        $template = <<<COMMAND
SIZE 40 mm,30 mm
GAP 2 mm,0 mm
REFERENCE 0,0
SPEED 2
DENSITY 8
DIRECTION 0
SET HEAD ON
SET PRINTKEY OFF
SET KEY1 ON
SET KEY2 ON
SHIFT 0
CLS

COMMAND;

        $template .= <<<HEAD
TEXT 20,20,"TSS24.BF2",0,1,1,"{$this->title}"
TEXT 180,24,"TSS16.BF2",0,1,1,"{$this->printTime}"

HEAD;

        $template .= <<<BARCODE_ZONE
BARCODE 50, 60, "128", 60, 1, 0, 2, 2, "{$this->barcode}"

BARCODE_ZONE;

        foreach ($this->explodeTextRow($this->content) as $key => $row)
        {
            $positionY = 150 + $key * 25;

            $template .= <<<CONTENT_ZONE
TEXT 20,{$positionY},"TSS24.BF2",0,1,1,"{$row}"

CONTENT_ZONE;
        }

        $template .= <<<COMMAND
PRINT 1,{$this->printCount}
COMMAND;

        return $template;
    }

}
