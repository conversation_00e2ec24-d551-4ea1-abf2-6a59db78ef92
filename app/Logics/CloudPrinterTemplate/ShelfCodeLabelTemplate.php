<?php

namespace App\Logics\CloudPrinterTemplate;

/**
 * 货位码打印
 *
 * 【适用】宽度40mm×高度30mm 标签纸
 *
 * $template = (new \App\Logics\CloudPrinterTemplate\StockShelfCodeLabelTemplate())
 * ->setPrintTime()
 * ->setShelfCods([['code' => 'A01-01', 'quantity' => 1], ['code' => 'A01-02', 'quantity' => 3]])
 * ->build();
 *
 * var_dump(\App\Services\GPrinter\GPrinter::print('00000000000000000', $template));
 */
class ShelfCodeLabelTemplate extends BaseTemplate
{
    private string $title = 'VetAxis';
    private string $printTime = '';
    private int $printCount = 1;
    private array $shelfCodes = [];

    public function __construct()
    {
    }

    /**
     * 打印时间
     *
     * @param string $printTime
     *
     * @return $this
     */
    public function setPrintTime(string $printTime = ''): self
    {
        $this->printTime = $printTime != '' ? $printTime : date('Y-m-d H:i');

        return $this;
    }

    /**
     * 货位码
     *
     * @param array $shelfCodes
     *
     * @return $this
     */
    public function setShelfCods(array $shelfCodes): self
    {
        $this->shelfCodes = $shelfCodes;

        return $this;
    }

    /**
     * 构建标签指令
     * @return string
     */
    public function build(): string
    {
        $template = '';
        foreach ($this->shelfCodes as $curCodeInfo)
        {
            if (empty($curCodeInfo['code']))
            {
                continue;
            }

            // 打印数量
            if (!empty($curCodeInfo['quantity']) && $curCodeInfo['quantity'] > 1)
            {
                $curCodeQuantity = $curCodeInfo['quantity'];
            }
            else
            {
                $curCodeQuantity = $this->printCount;
            }

            $template .= <<<COMMAND
SIZE 40 mm,30 mm
GAP 2 mm,0 mm
REFERENCE 0,0
SPEED 2
DENSITY 8
DIRECTION 0
SET HEAD ON
SET PRINTKEY OFF
SET KEY1 ON
SET KEY2 ON
SHIFT 0
CLS

COMMAND;

            $template .= <<<HEAD
TEXT 20,20,"TSS24.BF2",0,1,1,"{$this->title}"
TEXT 180,24,"TSS16.BF2",0,1,1,"{$this->printTime}"

HEAD;

            $template .= <<<BARCODE_ZONE
BARCODE 30, 60, "39", 70, 0, 0, 2, 6, "{$curCodeInfo['code']}"

BARCODE_ZONE;

            $template .= <<<CONTENT_ZONE
TEXT 20,140,"3",0,3,3,"{$curCodeInfo['code']}"

CONTENT_ZONE;

            $template .= <<<COMMAND
PRINT 1,{$curCodeQuantity}
COMMAND;
            $template .= "\n\n";
        }

        return $template;
    }
}
