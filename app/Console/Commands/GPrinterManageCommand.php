<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;
use App\Services\GPrinter\GPrinter;

class GPrinterManageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:gprinter
                            {operation : The operation type:summary|sync|info|status|setting}
                            {device_id? : Test target printer device_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gprinter cloud printer manage';

    private string $operation = '';
    private string $deviceId = '';
    private int $orgId = 0;

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->parseInput();

        if ($this->operation == 'sync')
        {
            $this->sync();
        }
        elseif ($this->operation == 'info')
        {
            $this->devInfo();
        }
        elseif ($this->operation == 'status')
        {
            $this->devStatus();
        }
        elseif ($this->operation == 'setting')
        {
            $this->devSetting();
        }
        else
        {
            $this->summary();
        }
    }

    private function parseInput(): void
    {
        $operation = $this->argument('operation');
        if (!in_array($operation, ['summary', 'sync', 'info', 'status', 'setting']))
        {
            $operation = 'summary';
        }

        $this->operation = $operation;
    }

    private function summary(): void
    {
        $summary = self::DBGetGprinterSummary();
        $this->table(['org_id', 'total', 'total_online', 'total_normal'], Arr::map($summary, function ($item) {
            return [
                $item->org_id,
                $item->total,
                $item->total_online,
                $item->total_normal,
            ];
        }));
    }

    private function sync(): void
    {
        $res = GPrinter::getDeviceList();
        if (!$res || !isset($res['code']) || !isset($res['deviceList']) || $res['code'] != 1)
        {
            $this->warn('接口请求失败');
        }

        if (!self::DBBatchInsertGprinterDevice($res['deviceList'], $this->orgId))
        {
            $this->warn('数据库插入失败');
        }

        $this->info('同步完成');
    }

    private function devInfo(): void
    {
        $deviceId = $this->argument('device_id');
        if (!$deviceId)
        {
            $this->warn('device_id不能为空');

            return;
        }

        $res = GPrinter::getDevice($deviceId);
        if (!$res || !isset($res['code']) || !isset($res['devInfo']) || $res['code'] != 1)
        {
            $this->warn('接口请求失败' . $res['msg'] ?? '');
        }

        $this->table(['device_id', 'device_name'], [
            [
                $res['devInfo']['deviceID'],
                $res['devInfo']['devName'],
            ]
        ]);
    }

    private function devStatus(): void
    {
        $deviceId = $this->argument('device_id');
        if (!$deviceId)
        {
            $this->warn('device_id不能为空');

            return;
        }

        $res = GPrinter::getStatus($deviceId);
        if (!$res || !isset($res['code']) || !isset($res['statusList']) || $res['code'] != 1)
        {
            $this->warn('接口请求失败' . $res['msg'] ?? '');
        }

        $this->table(['device_id', 'device_status', 'device_online', 'device_outtime'],
                     Arr::map($res['statusList'], function ($item) {
                         return [
                             $item['deviceID'],
                             $item['status'],
                             $item['online'],
                             $item['outtime'],
                         ];
                     }));
    }

    private function devSetting(): void
    {
        $deviceId = $this->argument('device_id');
        if (!$deviceId)
        {
            $this->warn('device_id不能为空');

            return;
        }

        $res = GPrinter::getDeviceInfo($deviceId);
        if (!$res || !isset($res['code']) || !isset($res['msg']) || $res['code'] != 0)
        {
            $this->warn('接口请求失败' . $res['msg'] ?? '');
        }

        $this->info('printType:11 小票模式， 22 标签模式');
        $this->info(json_encode($res['msg']));
    }

    private static function DBGetGprinterSummary(): array
    {
        return DB::select(
        /** @lang mysql */
            <<<SQL
SELECT
  org_id,
  count(*) AS total,
  count(IF(device_online = 1, 1, NULL)) AS total_online,
  count(IF(STATUS = 1, 1, NULL)) AS total_normal
FROM
  his_hospital_cloud_printers
GROUP BY
  org_id
ORDER BY
  org_id ASC;
SQL
        );
    }

    private static function DBBatchInsertGprinterDevice(array $deviceList, int $orgId): bool
    {
        $deviceIds = array_column($deviceList, 'deviceID');
        $deviceIds = array_unique($deviceIds);
        $deviceIds = array_values($deviceIds);

        $dbDeviceIds = DB::table('his_hospital_cloud_printers')
                         ->whereIn('device_id', $deviceIds)
                         ->pluck('device_id')
                         ->toArray();

        $notExistsDeviceIds = array_diff($deviceIds, $dbDeviceIds);
        if (empty($notExistsDeviceIds))
        {
            return true;
        }

        $notExistsDeviceList = array_filter($deviceList, function ($device) use ($notExistsDeviceIds) {
            return in_array($device['deviceID'], $notExistsDeviceIds);
        });

        $data = [];
        foreach ($notExistsDeviceList as $device)
        {
            $data[] = [
                'device_id'     => $device['deviceID'],
                'device_name'   => $device['title'],
                'group_id'      => 0,
                'org_id'        => $orgId,
                'brand_id'      => 0,
                'hospital_id'   => 0,
                'device_status' => 0,
                'device_online' => 0,
                'created_at'    => date('Y-m-d H:i:s'),
            ];
        }

        DB::table('his_hospital_cloud_printers')
          ->insert($data);


        return true;
    }
}
