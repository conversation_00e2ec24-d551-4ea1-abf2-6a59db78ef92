<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Meilisearch\Client;
use App\Facades\SearchFacade;

class MeilisearchSyncAllItemCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:meilisearch
                            {operation : The operation type:info|test|init|sync|flush}
                            {keywords? : Test search keywords}
                            {category? : Test search pet category id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Meilisearch manage';

    private string $operation = 'info';

    private string $orgId = '1';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->parseInput();

        if ($this->operation == 'test')
        {
            $this->test($this->argument('keywords'), $this->argument('category'));
        }
        elseif ($this->operation == 'init')
        {
            $this->init();
        }
        elseif ($this->operation == 'sync')
        {
            $this->sync();
        }
        elseif ($this->operation == 'flush')
        {
            $this->flush();
        }
        else
        {
            $this->information();
        }
    }

    private function parseInput(): void
    {
        $operation = $this->argument('operation');
        if (!in_array($operation, ['test', 'init', 'sync', 'flush']))
        {
            $operation = 'info';
        }

        $this->operation = $operation;
    }

    private function getIndexName(string $indexName = '', string|int|null $orgId = null): string
    {
        if ($indexName != '')
        {
            return $indexName;
        }
        elseif (isset($orgId))
        {
            return sprintf('his_item_%s', $orgId);
        }

        return sprintf('his_item_%s', $this->orgId);
    }

    private function getMeilisearchClient(): Client
    {
        return new Client(env('MEILISEARCH_HOST', 'http://127.0.0.1:7700'), env('MEILISEARCH_KEY', ''));
    }

    private function information(): void
    {
        $client = $this->getMeilisearchClient();

        try
        {
            $index = $client->getIndex($this->getIndexName());
            $this->info(sprintf('Index[%s] info:', $this->getIndexName()));
            $this->info(json_encode($index->fetchRawInfo(), JSON_UNESCAPED_UNICODE));
            $this->info(sprintf('Index[%s] stats:', $this->getIndexName()));
            $this->info(json_encode($index->stats(), JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e)
        {
            $this->info('Index not exists');
        }
    }

    private function test(string $keywords, string|int|null $category): void
    {
        try
        {
            if ($category != '' && intval($category) > 0)
            {
                $supportPetCategory = [intval($category)];
            }
            else
            {
                $supportPetCategory = [];
            }

            $this->info(sprintf('Search keywords: %s', $keywords));


            $result = SearchFacade::searchRecipeItems(keywords             : $keywords,
                                                      supportPetCategoryIds: $supportPetCategory,
                                                      orgId                : $this->orgId);

            $this->info('Search result:');
            $this->info(json_encode($result, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e)
        {
            $this->error('Test search failed');
            $this->error($e->getMessage());
        }
    }

    private function init(): bool
    {
        $options = [
            'primaryKey' => 'uid',
        ];

        $settings = [
            'searchableAttributes' => [
                'alias_name',
                'basis_name',
                'name',
                'english_name',
                'mem_code',
                'suit_items.alias_name',
                'suit_items.basis_name',
                'suit_items.name',
                'suit_items.english_name',
                'suit_items.mem_code',
            ],
            'rankingRules'         => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness',
            ],
            'filterableAttributes' => [
                'id',
                'barcode',
                'item_support_pet_category',
                'is_recipe_allow',
                'is_retail_allow',
                'is_receive_allow',
                'status',
                'is_suit',
                'first_sale_type.id',
                'is_purchasable',
                'brand.id',
                'purchase_hospital_ids',
            ],
            'sortableAttributes'   => [
                'id',
                'created_at',
                'item_type',
                'first_sale_type.id',
                'second_sale_type.id',
                'drug_type.id',
            ],
        ];

        $client = $this->getMeilisearchClient();

        $exists = false;

        try
        {
            $client->getIndex($this->getIndexName());
            $this->error('Index already exists');

            $exists = true;
        } catch (\Exception $e)
        {
            $this->info('Index not exists');
        }

        try
        {
            $index = $client->index($this->getIndexName());
            if (!$exists)
            {
                $index->create($this->getIndexName(), $options);
                $this->info('Create index');
            }

            $index->updateSettings($settings);
            $this->info('updated settings');
        } catch (\Exception $e)
        {
            $this->error('Init index failed');
            $this->error($e->getMessage());

            return false;
        }

        return true;
    }

    private function sync(): bool
    {
        $client = $this->getMeilisearchClient();

        try
        {
            $index = $client->getIndex($this->getIndexName());
        } catch (\Exception $e)
        {
            $this->error('Index not exists');

            return false;
        }

        $this->info(sprintf('Index[%s] sync start', $this->getIndexName()));

        $items = self::DBGetAllItem($this->orgId);
        $this->info(sprintf('Index[%s] sync %s items', $this->getIndexName(), count($items)));

        $beautyItems = self::DBGetAllBeautyItem($this->orgId);
        $this->info(sprintf('Index[%s] sync %s beauty items', $this->getIndexName(), count($beautyItems)));

        $suitItems = self::DBGetSuitItem($this->orgId);
        $this->info(sprintf('Index[%s] sync %s suitItems', $this->getIndexName(), count($suitItems)));

        $allItems = array_merge($items, $beautyItems, $suitItems);
        $index->addDocuments(self::BuildIndexDoc($allItems));

        $this->info(sprintf('Index[%s] sync end', $this->getIndexName()));

        return true;
    }

    private function flush(): bool
    {
        $client = $this->getMeilisearchClient();

        try
        {
            $index = $client->getIndex($this->getIndexName());
        } catch (\Exception $e)
        {
            $this->error('Index not exists');

            return false;
        }

        $this->info(sprintf('Index[%s] flush start', $this->getIndexName()));

        $index->deleteAllDocuments();

        $this->info(sprintf('Index[%s] flush end', $this->getIndexName()));

        return true;
    }

    private static function DBGetAllItem(string|int $orgId, array $itemIds = []): array
    {

        $itemWhere = ' 1=1 ';
        if (!empty($itemIds))
        {
            $itemWhere .= ' AND i.id IN (' . implode(',', $itemIds) . ')';
        }

        $sql = <<<SQL
SELECT
  i.id,
  i.uid,
  i.org_id,
  IFNULL(ib.item_barcode, '') AS barcode,
  i.`name`,
  i.english_name,
  i.basis_name,
  i.alias_name,
  i.mem_code,
  1 AS item_type,
  0 AS is_suit,
  IFNULL(GROUP_CONCAT(DISTINCT ispc.category_id ORDER BY ispc.category_id ASC SEPARATOR ','), '') AS item_support_pet_category,
  i.first_sale_type_id,
  ist.`name` AS first_sale_type_name,
  ist.alias_name AS first_sale_type_alias_name,
  i.second_sale_type_id,
  ist2.`name` AS second_sale_type_name,
  ist2.alias_name AS second_sale_type_alias_name,
  i.drug_type_id,
  idt.`name` AS drug_type_name,
  idt.alias_name AS drug_type_alias_name,
  i.first_category_id,
  ic.`name` AS first_category_name,
  ic.alias_name AS first_category_alias_name,
  i.second_category_id,
  ic2.`name` AS second_category_name,
  ic2.alias_name AS second_category_alias_name,
  i.brand_id,
  ibd.`name` AS brand_name,
  ibd.chinese_name AS brand_chinese_name,
  ibd.english_name AS brand_english_name,
  i.pack_unit,
  i.pack_unit_id,
  i.bulk_unit,
  i.bulk_unit_id,
  i.bulk_ratio,
  i.use_unit,
  i.use_unit_id,
  i.use_ratio,
  CONCAT(i.bulk_ratio, i.bulk_unit, '/', i.pack_unit) AS pack_spec,
  CONCAT(i.use_ratio, i.use_unit, '/', i.bulk_unit) AS bulk_spec,
  i.is_licensed,
  i.is_precise_metering,
  i.is_recipe_allow,
  i.is_retail_allow,
  i.is_pack_sale_allow,
  i.is_shelf_life,
  i.shelf_life,
  i.shelf_life_day,
  i.over_life_day,
  i.is_receive_allow,
  i.`desc`,
  i.remark,
  i.is_purchasable,
  i.`status`,
  i.created_at,
  i.updated_at
FROM
  item AS i
  LEFT JOIN item_barcode AS ib ON i.id = ib.item_id
  AND ib.`status` = 1
  LEFT JOIN item_sale_type AS ist ON i.first_sale_type_id = ist.id
  AND ist.is_leaf = 0
  LEFT JOIN item_sale_type AS ist2 ON i.second_sale_type_id = ist2.id
  AND ist2.is_leaf = 1
  LEFT JOIN item_drug_type AS idt ON i.drug_type_id = idt.id
  LEFT JOIN item_category AS ic ON i.first_category_id = ic.id
  AND ic.is_leaf = 0
  LEFT JOIN item_category AS ic2 ON i.second_category_id = ic2.id
  AND ic2.is_leaf = 1
  LEFT JOIN item_brand AS ibd ON i.brand_id = ibd.id
  LEFT JOIN item_support_pet_category AS ispc ON ispc.item_type = 1
  AND i.id = ispc.item_id
  AND ispc.`status` = 1
WHERE
  i.org_id = $orgId AND $itemWhere
GROUP BY
  i.id;
SQL;

        return DB::select($sql);
    }

    private static function DBGetAllBeautyItem(string|int $orgId, array $itemIds = []): array
    {
        $itemWhere = ' 1=1 ';
        if (!empty($itemIds))
        {
            $itemWhere .= ' AND i.id IN (' . implode(',', $itemIds) . ')';
        }

        $sql = <<<SQL
SELECT
  i.id,
  i.uid,
  i.org_id,
  i.`name`,
  i.english_name,
  i.alias_name,
  i.mem_code,
  4 AS item_type,
  0 AS is_suit,
  IFNULL(GROUP_CONCAT(DISTINCT ispc.category_id ORDER BY ispc.category_id ASC SEPARATOR ','), '') AS item_support_pet_category,
  i.first_sale_type_id,
  ist.`name` AS first_sale_type_name,
  ist.alias_name AS first_sale_type_alias_name,
  i.second_sale_type_id,
  ist2.`name` AS second_sale_type_name,
  ist2.alias_name AS second_sale_type_alias_name,
  i.first_category_id,
  ic.`name` AS first_category_name,
  ic.alias_name AS first_category_alias_name,
  i.second_category_id,
  ic2.`name` AS second_category_name,
  ic2.alias_name AS second_category_alias_name,
  i.use_unit,
  i.use_unit_id,
  0 AS is_recipe_allow,
  0 AS is_retail_allow,
  i.`desc`,
  i.remark,
  i.`status`,
  i.created_at,
  i.updated_at
FROM
  item_beauty AS i
  LEFT JOIN item_sale_type AS ist ON i.first_sale_type_id = ist.id
  AND ist.is_leaf = 0
  LEFT JOIN item_sale_type AS ist2 ON i.second_sale_type_id = ist2.id
  AND ist2.is_leaf = 1
  LEFT JOIN item_category AS ic ON i.first_category_id = ic.id
  AND ic.is_leaf = 0
  LEFT JOIN item_category AS ic2 ON i.second_category_id = ic2.id
  AND ic2.is_leaf = 1
  LEFT JOIN item_support_pet_category AS ispc ON ispc.item_type = 4
  AND i.id = ispc.item_id
  AND ispc.`status` = 1
WHERE
  i.org_id = $orgId AND $itemWhere
GROUP BY
  i.id;
SQL;

        return DB::select($sql);
    }

    private static function DBGetSuitItem(string|int $orgId): array
    {
        $sql = <<<SQL
SELECT
  i.id,
  i.uid,
  i.org_id,
  i.`name`,
  i.english_name,
  i.alias_name,
  i.mem_code,
  2 AS item_type,
  1 AS is_suit,
  1 AS is_recipe_allow,
  IFNULL(GROUP_CONCAT(DISTINCT ispc.category_id ORDER BY ispc.category_id ASC SEPARATOR ','), '') AS item_support_pet_category,
  i.sale_type_id as first_sale_type_id,
  ist.`name` AS first_sale_type_name,
  ist.alias_name AS sale_type_alias_name,
  i.use_unit,
  i.use_unit_id,
  i.is_print_items,
  i.is_use_items_price,
  i.`desc`,
  i.remark,
  i.`status`,
  i.created_at,
  i.updated_at
FROM
  item_suit AS i
  LEFT JOIN item_sale_type AS ist ON i.sale_type_id = ist.id AND ist.is_leaf = 0
  LEFT JOIN item_support_pet_category AS ispc ON ispc.item_type = 1 AND i.id = ispc.item_id AND ispc.`status` = 1
WHERE
  i.org_id = $orgId
GROUP BY
  i.id;
SQL;

        $getSuitItemsRes = DB::select($sql);
        if (empty($getSuitItemsRes))
        {
            return [];
        }

        // 获取关联的 item_suit_relation
        $suitItemIds      = array_map(fn($item) => $item->id, $getSuitItemsRes);
        $getSuitRelations = DB::table('item_suit_relation')
                              ->whereIn('suit_id', $suitItemIds)
                              ->where('status', 1)
                              ->get();
        if (empty($getSuitRelations))
        {
            return [];
        }

        $relatedAllItemIds   = [];
        $suitRelationItemIds = [];
        foreach ($getSuitRelations as $relation)
        {
            $itemId   = intval($relation->item_id);
            $itemType = intval($relation->item_type);
            if (empty($relatedAllItemIds[$itemType]) || !in_array($itemId, $relatedAllItemIds[$itemType]))
            {
                $relatedAllItemIds[$itemType][] = $itemId;
            }

            $suitRelationItemIds[$relation->suit_id][] = $itemId;
        }

        // 获取组合内的商品信息
        $getRelationItemInfo = [
            1 => self::DBGetAllItem($orgId, $relatedAllItemIds[1]),
            2 => [],
            4 => self::DBGetAllBeautyItem($orgId, $relatedAllItemIds[4]),
        ];

        if (empty($getRelationItemInfo))
        {
            return [];
        }

        // 将关联的商品信息填充进每个组合中
        foreach ($getSuitItemsRes as &$suitItem)
        {
            $suitId               = $suitItem->id;
            $suitItem->suit_items = [];

            if (!isset($suitRelationItemIds[$suitId]))
            {
                continue;
            }

            foreach ($getSuitRelations as $relation)
            {
                if ($relation->suit_id != $suitId)
                {
                    continue;
                }

                $itemType = intval($relation->item_type);
                $itemId   = intval($relation->item_id);
                if (!isset($getRelationItemInfo[$itemType]))
                {
                    continue;
                }

                foreach ($getRelationItemInfo[$itemType] as $item)
                {
                    if ($item->id == $itemId)
                    {
                        $item->suit_relation_id = $relation->id;
                        $item->quantity         = $relation->num;
                        $item->unit_type        = $relation->unit_type;
                        $suitItem->suit_items[] = $item;
                        break;
                    }
                }
            }
        }
        unset($suitItem);

        return $getSuitItemsRes;
    }

    private static function BuildIndexDoc(array $items): array
    {
        $result = [];
        foreach ($items as $value)
        {
            // 一级项目类型
            $curFirstSaleType = [];
            if ($value?->first_sale_type_id)
            {
                $curFirstSaleType = [
                    'id'         => intval($value?->first_sale_type_id),
                    'name'       => trim($value?->first_sale_type_name ?? ''),
                    'alias_name' => trim($value?->first_sale_type_alias_name ?? ''),
                ];
            }

            // 二级项目类型
            $curSecondSaleType = [];
            if (!empty($value->second_sale_type_id))
            {
                $curSecondSaleType = [
                    'id'         => intval($value?->second_sale_type_id),
                    'name'       => trim($value?->second_sale_type_name ?? ''),
                    'alias_name' => trim($value?->second_sale_type_alias_name ?? ''),
                ];
            }

            // 药品类型
            $curDrugType = [];
            if (!empty($value->drug_type_id))
            {
                $curDrugType = [
                    'id'         => intval($value?->drug_type_id),
                    'name'       => trim($value?->drug_type_name ?? ''),
                    'alias_name' => trim($value?->drug_type_alias_name ?? ''),
                ];
            }

            // 一级分类
            $curFirstCategory = [];
            if (!empty($value->first_category_id))
            {
                $curFirstCategory = [
                    'id'         => intval($value?->first_category_id),
                    'name'       => trim($value?->first_category_name ?? ''),
                    'alias_name' => trim($value?->first_category_alias_name ?? ''),
                ];
            }

            // 二级分类
            $curSecondCategory = [];
            if (!empty($value->second_category_id))
            {
                $curSecondCategory = [
                    'id'         => intval($value?->second_category_id),
                    'name'       => trim($value?->second_category_name ?? ''),
                    'alias_name' => trim($value?->second_category_alias_name ?? ''),
                ];
            }

            // 品牌
            $curBrand = [];
            if (!empty($value->brand_id))
            {
                $curBrand = [
                    'id'           => intval($value?->brand_id),
                    'name'         => trim($value?->brand_name ?? ''),
                    'chinese_name' => trim($value?->brand_chinese_name ?? ''),
                    'english_name' => trim($value?->brand_english_name ?? ''),
                ];
            }

            // 整装单位
            $curPackUnit = [];
            if (!empty($value->pack_unit_id))
            {
                $curPackUnit = [
                    'id'   => intval($value?->pack_unit_id),
                    'name' => trim($value?->pack_unit ?? ''),
                ];
            }

            // 散装单位
            $curBulkUnit = [];
            if (!empty($value->bulk_unit_id))
            {
                $curBulkUnit = [
                    'id'   => intval($value?->bulk_unit_id),
                    'name' => trim($value?->bulk_unit ?? ''),
                ];
            }

            // 计量单位
            $curUseUnit = [];
            if (!empty($value->use_unit_id))
            {
                $curUseUnit = [
                    'id'   => intval($value?->use_unit_id),
                    'name' => trim($value?->use_unit ?? ''),
                ];
            }

            // 组合明细
            $suitItemDetail = [];
            if (!empty($value->suit_items))
            {
                $suitItemDetail = self::BuildIndexDoc($value->suit_items);
            }

            $result[] = [
                'id'                        => intval($value?->id),
                'uid'                       => trim($value?->uid),
                'org_id'                    => intval($value?->org_id),
                'is_suit'                   => $value->is_suit ?? 0,
                'barcode'                   => trim($value?->barcode ?? ''),
                'name'                      => trim($value?->name),
                'english_name'              => trim($value?->english_name),
                'basis_name'                => trim($value?->basis_name ?? ''),
                'alias_name'                => trim($value?->alias_name ?? ''),
                'mem_code'                  => trim($value?->mem_code ?? ''),
                'item_type'                 => intval($value?->item_type),
                'item_support_pet_category' => array_values(array_filter(array_map('intval',
                                                                                   explode(',',
                                                                                           $value?->item_support_pet_category ?? '')))),
                'first_sale_type'           => $curFirstSaleType,
                'second_sale_type'          => $curSecondSaleType,
                'drug_type'                 => $curDrugType,
                'first_category'            => $curFirstCategory,
                'second_category'           => $curSecondCategory,
                'brand'                     => $curBrand,
                'pack_unit'                 => $curPackUnit,
                'bulk_unit'                 => $curBulkUnit,
                'use_unit'                  => $curUseUnit,
                'bulk_ratio'                => intval($value?->bulk_ratio ?? 0),
                'use_ratio'                 => number_format((float) ($value?->use_ratio ?? 0), 2, '.', ''),
                'pack_spec'                 => trim($value?->pack_spec ?? ''),
                'bulk_spec'                 => trim($value?->bulk_spec ?? ''),
                'is_licensed'               => boolval($value?->is_licensed ?? 0),
                'is_precise_metering'       => boolval($value?->is_precise_metering ?? 0),
                'is_recipe_allow'           => boolval($value?->is_recipe_allow ?? 0),
                'is_retail_allow'           => boolval($value?->is_retail_allow ?? 0),
                'is_pack_sale_allow'        => boolval($value?->is_pack_sale_allow ?? 0),
                'is_shelf_life'             => boolval($value?->is_shelf_life ?? 0),
                'shelf_life'                => trim($value?->shelf_life ?? ''),
                'shelf_life_day'            => intval($value?->shelf_life_day ?? 0),
                'over_life_day'             => intval($value?->over_life_day ?? 0),
                'is_receive_allow'          => boolval($value?->is_receive_allow ?? 0),
                'desc'                      => trim($value?->desc ?? ''),
                'remark'                    => trim($value?->remark ?? ''),
                'is_purchasable'            => $value?->is_purchasable ?? 0,
                'purchase_hospital_ids'     => [],
                'status'                    => intval($value?->status),
                'created_at'                => optional(Carbon::createFromFormat('Y-m-d H:i:s.u',
                                                                                 $value->created_at))->format('Y-m-d\TH:i:s.vP') ?? null,
                'updated_at'                => optional(Carbon::createFromFormat('Y-m-d H:i:s.u',
                                                                                 $value->updated_at))->format('Y-m-d\TH:i:s.vP') ?? null,
                'quantity'                  => formatDisplayNumber($value->quantity ?? 0),
                'unit_type'                 => $value->unit_type ?? '',
                'suit_items'                => $suitItemDetail,
            ];
        }

        return $result;
    }
}
