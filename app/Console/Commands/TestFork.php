<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>tie\Fork\Fork;

class TestFork extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-fork';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("开始并发测试...");
        $start = microtime(true);

        $results = Fork::new()
                       ->run(fn() => ['name' => '任务A', 'result' => sleep(1) . '完成于 ' . now()],
                           fn() => ['name' => '任务B', 'result' => sleep(2) . '完成于 ' . now()],
                           fn() => ['name' => '任务C', 'result' => sleep(3) . '完成于 ' . now()]);

        foreach ($results as $index => $result)
        {
            $this->info("结果 #{$index}：" . json_encode($result, JSON_UNESCAPED_UNICODE));
        }

        $this->info("总耗时：" . round((microtime(true) - $start), 3) . " 秒");
        $this->info("全部任务完成");
    }
}
