<?php

namespace App\Models;

/**
 * 商品中心:销售价
 */
class ItemSalePriceModel extends Model
{
    protected $table = 'item_sale_price';

    /**
     * 获取商品销售价格
     *
     * @param int   $itemType
     * @param array $itemIds
     * @param int   $orgId
     * @param int   $brandId
     * @param int   $provinceId
     * @param int   $cityId
     * @param int   $hospitalId
     *
     * @return array
     */
    public static function getItemSalePrice(int $itemType, array $itemIds, int $orgId, int $brandId, int $provinceId, int $cityId, int $hospitalId): array
    {
        if (empty($itemType) || empty($itemIds))
        {
            return [];
        }

        if (empty($orgId) || empty($brandId) || empty($provinceId) || empty($cityId) || empty($hospitalId))
        {
            return [];
        }

        $baseQuery = self::on()
                         ->where([
                                     ['item_type', '=', $itemType],
                                     ['status', '=', 1],
                                     ['start_time', '<=', getNowDateTime()],
                                     ['end_time', '>', getNowDateTime()]
                                 ])
                         ->whereIn('item_id', $itemIds);

        $records = (clone $baseQuery)->where(function ($query) use ($orgId, $brandId, $provinceId, $cityId, $hospitalId) {
            // 医院价格（最高优先级）
            $query->orWhere(function ($q) use ($hospitalId, $cityId, $provinceId, $brandId, $orgId) {
                $q->where('hospital_id', $hospitalId)
                  ->where('city_id', $cityId)
                  ->where('province_id', $provinceId)
                  ->where('brand_id', $brandId)
                  ->where('org_id', $orgId);
            });

            // 城市价格
            $query->orWhere(function ($q) use ($cityId, $provinceId, $brandId, $orgId) {
                $q->where('hospital_id', 0)
                  ->where('city_id', $cityId)
                  ->where('province_id', $provinceId)
                  ->where('brand_id', $brandId)
                  ->where('org_id', $orgId);
            });

            // 省份价格
            $query->orWhere(function ($q) use ($provinceId, $brandId, $orgId) {
                $q->where('hospital_id', 0)
                  ->where('city_id', 0)
                  ->where('province_id', $provinceId)
                  ->where('brand_id', $brandId)
                  ->where('org_id', $orgId);
            });

            // 品牌价格
            $query->orWhere(function ($q) use ($brandId, $orgId) {
                $q->where('hospital_id', 0)
                  ->where('city_id', 0)
                  ->where('province_id', 0)
                  ->where('brand_id', $brandId)
                  ->where('org_id', $orgId);
            });

            // 组织价格
            $query->orWhere(function ($q) use ($orgId) {
                $q->where('hospital_id', 0)
                  ->where('city_id', 0)
                  ->where('province_id', 0)
                  ->where('brand_id', 0)
                  ->where('org_id', $orgId);
            });
        });

        // 获取记录
        $grouped = $records->get()
                           ->groupBy('item_id');
        if ($grouped->isEmpty())
        {
            return [];
        }

        $results = [];
        foreach ($itemIds as $itemId)
        {
            $group = $grouped->get($itemId, collect());

            $result = [
                'hospital_price' => self::matchItemPrice($group, $hospitalId, $cityId, $provinceId, $brandId, $orgId),
                'city_price'     => self::matchItemPrice($group, 0, $cityId, $provinceId, $brandId, $orgId),
                'province_price' => self::matchItemPrice($group, 0, 0, $provinceId, $brandId, $orgId),
                'brand_price'    => self::matchItemPrice($group, 0, 0, 0, $brandId, $orgId),
                'org_price'      => self::matchItemPrice($group, 0, 0, 0, 0, $orgId),
                'sale_price'     => null,
            ];

            // 根据价格优先级
            foreach (['hospital_price', 'city_price', 'province_price', 'brand_price', 'org_price'] as $level)
            {
                if ($result[$level])
                {
                    $result['sale_price'] = [
                        'pack_sale_price' => $result[$level]['pack_sale_price'],
                        'bulk_sale_price' => $result[$level]['bulk_sale_price'],
                        'level'           => $level,
                    ];
                    break;
                }
            }

            $results[$itemId] = $result;
        }

        return $results;
    }

    /**
     * 匹配商品价格
     *
     * @param $group
     * @param $hospitalId
     * @param $cityId
     * @param $provinceId
     * @param $brandId
     * @param $orgId
     *
     * @return array
     */
    private static function matchItemPrice($group, $hospitalId, $cityId, $provinceId, $brandId, $orgId): array
    {
        $item = $group->first(function ($item) use ($hospitalId, $cityId, $provinceId, $brandId, $orgId) {
            return (int) $item->hospital_id === $hospitalId && (int) $item->city_id === $cityId && (int) $item->province_id === $provinceId && (int) $item->brand_id === $brandId && (int) $item->org_id === $orgId;
        });

        if (!$item)
        {
            return [];
        }

        return [
            'hospital_id'     => $item->hospital_id,
            'city_id'         => $item->city_id,
            'province_id'     => $item->province_id,
            'brand_id'        => $item->brand_id,
            'org_id'          => $item->org_id,
            'pack_sale_price' => $item->pack_sale_price,
            'bulk_sale_price' => $item->bulk_sale_price,
            'start_time'      => $item->start_time,
            'end_time'        => $item->end_time,
        ];
    }
}
