<?php

namespace App\Models;

/**
 * 医院自采商品白名单
 */
class ItemSelfPurchaseWhitelistModel extends Model
{
    protected $table = 'item_self_purchase_whitelist';

    /**
     * 获取组织下自采商品白名单列表
     *
     * @param array $searchParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return array
     */
    public static function getSelfPurchaseItemList(array $searchParams, int $iPage = 1, int $iPageSize = 50): array
    {
        // 搜索条件
        $orgId       = $searchParams['orgId'] ?? 0;
        $hospitalId  = $searchParams['hospitalId'] ?? 0;
        $supplierId  = $searchParams['supplierId'] ?? 0;
        $itemIds     = $searchParams['itemIds'] ?? [];
        $keywords    = $searchParams['keywords'] ?? '';
        $brandId     = $searchParams['brandId'] ?? 0;
        $isPurchased = $searchParams['isPurchased'] ?? 0;
        if (empty($orgId) || empty($hospitalId))
        {
            return [];
        }
        if (empty($supplierId))
        {
            return [];
        }
        if (empty($keywords) && empty($itemIds))
        {
            return [];
        }

        $builder = self::on()
                       ->select(['item.id'])
                       ->leftJoin(new ItemModel()->getTable() . ' as item',
                                  'item.id',
                                  '=',
                                  'item_self_purchase_whitelist.item_id')
                       ->leftJoin(new HospitalItemPurchaseRecordModel()->getTable() . ' as purchase_record',
                           function ($join) use ($hospitalId) {
                               $join->on('item.id', '=', 'purchase_record.item_id')
                                    ->where([
                                                'purchase_record.hospital_id' => $hospitalId,
                                                'purchase_record.status'      => 1
                                            ]);
                           })
                       ->where([
                                   'item_self_purchase_whitelist.org_id'      => $orgId,
                                   'item_self_purchase_whitelist.supplier_id' => $supplierId,
                                   'item_self_purchase_whitelist.status'      => 1,
                               ])
                       ->when(!empty($itemIds), function ($query) use ($itemIds) {
                           $query->whereIn('item.id', $itemIds);
                       })
                       ->when(!empty($keywords), function ($query) use ($keywords) {
                           $query->where(function ($query) use ($keywords) {
                               $query->where('item.name', 'like', '%' . $keywords . '%')
                                     ->orWhere('item.alias_name', 'like', '%' . $keywords . '%')
                                     ->orWhere('item.basis_name', 'like', '%' . $keywords . '%');
                           });
                       })
                       ->when($brandId > 0, function ($query) use ($brandId) {
                           $query->where(['item.brand_id' => $brandId]);
                       })
                       ->when($isPurchased, function ($query) {
                           $query->whereNotNull('purchase_record.id');
                       })
                       ->groupBy('item.id');

        // 分页
        if ($iPage > 0 && $iPageSize > 0)
        {
            $builder->offset(($iPage - 1) * $iPageSize)
                    ->limit($iPageSize);
        }

        // 获取条目数据
        return $builder->get()
                       ->toArray();
    }
}
