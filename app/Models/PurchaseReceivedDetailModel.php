<?php

namespace App\Models;

/**
 * 采购：到货单详情
 */
class PurchaseReceivedDetailModel extends Model
{
    protected $table = 'purchase_received_detail';

    public static function getOneReceivedDetailById(int $receivedDetailId, int $hospitalId = 0, int $itemId = 0): object|null
    {
        if (empty($receivedDetailId))
        {
            return null;
        }

        $getWhere = ['purchase_received_detail.id' => $receivedDetailId, 'purchase_received_detail.status' => 1];
        if (!empty($hospitalId))
        {
            $getWhere['received.hospital_id'] = $hospitalId;
        }
        if (!empty($itemId))
        {
            $getWhere['purchase_received_detail.item_id'] = $itemId;
        }

        return PurchaseReceivedDetailModel::on()
                                          ->leftJoin(new PurchaseReceivedModel()->getTable() . ' as received',
                                                     'purchase_received_detail.received_id',
                                                     '=',
                                                     'received.id')
                                          ->where($getWhere)
                                          ->first();
    }
}
