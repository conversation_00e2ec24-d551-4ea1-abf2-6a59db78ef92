<?php

namespace App\Models;

use DB;

/**
 * 采购：到货单
 */
class PurchaseReceivedModel extends Model
{
    protected $table = 'purchase_received';

    /**
     * 获取到货单列表
     *
     * @param     $arrParams
     * @param int $iPage
     * @param int $iPageSize
     *
     * @return array
     */
    public static function getReceivedListData($arrParams, int $iPage = 1, int $iPageSize = 10): array
    {
        $getWhere   = [];
        $getOrWhere = []; // 多个维度查询条件，需要与其他条件使用and

        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['purchase_received.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['purchase_received.status', '=', 1];

        // 多维度关键字查询
        if (!empty($arrParams['keywords']))
        {
            // 1.到货单号
            $getOrWhere[] = ['purchase_received.received_code', 'like', '%' . $arrParams['keywords'] . '%'];

            // 2.采购单编号
            $getOrWhere[] = ['purchase_received.purchase_code', 'like', '%' . $arrParams['keywords'] . '%'];

            // 3.商品条码
            $getOrWhere[] = ['received_detail.item_barcode', 'like', '%' . $arrParams['keywords'] . '%'];
        }

        // 到货单ID
        if (!empty($arrParams['receivedId']))
        {
            $getWhere[] = ['purchase_received.id', '=', $arrParams['receivedId']];
        }

        // 到货单创建时间
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['purchase_received.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['purchase_received.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 入库状态
        if (isset($arrParams['inboundStatus']) && is_numeric($arrParams['inboundStatus']))
        {
            $getWhere[] = ['purchase_received.inbound_status', '=', $arrParams['inboundStatus']];
        }

        // 采购单创建人
        if (!empty($arrParams['createUserUid']) || !empty($arrParams['createUserId']))
        {
            if (!empty($arrParams['createUserId']))
            {
                $getWhere[] = ['purchase_received.created_by', '=', $arrParams['createUserId']];
            }
            else
            {
                $getWhere[] = ['user.uid', '=', $arrParams['createUserUid']];
            }
        }

        // 采购类型
        if (!empty($arrParams['purchaseType']))
        {
            $getWhere[] = ['purchase_received.purchase_type', '=', $arrParams['purchaseType']];
        }

        // 采购供应商
        if (!empty($arrParams['supplierUid']) || !empty($arrParams['supplierId']))
        {
            if (!empty($arrParams['supplierId']))
            {
                $getWhere[] = ['purchase_received.supplier_id', '=', $arrParams['supplierId']];
            }
            else
            {
                $getWhere[] = ['supplier.uid', '=', $arrParams['supplierUid']];
            }
        }

        // 调拨来源门店
        if (!empty($arrParams['allotHospitalUid']) || !empty($arrParams['allotHospitalId']))
        {
            if (!empty($arrParams['allotHospitalId']))
            {
                $getWhere[] = ['purchase_received.allot_hospital_id', '=', $arrParams['allotHospitalId']];
            }
            else
            {
                $getWhere[] = ['hospital.uid', '=', $arrParams['allotHospitalUid']];
            }
        }

        $builderQuery = self::on()
                            ->select([
                                         'purchase_received.*',
                                         'supplier.uid as supplier_uid',
                                         'supplier.name as supplier_name',
                                         'hospital.uid as hospital_uid',
                                         'hospital.alias_name as hospital_alias_name',
                                         'user.uid as user_uid',
                                         'user.name as user_name',
                                     ])
                            ->leftJoin((new PurchaseSupplierModel()->getTable()) . ' as supplier', 'purchase_received.supplier_id', '=', 'supplier.id')
                            ->leftJoin((new HospitalModel()->getTable()) . ' as hospital', 'purchase_received.allot_hospital_id', '=', 'hospital.id')
                            ->leftJoin((new UsersModel()->getTable()) . ' as user', 'purchase_received.created_by', '=', 'user.id')
                            ->when(!empty($getOrWhere), function ($query) use ($getOrWhere) {
                                $query->leftJoin((new PurchaseReceivedDetailModel()->getTable()) . ' as received_detail',
                                                 'purchase_received.id',
                                                 '=',
                                                 'received_detail.received_id');
                            })
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->orderBy('purchase_received.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count(DB::raw('distinct purchase_received.id'));
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($iPage > 0 && $iPageSize > 0)
        {
            $builderQuery->offset(($iPage - 1) * $iPageSize)
                         ->limit($iPageSize);
        }

        // 获取条目数据
        $purchaseOrderList = $builderQuery->groupBy(['purchase_received.id'])
                                          ->get()
                                          ->toArray();

        return ['total' => $totalCount, 'data' => $purchaseOrderList];
    }
}
