<?php

namespace App\Models;

/**
 * 医院：云小票打印机
 * 佳博打印机
 */
class HospitalCloudPrintersModel extends Model
{
    protected $table = 'his_hospital_cloud_printers';

    public static function getOneByDeviceId(string $deviceId, ?int $hospitalId = null): object|null
    {
        return self::on()
                   ->where(['device_id' => $deviceId, 'status' => 1])
                   ->when(!empty($hospitalId), function ($query) use ($hospitalId) {
                       $query->where(['hospital_id' => $hospitalId]);
                   })
                   ->first();
    }
}
