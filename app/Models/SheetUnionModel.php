<?php

namespace App\Models;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;
use App\Enums\SheetBusinessTypeEnum;
use App\Models\Interfaces\SheetUnionInterface;
use RuntimeException;

class SheetUnionModel extends Model
{

    /**
     * 购买单业务类型与数据模型映射
     *
     * @var array
     */
    const array SHEET_BUSINESS_MODEL_MAP = [
        SheetBusinessTypeEnum::Retail->value => RetailSheetModel::class,
        SheetBusinessTypeEnum::Beauty->value => BeautyServiceSheetModel::class,
    ];

    /**
     * 获取待付款的服务单的所有会员
     *
     * @param int $hospitalId
     *
     * @return array
     */
    public static function GetSheetMemberOptions(int $hospitalId): array
    {
        // 1. 先拿到 union SQL 和绑定
        $unionQuery = self::buildBaseUnionQuery($hospitalId);

        // 2. 包一层子查询，执行窗口函数
        $windowQuery = DB::table(DB::raw("({$unionQuery->toSql()}) as union_sub"))
                         ->mergeBindings($unionQuery)
                         ->select(
                             'member_id',
                             'updated_at',
                             DB::raw('ROW_NUMBER() OVER (PARTITION BY member_id ORDER BY updated_at DESC) AS rn')
                         );

        // 3. 再包一层，筛选最新记录 rn=1
        return DB::table(DB::raw("({$windowQuery->toSql()}) as ranked"))
                 ->mergeBindings($windowQuery)
                 ->where('rn', 1)
                 ->orderBy('updated_at', 'desc')
                 ->pluck('member_id')
                 ->toArray();
    }


    /**
     * 获取待付款的服务单的所有创建人
     *
     * @param int $hospitalId
     *
     * @return array
     */
    public static function GetSheetCreateUsersOptions(int $hospitalId): array
    {
        $unionQuery = self::buildBaseUnionQuery($hospitalId);

        return DB::table(DB::raw("({$unionQuery->toSql()}) as union_sub"))
                 ->mergeBindings($unionQuery)
                 ->select('created_by')
                 ->distinct()
                 ->orderByDesc('updated_at')
                 ->pluck('created_by')
                 ->toArray();
    }

    /**
     * 获取待付款的服务单列表
     *
     * 无分页
     *
     * @param int   $hospitalId
     * @param array $filters
     * @param array $businessTypes
     *
     * @return Collection
     */
    public static function GetUnpaidSheetList(int $hospitalId, array $filters = [], array $businessTypes = []
    ): Collection
    {
        $unionQuery = self::buildBaseUnionQuery($hospitalId, $filters, $businessTypes);

        return DB::table(DB::raw("({$unionQuery->toSql()}) as union_sub"))
                 ->mergeBindings($unionQuery)
                 ->orderByDesc('created_at')
                 ->get();
    }

    /**
     * 生成基础 union 查询（不带排序分页）
     *
     * @param int   $hospitalId    医院ID
     * @param array $filters       过滤条件：
     *                             [
     *                             'member_id'   => ...,
     *                             'created_by'  => ...,
     *                             'created_start_time'  => ...,
     *                             'created_end_time'    => ...,
     *                             ]
     * @param array $businessTypes 业务类型
     *
     * @return Builder
     * @throws RuntimeException
     */
    protected static function buildBaseUnionQuery(int $hospitalId, array $filters = [], array $businessTypes = []
    ): Builder
    {
        $models     = self::GetSheetModelsClass($businessTypes);
        $unionQuery = null;

        foreach ($models as $model)
        {
            /** @var SheetUnionInterface $instance */
            $instance = new $model();
            $query    = $instance->getUnpaidQuery(
                hospitalId:       $hospitalId,
                memberId:         $filters['member_id'] ?? null,
                createdBy:        $filters['created_by'] ?? null,
                createdStartTime: $filters['created_start_time'] ?? null,
                createdEndTime:   $filters['created_end_time'] ?? null
            );

            if ($unionQuery === null)
            {
                $unionQuery = $query;
            }
            else
            {
                $unionQuery->unionAll($query);
            }
        }

        if ($unionQuery === null)
        {
            throw new RuntimeException('无有效的购买单数据模型');
        }

        return $unionQuery;
    }

    /**
     * 检查数据模型是否符合要求
     *
     * @return bool
     */
    private static function CheckSheetModelIsValid(): bool
    {
        return array_all(
            self::SHEET_BUSINESS_MODEL_MAP,
            fn($model) => is_subclass_of($model, SheetUnionInterface::class)
        );
    }

    /**
     * 获取购买单数据模型
     *
     * @param array $businessTypes
     *
     * @return array
     * @throws RuntimeException
     */
    private static function GetSheetModelsClass(array $businessTypes = []): array
    {
        if (!self::CheckSheetModelIsValid())
        {
            throw new RuntimeException('购买单数据模型配置错误');
        }

        if (empty($businessTypes))
        {
            return self::SHEET_BUSINESS_MODEL_MAP;
        }

        return array_intersect_key(self::SHEET_BUSINESS_MODEL_MAP, array_flip($businessTypes));
    }

}
