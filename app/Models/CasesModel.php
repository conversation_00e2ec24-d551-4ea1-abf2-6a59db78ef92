<?php

namespace App\Models;

/**
 * 宠物病历
 */
class CasesModel extends Model
{
    protected $table = 'his_cases';

    /**
     * 根据门诊ID、住院ID，获取病历
     *
     * @param int $hospitalId
     * @param int $sourceType
     * @param int $sourceRelationId
     *
     * @return array
     */
    public static function getCaseByRelationId(int $hospitalId, int $sourceType, int $sourceRelationId): array
    {
        if (empty($hospitalId) || empty($sourceType) || empty($sourceRelationId))
        {
            return [];
        }

        $getCaseRes = self::on()
                          ->where([
                                      'hospital_id'        => $hospitalId,
                                      'source_type'        => $sourceType,
                                      'source_relation_id' => $sourceRelationId
                                  ])
                          ->first();

        return $getCaseRes ? $getCaseRes->toArray() : [];
    }
}
