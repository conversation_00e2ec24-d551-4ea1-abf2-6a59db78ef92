<?php

namespace App\Models;

use Illuminate\Support\Collection;

/**
 * 住院
 */
class InpatientModel extends Model
{
    protected $table = 'his_inpatient';

    /**
     * 根据住院编码获取住院门诊
     *
     * @param string $inpatientCode
     *
     * @return array
     */
    public static function getInpatientByCode(string $inpatientCode): array
    {
        if (empty($inpatientCode))
        {
            return [];
        }

        $getInpatientRes = self::on()
                               ->where(['inpatient_code' => $inpatientCode])
                               ->first();

        return $getInpatientRes ? $getInpatientRes->toArray() : [];
    }

    /**
     * 获取用户宠物的住院记录
     *
     * @param int   $memberId
     * @param int   $hospitalId
     * @param array $petIds
     * @param array $status
     * @param bool  $onWritePdo
     *
     * @return Collection|array
     */
    public static function getInpatientByPetId(int $hospitalId, int $memberId, array $petIds, array $status = [], bool $onWritePdo = false): Collection|array
    {
        if (empty($memberId) || empty($hospitalId) || empty($petIds))
        {
            return [];
        }

        return self::on()
                   ->select([
                                'his_inpatient.*',
                                'his_hospital_users.work_title as work_title',
                                'his_registrations.created_at as registration_time',
                                'his_users.name as doctor_name'
                            ])
                   ->leftJoin(new HospitalUserModel()->getTable() . ' as his_hospital_users', function ($query) {
                       $query->on('his_hospital_users.hospital_id', '=', 'his_inpatient.hospital_id')
                             ->on('his_hospital_users.user_id', '=', 'his_inpatient.doctor_id');
                   })
                   ->leftJoin(new UsersModel()->getTable() . ' as his_users', function ($query) {
                       $query->on('his_inpatient.doctor_id', '=', 'his_users.id');
                   })
                   ->leftJoin(new RegistrationsModel()->getTable() . ' as his_registrations', function ($query) {
                       $query->on('his_inpatient.relation_registration_id', '=', 'his_registrations.id');
                   })
                   ->where([
                               'his_inpatient.hospital_id' => $hospitalId,
                               'his_inpatient.member_id'   => $memberId
                           ])
                   ->whereIn('his_inpatient.pet_id', $petIds)
                   ->when(!empty($status), function ($query) use ($status) {
                       $query->whereIn('his_inpatient.status', $status);
                   })
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->orderBy('his_inpatient.id', 'desc')
                   ->get();
    }
}
