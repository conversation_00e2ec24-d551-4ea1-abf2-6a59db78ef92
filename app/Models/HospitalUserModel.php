<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;

/**
 * 医院用户表
 */
class HospitalUserModel extends Model
{
    protected $table = 'his_hospital_users';

    /**
     * 获取用户在所有医院用户身份
     *
     * @param int      $userId
     * @param int|null $status
     * @param bool     $onWritePdo
     *
     * @return Collection|null
     */
    public static function getUserHospitalUsers(int $userId, int|null $status = 1, bool $onWritePdo = false): Collection|null
    {
        if ($userId <= 0)
        {
            return null;
        }

        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where(['user_id' => $userId])
                   ->when(is_int($status), function ($query) use ($status) {
                       $query->where(['status' => $status]);
                   })
                   ->orderBy('id', 'desc')
                   ->get();
    }

    /**
     * 获取用户在某医院用户身份
     *
     * @param int      $userId
     * @param int      $hospitalId
     * @param int|null $status
     * @param bool     $onWritePdo
     *
     * @return object|null
     */
    public static function getUserHospitalUser(int $userId, int $hospitalId, int|null $status = 1, bool $onWritePdo = false): object|null
    {
        if ($userId <= 0 || $hospitalId <= 0)
        {
            return null;
        }

        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where(['user_id' => $userId, 'hospital_id' => $hospitalId])
                   ->when(is_int($status), function ($query) use ($status) {
                       $query->where(['status' => $status]);
                   })
                   ->first();
    }

    /**
     * 获取某医院的用户列表
     *
     * @param int      $hospitalId     医院ID
     * @param int      $userId         医生ID
     * @param array    $roleIds        角色ID数组
     * @param int|null $status         用户状态
     * @param array    $excludeRoleIds 排除拥有某些角色的用户
     *
     * @return Collection|null
     */
    public static function getHospitalUsers(int $hospitalId, array $userIds = [], array $roleIds = [], int|null $status = 1, array $excludeRoleIds = []): Collection|null
    {
        if (empty($hospitalId))
        {
            return null;
        }

        // TODO 需要排除拥有此角色的用户
        $excludeRoleUserIds = [];
        if (!empty($excludeRoleIds))
        {
        }

        return self::on()
                   ->select([
                                'his_hospital_users.*',
                                'h_user.name as user_name',
                                'h_user.id as user_id',
                                'h_user.uid as user_uid'
                            ])
                   ->where(['his_hospital_users.hospital_id' => $hospitalId])
                   ->leftJoin(new UsersModel()->getTable() . ' as h_user',
                              'h_user.id',
                              '=',
                              'his_hospital_users.user_id')
                   ->when(!empty($userIds), function ($query) use ($userIds) {
                       $query->whereIn('his_hospital_users.user_id', $userIds);
                   })
                   ->when(!empty($roleIds), function ($query) use ($roleIds) {
                   })
                   ->when(is_int($status), function ($query) use ($status) {
                       $query->where(['his_hospital_users.status' => $status]);
                       $query->where(['h_user.status' => $status]);
                   })
                   ->when(!empty($excludeRoleUserIds),
                       function ($query) use ($excludeRoleUserIds) {
                       })
                   ->get();
    }
}
