<?php

namespace App\Models;

/**
 * 采购-供应商
 */
class PurchaseSupplierModel extends Model
{
    protected $table = 'purchase_supplier';

    /**
     * 根据采购类型和城市ID获取供应商
     * 注：如果供应商没有关联城市，则不限制城市。反之则只获取当前城市下的供应商
     *
     * @param int $purchaseType
     * @param int $hospitalCityId
     *
     * @return array
     */
    public static function getSupplierByPurchaseTypeAndCityId(int $purchaseType, int $hospitalCityId): array
    {
        if (empty($purchaseType) || empty($hospitalCityId))
        {
            return [];
        }

        $supplierTableName     = self::table();
        $supplierCityTableName = PurchaseSupplierCityModel::table();

        return self::on()
                   ->select(["$supplierTableName.*"])
                   ->leftJoin($supplierCityTableName,
                              "$supplierCityTableName.supplier_id",
                              '=',
                              "$supplierTableName.id")
                   ->where([
                               "$supplierTableName.purchase_type" => $purchaseType,
                               "$supplierTableName.status"        => 1,
                           ])
                   ->where(function ($query) use ($supplierCityTableName, $hospitalCityId) {
                       // 没有关联城市限制
                       $query->whereNull("$supplierCityTableName.id")
                             ->orWhere(function ($q) use ($supplierCityTableName, $hospitalCityId) {
                                 // 有城市限制，获取当前支持当前城市的供应商
                                 $q->where([
                                               "$supplierCityTableName.city_id" => $hospitalCityId,
                                               "$supplierCityTableName.status"  => 1,
                                           ]);
                             });
                   })
                   ->groupBy("$supplierTableName.id")
                   ->get()
                   ->toArray();
    }
}
