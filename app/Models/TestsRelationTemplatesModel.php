<?php

namespace App\Models;

/**
 * 化验项关联化验模版
 */
class TestsRelationTemplatesModel extends Model
{
    protected $table = 'his_tests_relation_templates';

    /**
     * 获取化验项关联的模版
     *
     * @param int   $itemId
     * @param array $templateIds
     * @param array $templateUids
     * @param bool  $withValidStatus
     *
     * @return array
     */
    public static function getTestItemRelationTemplates(int $itemId, array $templateIds = [], array $templateUids = [], bool $withValidStatus = true): array
    {
        if (empty($itemId))
        {
            return [];
        }

        return self::on()
                   ->select([
                                'his_tests_relation_templates.*',
                                'template.uid as template_uid',
                                'template.name as template_name',
                                'template.is_need_position as template_is_need_position',
                                'template.is_need_mode as template_is_need_mode',
                                'template.status as template_status',
                            ])
                   ->where(['his_tests_relation_templates.item_id' => $itemId])
                   ->leftJoin(new TestsReportTemplatesModel()->getTable() . ' as template',
                              'his_tests_relation_templates.template_id',
                              '=',
                              'template.id')
                   ->when(!empty($templateIds), function ($query) use ($templateIds) {
                       $query->whereIn('his_tests_relation_templates.template_id', $templateIds);
                   })
                   ->when(!empty($templateUids), function ($query) use ($templateUids) {
                       $query->whereIn('template.uid', $templateUids);
                   })
                   ->when(!empty($withValidStatus), function ($query) {
                       $query->where(['his_tests_relation_templates.status' => 1]);
                       $query->where(['template.status' => 1]);
                   })
                   ->get()
                   ->toArray();
    }
}
