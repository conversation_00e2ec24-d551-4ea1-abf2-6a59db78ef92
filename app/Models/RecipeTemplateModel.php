<?php

namespace App\Models;

/**
 * 处方模版
 */
class RecipeTemplateModel extends Model
{
    protected $table = 'his_recipe_templates';

    /**
     * 获取模版列表
     *
     * @param array $searchParams
     *
     * @return array
     */
    public static function getTemplateList(array $searchParams): array
    {
        // 医院、医生参数
        $orgId      = $searchParams['orgId'] ?? 0;
        $hospitalId = $searchParams['hospitalId'] ?? 0;
        $doctorId   = $searchParams['doctorId'] ?? 0;

        // 检索条件， 模版类型，0-全部模版 1-我的模版 2-本院模版 3-集团模版
        $type       = $searchParams['type'] ?? 0;
        $templateId = $searchParams['templateId'] ?? 0;
        $keyword    = $searchParams['keyword'] ?? '';

        // 基础条件
        $baseWhere = "his_recipe_templates.status = 1 and configure.status = 1 and configure.org_id = $orgId";
        if (!empty($templateId))
        {
            $baseWhere .= " and his_recipe_templates.id = $templateId";
        }
        if (!empty($keyword))
        {
            $baseWhere .= " and his_recipe_templates.name like '%$keyword%'";
        }

        // 可见范围筛选条件
        match ($type)
        {
            // 个人模版
            1 => $configureWhere = '(configure.hospital_id = :hospitalId and configure.doctor_id = :doctorId)',

            // 本院模版
            2 => $configureWhere = '(configure.hospital_id = :hospitalId and (configure.doctor_id = 0 OR configure.doctor_id = :doctorId))',

            // 集团模版
            3 => $configureWhere = '(configure.hospital_id = 0 and configure.doctor_id = 0)',

            // 全部（集团 + 本院 + 个人）
            default => $configureWhere = '(
            (configure.hospital_id = 0 and configure.doctor_id = 0) OR
            (configure.hospital_id = :hospitalId and configure.doctor_id = 0) OR
            (configure.hospital_id = :hospitalId and doctor_id = :doctorId)
            )',
        };

        // 可见范围筛选条件
        $configureWhere = str_replace([':hospitalId', ':doctorId'], [$hospitalId, $doctorId], $configureWhere);
        $getWhere       = $baseWhere . ' and ' . $configureWhere;

        return self::on()
                   ->select(['his_recipe_templates.*', 'configure.id as template_configure_id'])
                   ->leftJoin(RecipeTemplateConfigureModel::table() . ' as configure',
                              'configure.template_id',
                              '=',
                              'his_recipe_templates.id')
                   ->whereRaw($getWhere)
                   ->get()
                   ->toArray();
    }
}
