<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class BalanceRechargeSheetModel extends Model
{
    protected $table = 'member_balance_recharge_sheet';

    /**
     * 创建充值购买单
     *
     * @param array $sheetData
     * @param array $sheetItemsData
     *
     * @return int|null
     * @throws Throwable
     */
    public static function DoCreateSheet(array $sheetData, array $sheetItemsData): int|null
    {
        if (empty($sheetData) || empty($sheetItemsData))
        {
            return null;
        }

        try
        {
            DB::beginTransaction();

            $sheetId = self::insertOne($sheetData);
            if (empty($sheetId))
            {
                DB::rollBack();

                return null;
            }

            foreach ($sheetItemsData as $key => $itemInfo)
            {
                $sheetItemsData[$key]['sheet_id'] = $sheetId;
            }

            $insertItemRes = BalanceRechargeSheetItemModel::insert($sheetItemsData);
            if (empty($insertItemRes))
            {
                DB::rollBack();

                return null;
            }

            DB::commit();

            return $sheetId;

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 创建充值单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return null;
        }
    }
}
