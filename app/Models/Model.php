<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model as BaseModel;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class Model
 */
abstract class Model extends BaseModel
{
    /**
     * 将带有时区的标准时间，统一转化成可视化时间
     * @var string[]
     */
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 模型日期列的存储格式
     *
     * @var string
     */
    protected $dateFormat = 'Y-m-d H:i:s';

    /**
     * 与模型关联的数据表.
     *
     * @var string
     */
    protected $table = '';

    /**
     * 获取表名称
     *
     * @return string
     */
    public static function table(): string
    {
        return static::make()
                     ->getTable();
    }

    /**
     *指定格式化时间不增加时区，只有格式
     *
     * @param \DateTimeInterface $date
     *
     * @return string
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format($this->dateFormat);
    }

    /**
     * 格式化时间
     *
     * @return Attribute
     */
    protected function createdAt(): Attribute
    {
        return Attribute::make(get: fn($value) => $value ? Carbon::parse($value)
                                                                 ->format($this->dateFormat) : null);
    }

    /**
     * 格式化时间
     *
     * @return Attribute
     */
    protected function updatedAt(): Attribute
    {
        return Attribute::make(get: fn($value) => $value ? Carbon::parse($value)
                                                                 ->format($this->dateFormat) : null);
    }

    /**
     * 插入一条
     *
     * @param array $data
     *
     * @return int
     */
    public static function insertOne(array $data): int
    {
        return self::on()
                   ->insertGetId($data);
    }

    /**
     * 插入多条
     *
     * @param array $data
     *
     * @return bool
     */
    public static function insert(array $data): bool
    {
        return self::on()
                   ->insert($data);
    }

    /**
     * 更新一条
     *
     * @param int   $id
     * @param array $data
     *
     * @return int
     */
    public static function updateOne(int $id, array $data): int
    {
        return self::on()
                   ->where(['id' => $id])
                   ->update($data);
    }

    /**
     * UID更新一条
     *
     * @param string $uid
     * @param array  $data
     *
     * @return int
     */
    public static function updateOneByUid(string $uid, array $data): int
    {
        return self::on()
                   ->where(['uid' => $uid])
                   ->update($data);
    }

    /**
     * 获取一条
     *
     * @param int  $id
     * @param bool $onWritePdo
     *
     * @return object|null
     */
    public static function getOne(int $id, bool $onWritePdo = false): object|null
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where(['id' => $id])
                   ->first();
    }

    /**
     * 获取多条
     *
     * @param array $ids
     * @param bool  $onWritePdo
     *
     * @return object|null
     */
    public static function getAllByIds(array $ids = [], bool $onWritePdo = false): Collection|null
    {
        return self::on()
                   ->when($ids, function ($query) use ($ids) {
                       $query->whereIn('id', $ids);
                   })
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->get();
    }

    /**
     * UID获取一条
     *
     * @param string $uid
     * @param bool   $onWritePdo
     *
     * @return object|null
     */
    public static function getOneByUid(string $uid, bool $onWritePdo = false): object|null
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where(['uid' => $uid])
                   ->first();
    }

    /**
     * 获取一条：通过ID或UID
     *
     * @param int      $id
     * @param string   $uid
     * @param int|null $orgId
     * @param int|null $brandId
     * @param int|null $hospitalId
     * @param bool     $onWritePdo
     *
     * @return object|null
     */
    public static function getOneByIdOrUid(
        int    $id = 0,
        string $uid = '',
        ?int   $orgId = null,
        ?int   $brandId = null,
        ?int   $hospitalId = null,
        bool   $onWritePdo = false
    ): object|null
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->when($id > 0 && $uid == '', function ($query) use ($id) {
                       $query->where(['id' => $id]);
                   })
                   ->when($id <= 0 && $uid != '', function ($query) use ($uid) {
                       $query->where(['uid' => $uid]);
                   })
                   ->when($id > 0 && $uid != '', function ($query) use ($id, $uid) {
                       $query->where(['id' => $id, 'uid' => $uid]);
                   })
                   ->when($orgId, function ($query) use ($orgId) {
                       $query->where(['org_id' => $orgId]);
                   })
                   ->when($brandId, function ($query) use ($brandId) {
                       $query->where(['brand_id' => $brandId]);
                   })
                   ->when($hospitalId, function ($query) use ($hospitalId) {
                       $query->where(['hospital_id' => $hospitalId]);
                   })
                   ->first();
    }

    /**
     * 获取多条：通过IN
     *
     * @param array<int> $ids
     * @param bool       $onWritePdo
     *
     * @return Collection|null
     */
    public static function getManyByIds(array $ids, bool $onWritePdo = false): Collection|null
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->whereIn('id', $ids)
                   ->orderByRaw("FIELD(id, " . implode(',', $ids) . ")")
                   ->get();
    }

    /**
     * 是否存在
     *
     * @param int  $id
     * @param bool $onWritePdo
     *
     * @return bool
     */
    public static function isExists(int $id, bool $onWritePdo = false): bool
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where(['id' => $id])
                   ->exists();
    }

    /**
     * UID是否存在
     *
     * @param string $uid
     * @param bool   $onWritePdo
     *
     * @return bool
     */
    public static function isExistsByUid(string $uid, bool $onWritePdo = false): bool
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where(['uid' => $uid])
                   ->exists();
    }

    /**
     * 根据条件判断是否存在
     *
     * @param array $where
     * @param bool  $onWritePdo
     *
     * @return bool
     */
    public static function isExistsByWhere(array $where, bool $onWritePdo = false): bool
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where($where)
                   ->exists();
    }

    public static function getOneByPhone(string $phone, bool $onWritePdo = false): object|null
    {
        if ($phone == '')
        {
            return null;
        }

        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where(['phone' => $phone])
                   ->first();
    }

    /**
     * 获取总数
     *
     * @param array $where
     * @param array $whereIn
     * @param array $whereNotIn
     * @param bool  $onWritePdo
     *
     * @return int
     */
    public static function getTotalNumber(
        array $where = [], array $whereIn = [], array $whereNotIn = [], bool $onWritePdo = false
    ): int
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->when($where, function ($query) use ($where) {
                       $query->where($where);
                   })
                   ->when($whereIn, function ($query) use ($whereIn) {
                       foreach ($whereIn as $column => $val)
                       {
                           $query->whereIn($column, $val);
                       }
                   })
                   ->when($whereNotIn, function ($query) use ($whereNotIn) {
                       foreach ($whereNotIn as $noColumn => $noVal)
                       {
                           $query->whereNotIn($noColumn, $noVal);
                       }
                   })
                   ->count();
    }

    /**
     * 批量获取数据
     *
     * @param array    $fields
     * @param array    $where
     * @param array    $orWhere
     * @param array    $whereIn
     * @param array    $whereNotIn
     * @param string   $group
     * @param array    $orderBys
     * @param string   $keyBy
     * @param int|null $pageIndex
     * @param int|null $pageSize
     *
     * @return array
     */
    public static function getData(
        array  $fields = ['*'], array $where = [], array $orWhere = [], array $whereIn = [], array $whereNotIn = [],
        string $group = '', array $orderBys = [], string $keyBy = '', ?int $pageIndex = 0, ?int $pageSize = 0
    ): array
    {
        if (empty($where) && empty($orWhere) && empty($whereIn))
        {
            return [];
        }

        // 初始化实例
        $query = self::on();
        if (!empty($where))
        {
            $query->where($where);
        }

        // orWhere,如果where不为空，那么or执行的是 () and (xx OR xx)。如果where为空，那么or执行的是 (xx OR xx)
        if (!empty($orWhere))
        {
            if (!empty($where))
            {
                $query->where(function ($subQuery) use ($orWhere) {
                    $subQuery->orWhere($orWhere);
                });
            }
            else
            {
                $query->orWhere($orWhere);
            }
        }

        // whereIn
        if (!empty($whereIn))
        {
            foreach ($whereIn as $column => $val)
            {
                $query->whereIn($column, $val);
            }
        }

        // whereNotIn
        if (!empty($whereNotIn))
        {
            foreach ($whereNotIn as $noColumn => $noVal)
            {
                $query->whereNotIn($noColumn, $noVal);
            }
        }

        // 分组
        if (!empty($group))
        {
            $query->groupBy($group);
        }

        // 排序
        if (!empty($orderBys))
        {
            foreach ($orderBys as $column => $orderBy)
            {
                $query->orderBy($column, $orderBy);
            }
        }

        // 分页
        if ($pageIndex !== '' && $pageIndex >= 0 && $pageSize !== '' && $pageSize > 0)
        {
            $query->offset(($pageIndex - 1) * $pageSize)
                  ->limit($pageSize);
        }

        // 查询字段
        $query->select($fields);

        // 获取结果集
        $results = $query->get();

        // 如果指定了键名，则应用它
        if (!empty($keyBy))
        {
            $results = $results->keyBy($keyBy);
        }

        // 转换为数组并返回
        return $results->toArray();
    }
}
