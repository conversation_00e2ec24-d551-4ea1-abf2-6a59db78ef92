<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

/**
 * 客户充值订单
 */
class BalanceRechargeOrderModel extends Model
{
    protected $table = 'member_balance_recharge_orders';

    /**
     * 获取医院充值活动使用次数
     *
     * @param int   $hospitalId    医院ID
     * @param array $activitiesIds 充值活动ID
     *
     * @return Collection
     */
    public static function GetHospitalRechargeActivitiesUsedCount(int $hospitalId, array $activitiesIds): Collection
    {
        return self::on()
                   ->select('activity_id', DB::raw('SUM(quantity) as count'))
                   ->where('hospital_id', $hospitalId)
                   ->where('status', '>', 0)
                   ->whereIn('activity_id', $activitiesIds)
                   ->groupBy('activity_id')
                   ->get()
                   ->pluck('count', 'activity_id');
    }

    /**
     * 获取客户充值活动使用次数
     *
     * @param int   $memberId
     * @param array $activitiesIds
     *
     * @return Collection
     */
    public static function GetMemberRechargeActivitiesUsedCount(int $memberId, array $activitiesIds): Collection
    {
        return self::on()
                   ->select('activity_id', DB::raw('SUM(quantity) as count'))
                   ->where('member_id', $memberId)
                   ->where('status', '>', 0)
                   ->whereIn('activity_id', $activitiesIds)
                   ->groupBy('activity_id')
                   ->get()
                   ->pluck('count', 'activity_id');
    }
}
