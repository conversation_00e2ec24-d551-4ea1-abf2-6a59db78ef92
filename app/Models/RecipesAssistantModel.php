<?php

namespace App\Models;

/**
 * 处方跟诊助理
 */
class RecipesAssistantModel extends Model
{
    protected $table = 'his_recipes_assistant';

    /**
     * 获取处方跟诊助理
     *
     * @param array    $recipeIds
     * @param int|null $assistantStatus
     *
     * @return array
     */
    public static function getRecipeAssistant(array $recipeIds, ?int $assistantStatus = 1): array
    {
        if (empty($recipeIds))
        {
            return [];
        }

        return self::on()
                   ->select(['his_recipes_assistant.*', 'his_user.uid as user_uid', 'his_user.name as user_name'])
                   ->leftJoin(new UsersModel()->getTable() . ' as his_user',
                              'his_user.id',
                              '=',
                              'his_recipes_assistant.user_id')
                   ->when(is_int($assistantStatus), function ($query) use ($assistantStatus) {
                       $query->where(['his_recipes_assistant.assistant_status' => $assistantStatus]);
                   })
                   ->whereIn('his_recipes_assistant.recipe_id', $recipeIds)
                   ->get()
                   ->toArray();
    }
}
