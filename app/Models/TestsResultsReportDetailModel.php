<?php

namespace App\Models;

/**
 * 化验项报告单结果详情
 */
class TestsResultsReportDetailModel extends Model
{
    protected $table = 'his_tests_results_report_details';

    /**
     * 获取化验项报告单结果详情
     *
     * @param int $testId
     * @param int $templateId
     * @param int $reportResultId
     *
     * @return array
     */
    public static function getTestResultReportDetail(int $testId, int $templateId, int $reportResultId = 0): array
    {
        if (empty($testId) || empty($templateId))
        {
            return [];
        }

        // 获取模版所有指标项
        $getWhere = ['template_id' => $templateId];

        // 如果不存在结果，只获取有效的指标
        if (empty($reportResultId))
        {
            $getWhere['status'] = 1;
        }

        $getIndicatorsRes = TestsReportTemplatesIndicatorsModel::getData(where: $getWhere,
                                                                         keyBy: 'id');
        if (empty($getIndicatorsRes))
        {
            return [];
        }

        // 获取化验项报告单结果详情
        $getTestReportDetailRes = [];
        if (!empty($reportResultId))
        {
            $getTestReportDetailRes = self::on()
                                          ->where([
                                                      'test_id'          => $testId,
                                                      'template_id'      => $templateId,
                                                      'report_result_id' => $reportResultId,
                                                      'status'           => 1
                                                  ])
                                          ->get()
                                          ->keyBy('template_indicator_id')
                                          ->toArray();
        }


        $returnResult = [];
        foreach ($getIndicatorsRes as $indicator)
        {
            $curIndicatorResult = $getTestReportDetailRes[$indicator['id']] ?? [];

            $indicator['report_detail'] = $curIndicatorResult ?? [];
            $returnResult[]             = $indicator;
        }

        return $returnResult;
    }
}
