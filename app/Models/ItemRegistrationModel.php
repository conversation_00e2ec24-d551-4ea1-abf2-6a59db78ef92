<?php

namespace App\Models;

use Illuminate\Support\Collection;

/**
 * 商品:挂号商品
 */
class ItemRegistrationModel extends Model
{
    protected $table = 'item_registration';

    /**
     * 根据挂号类型获取挂号商品
     *
     * @param int         $hospitalOrgId
     * @param int         $registrationTypeId
     * @param int|null    $itemStatus
     * @param string|null $registrationTime
     *
     * @return Collection|array
     */
    public static function getRegistrationItemByType(int $hospitalOrgId, int $registrationTypeId, ?int $itemStatus = 1, ?string $registrationTime = ''): Collection|array
    {
        if (empty($hospitalOrgId) || empty($registrationTypeId))
        {
            return [];
        }

        return self::on()
                   ->where(['org_id' => $hospitalOrgId, 'registration_type' => $registrationTypeId])
                   ->when($itemStatus, function ($query, $itemStatus) {
                       $query->where('status', $itemStatus);
                   })
                   ->when($registrationTime, function ($query) use ($registrationTime) {
                       $query->where(function ($subQuery) use ($registrationTime) {
                           $subQuery->where(function ($q) use ($registrationTime) {
                               // 处理普通（非跨天）时间段：start_time < end_time
                               $q->whereRaw('start_time < end_time')
                                 ->where('start_time', '<=', $registrationTime)
                                 ->where('end_time', '>=', $registrationTime);
                           })
                                    ->orWhere(function ($q) use ($registrationTime) {
                                        // 处理跨天时间段：start_time > end_time
                                        $q->whereRaw('start_time > end_time')
                                          ->where(function ($inner) use ($registrationTime) {
                                              $inner->where('start_time', '<=', $registrationTime)
                                                    ->orWhere('end_time', '>=', $registrationTime);
                                          });
                                    });
                       });
                   })
                   ->get();

    }
}
