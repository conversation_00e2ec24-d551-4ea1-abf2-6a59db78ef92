<?php

namespace App\Models;

/**
 * 宠物体况表
 */
class PetVitalSignModel extends Model
{
    protected $table = 'pet_vital_sign';

    /**
     * 根据病历ID、挂号ID获取宠物体征信息
     *
     * @param int $caseid
     * @param int $registrationId
     * @param int $status
     *
     * @return array
     */
    public static function getVitalSignByCaseIdOrRegistrationId(int $caseId = 0, int $registrationId = 0, int $status = 1): array
    {
        if (empty($caseId) && empty($registrationId))
        {
            return [];
        }

        $where = ['status' => $status];
        if (!empty($caseId))
        {
            $where['case_id'] = $caseId;
        }
        if (!empty($registrationId))
        {
            $where['registration_id'] = $registrationId;
        }

        $getVitalSignRes = self::on()
                               ->where($where)
                               ->first();

        return $getVitalSignRes ? $getVitalSignRes->toArray() : [];
    }
}
