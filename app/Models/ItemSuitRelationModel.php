<?php

namespace App\Models;

use App\Enums\ItemTypeEnum;

/**
 * 商品组合：关联商品详情
 */
class ItemSuitRelationModel extends Model
{
    protected $table = 'item_suit_relation';

    public static function getItemSuitRelationItemDetailInfo(int $orgId, array $itemSuitIds = []): array
    {
        if (empty($orgId) || empty($itemSuitIds))
        {
            return [];
        }

        // 获取组合商品明细信息
        $getItemSuitRelationInfoRes = self::on()
                                          ->where(['org_id' => $orgId])
                                          ->whereIn('suit_id', $itemSuitIds)
                                          ->get()
                                          ->toArray();
        if (empty($getItemSuitRelationInfoRes))
        {
            return [];
        }

        // 是否存在多个item_type不同类型
        $itemTypes = array_unique(array_column($getItemSuitRelationInfoRes, 'item_type'));
        if (count($itemTypes) > 1 || !in_array($itemTypes[0], array_keys(ItemTypeEnum::SUIT_ITEM_TYPE_RELATION_MODEL)))
        {
            return [];
        }

        // 根据item_type获取商品明细信息
        $typeModel                            = ItemTypeEnum::SUIT_ITEM_TYPE_RELATION_MODEL[$itemTypes[0]];
        $itemIds                              = array_column($getItemSuitRelationInfoRes, 'item_id');
        $getItemSuitRelationItemDetailInfoRes = $typeModel::on()
                                                          ->whereIn('id', $itemIds)
                                                          ->get()
                                                          ->keyBy('id')
                                                          ->toArray();
        if (empty($getItemSuitRelationItemDetailInfoRes))
        {
            return [];
        }


        // 组合成目标格式
        $structuredResult = [];
        foreach ($getItemSuitRelationInfoRes as $relation)
        {
            $suitId = $relation['suit_id'];
            $itemId = $relation['item_id'];

            if (!isset($structuredResult[$suitId]))
            {
                $structuredResult[$suitId] = [];
            }

            $structuredResult[$suitId][$itemId] = array_merge($relation,
                                                              ['item_info' => $getItemSuitRelationItemDetailInfoRes[$itemId] ?? null]);
        }

        return $structuredResult;
    }
}
