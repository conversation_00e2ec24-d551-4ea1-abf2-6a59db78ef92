<?php

namespace App\Models;

/**
 * 门诊住院疾病分类
 */
class DiseaseCategoryModel extends Model
{
    protected $table = 'his_disease_category';

    /**
     * 获取疾病分类
     *
     * @param int $petCategoryId 宠物类别ID（1=狗，2=猫，其他=其他动物）
     *
     * @return array
     */
    public static function getDiseaseCategory(int $petCategoryId = 0): array
    {
        // 如果宠物类别，1-犬、2-猫、0-通用。否则获取，3-其它、0-通用
        if ($petCategoryId == 1 || $petCategoryId == 2)
        {
            $categoryIds = [0, $petCategoryId];
        }
        else
        {
            $categoryIds = [0, 3];
        }

        return self::on()
                   ->where(['status' => 1])
                   ->whereIn('pet_category_id', $categoryIds)
                   ->orderBy('orderby', 'desc')
                   ->get()
                   ->toArray();
    }
}
