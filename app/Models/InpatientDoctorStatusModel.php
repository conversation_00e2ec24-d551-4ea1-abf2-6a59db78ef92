<?php

namespace App\Models;

use Illuminate\Support\Collection;

/**
 * 医生操作住院状态
 */
class InpatientDoctorStatusModel extends Model
{
    protected $table = 'his_inpatient_doctor_status';

    // 允许批量赋值的字段
    protected $fillable = [
        'hospital_id',
        'inpatient_id',
        'doctor_id',
        'desc',
        'status',
    ];

    /**
     * 获取医生正在诊断的住院
     *
     * @param int   $hospitalId
     * @param int   $doctorId
     * @param array $inpatientIds
     *
     * @return Collection|array
     */
    public static function getDoctorInTreatmentInpatient(int $hospitalId, int $doctorId, array $inpatientIds = []): Collection|array
    {
        if (empty($hospitalId) || empty($doctorId))
        {
            return [];
        }

        return self::on()
                   ->where(['hospital_id' => $hospitalId, 'doctor_id' => $doctorId, 'status' => 1])
                   ->when(!empty($inpatientIds), function ($query) use ($inpatientIds) {
                       $query->whereIn('inpatient_id', $inpatientIds);
                   })
                   ->get()
                   ->keyBy('inpatient_id');
    }
}
