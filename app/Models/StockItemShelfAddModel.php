<?php

namespace App\Models;

use DB;

/**
 * 仓储-库存动态-加
 */
class StockItemShelfAddModel extends Model
{
    protected $table = 'stock_item_shelf_add';

    /**
     * 获取库存动态加记录数量
     *
     * @param int   $hospitalId
     * @param array $searchParams
     *
     * @return array
     */
    public static function getStockItemShelfAddQuantity(int $hospitalId, array $searchParams): array
    {
        if (empty($hospitalId))
        {
            return [];
        }

        $query = self::on()
                     ->where(['hospital_id' => $hospitalId]);
        if (isset($searchParams['type']) && is_numeric($searchParams['type']))
        {
            $query->where('type', $searchParams['type']);
        }
        if (isset($searchParams['subType']) && is_numeric($searchParams['subType']))
        {
            $query->where('sub_type', $searchParams['subType']);
        }
        if (!empty($searchParams['relationCode']))
        {
            $query->where('relation_code', $searchParams['relationCode']);
        }
        if (!empty($searchParams['relationId']))
        {
            $query->where('relation_id', $searchParams['relationId']);
        }
        if (!empty($searchParams['relationDetailId']))
        {
            $query->where('relation_detail_id', $searchParams['relationDetailId']);
        }
        if (!empty($searchParams['itemId']))
        {
            $query->where('item_id', $searchParams['itemId']);
        }
        if (!empty($searchParams['itemBarcode']))
        {
            $query->where('item_barcode', $searchParams['itemBarcode']);
        }
        if (!empty($searchParams['shelfCode']))
        {
            $query->where('shelf_code', $searchParams['shelfCode']);
        }

        $getQuantityRes = $query->select([DB::raw('sum(pack_quantity) as pack_quantity'), DB::raw('sum(bulk_quantity) as bulk_quantity')])
                                ->first();

        return ['packQuantity' => $getQuantityRes['pack_quantity'] ?? 0, 'bulkQuantity' => $getQuantityRes['bulk_quantity'] ?? 0];
    }
}
