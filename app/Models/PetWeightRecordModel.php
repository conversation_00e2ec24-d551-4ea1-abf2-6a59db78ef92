<?php

namespace App\Models;

/**
 * 宠物体重变动记录
 */
class PetWeightRecordModel extends Model
{
    protected $table = 'pet_weight_records';

    /**
     * 根据关联类型、关联ID获取宠物体重变动记录
     *
     * @param int $relationType
     * @param int $relationId
     *
     * @return array
     */
    public static function getPetWeightRecordByType(int $relationType, int $relationId): array
    {
        if (empty($relationType) || empty($relationId))
        {
            return [];
        }

        return self::on()
                   ->where(['relation_type' => $relationType, 'relation_id' => $relationId])
                   ->orderBy('id', 'desc')
                   ->get()
                   ->toArray();
    }
}
