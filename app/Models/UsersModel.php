<?php

namespace App\Models;

/**
 * 用户
 */
class UsersModel extends Model
{
    protected $table = 'his_users';

    /**
     * 通过手机号查找用户
     *
     * @param string $phone
     * @param bool   $onWritePdo
     *
     * @return object|null
     */
    public static function getOneByPhone(string $phone, bool $onWritePdo = false): object|null
    {
        if ($phone == '')
        {
            return null;
        }

        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->where(['phone' => $phone])
                   ->first();
    }

    /**
     * 获取用户信息
     *
     * @param array $ids
     * @param bool  $onWritePdo
     *
     * @return null | object
     */
    public static function getUserByIds(array $ids, bool $onWritePdo = false): object|null
    {
        if (empty($ids))
        {
            return null;
        }

        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->whereIn('id', $ids)
                   ->get()
                   ->keyBy('id');
    }

    /**
     * 获取用户名称
     *
     * @param int $id
     *
     * @return string
     */
    public static function getOneUserNameById(int $id): string
    {
        if ($id <= 0)
        {
            return '';
        }

        return self::on()
                   ->where(['id' => $id])
                   ->value('name');
    }
}
