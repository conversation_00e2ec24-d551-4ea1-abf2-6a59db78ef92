<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;

/**
 * 会员宠物关键字搜索
 *
 * Laravel的Eloquent模型中scope方式
 */
trait KeywordSearchMemberPet
{
    /**
     * 关键字搜索会员宠物
     *
     * 通用关键词搜索：支持手机号、会员名、宠物名、病历号。
     *
     * @param Builder     $query
     * @param string|null $keywords
     * @param string      $driverTable
     *
     * @return Builder
     */
    public function scopeSearchMemberPet(Builder $query, ?string $keywords, string $driverTable = ''): Builder
    {
        if (empty($keywords))
        {
            return $query;
        }

        $driverTable = empty($driverTable) ? self::table() : $driverTable;

        // 判断是否为手机号：11位数字
        $isPhone = preg_match('/^\d{11}$/', $keywords);

        return $query
            ->leftJoin('members as m', $driverTable . '.member_id', '=', 'm.id')
            ->leftJoin('member_pets as p', $driverTable . '.pet_id', '=', 'p.id')
            ->where(function ($q) use ($keywords, $isPhone) {
                if ($isPhone)
                {
                    $q->orWhere('m.phone', $keywords);
                }
                else
                {
                    $q->orWhere('m.name', 'like', "%{$keywords}%")
                      ->orWhere('p.name', 'like', "%{$keywords}%")
                      ->orWhere('p.record_number', $keywords);
                }
            });
    }
}
