<?php

namespace App\Models;

/**
 * 用户登录记录
 */
class UserLoginLogModel extends Model
{
    protected $table = 'his_user_login_log';

    /**
     * 更新一条登录记录：通过token
     *
     * @param string $token
     * @param array  $data
     *
     * @return int
     */
    public static function updateOneByToken(string $token, array $data): int
    {
        return self::on()
                   ->where(['token' => $token])
                   ->update($data);
    }
}
