<?php

namespace App\Models;

/**
 * 仓储-待出库单
 */
class StockPendingOrderModel extends Model
{
    protected $table = 'stock_pending_order';

    /**
     * 获取待出库单列表数据
     *
     * @param array $arrParams 查询参数
     * @param int   $page      页码
     * @param int   $pageSize  每页条数
     *
     * @return array
     */
    public static function getPendingOrderListData(array $arrParams, int $page = 1, int $pageSize = 15): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 基础查询条件
        $getWhere[] = ['stock_pending_order.hospital_id', '=', $arrParams['hospitalId']];

        // 出库单ID
        if (!empty($arrParams['pendingOrderId']))
        {
            $getWhere[] = ['stock_pending_order.id', '=', $arrParams['pendingOrderId']];
        }

        // 关键字查询（客户名称｜手机号｜宠物名称｜宠物病历号）
        $getOrWhere = [];
        if (!empty($arrParams['keywords']))
        {
            $keywords = $arrParams['keywords'];
            if (checkValidCellphone($keywords))
            {
                $getOrWhere[] = ['member.phone', '=', $keywords];
            }
            else
            {
                $getOrWhere = [
                    ['member.name', 'like', '%' . $keywords . '%'],
                    ['pet.name', 'like', '%' . $keywords . '%'],
                    ['pet.record_number', 'like', '%' . $keywords . '%'],
                ];
            }
        }

        // 时间查询（按创建时间）
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['stock_pending_order.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['stock_pending_order.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 业务单号查询
        if (!empty($arrParams['relationCode']))
        {
            $getWhere[] = ['stock_pending_order.relation_code', 'like', '%' . $arrParams['relationCode'] . '%'];
        }

        // 出库状态查询
        if (isset($arrParams['status']) && is_numeric($arrParams['status']))
        {
            $getWhere[] = ['stock_pending_order.status', '=', $arrParams['status']];
        }

        // 订单创建人（订单的单据创建人）
        if (!empty($arrParams['createdUserId']) || !empty($arrParams['createUserUid']))
        {
            if (!empty($arrParams['createUserId']))
            {
                $getWhere[] = ['stock_pending_order.created_by', '=', $arrParams['createUserId']];
            }
            else
            {
                $getWhere[] = ['user.uid', '=', $arrParams['createUserUid']];
            }
        }

        $builderQuery = self::on()
                            ->select([
                                         'stock_pending_order.*',
                                         'member.uid as member_uid',
                                         'member.name as member_name',
                                         'member.phone as member_phone',
                                         'pet.uid as pet_uid',
                                         'pet.name as pet_name',
                                         'user.uid as user_uid',
                                         'user.name as user_name',
                                     ])
                            ->leftJoin(new MemberModel()->getTable() . ' as member', 'stock_pending_order.member_id', '=', 'member.id')
                            ->leftJoin(new MemberPetsModel()->getTable() . ' as pet', 'stock_pending_order.pet_id', '=', 'pet.id')
                            ->leftJoin(new UsersModel()->getTable() . ' as user', 'stock_pending_order.created_by', '=', 'user.id')
                            ->where(function ($query) use ($getWhere, $getOrWhere) {
                                $query->where($getWhere);

                                if (!empty($getOrWhere))
                                {
                                    $query->where(function ($subQuery) use ($getOrWhere) {
                                        $subQuery->orWhere($getOrWhere);
                                    });
                                }
                            })
                            ->orderBy('stock_pending_order.created_at', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($page > 0 && $pageSize > 0)
        {
            $builderQuery->offset(($page - 1) * $pageSize)
                         ->limit($pageSize);
        }

        // 获取条目数据
        $pendingOrderList = $builderQuery->get()
                                         ->toArray();

        return ['total' => $totalCount, 'data' => $pendingOrderList];
    }
}
