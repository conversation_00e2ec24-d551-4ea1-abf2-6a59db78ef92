<?php

namespace App\Models;

/**
 * 影像检测员
 */
class ImagesTesterModel extends Model
{
    protected $table = 'his_images_tester';

    /**
     * 获取化验检测员
     *
     * @param array $testIds
     *
     * @return array
     */
    public static function getImageTester(array $imageIds, ?int $testerStatus = 1): array
    {
        if (empty($imageIds))
        {
            return [];
        }

        return self::on()
                   ->select(['his_images_tester.*', 'his_user.uid as user_uid', 'his_user.name as user_name'])
                   ->leftJoin(new UsersModel()->getTable() . ' as his_user',
                              'his_user.id',
                              '=',
                              'his_images_tester.user_id')
                   ->when(is_int($testerStatus), function ($query) use ($testerStatus) {
                       $query->where(['his_images_tester.tester_status' => $testerStatus]);
                   })
                   ->whereIn('his_images_tester.image_id', $imageIds)
                   ->get()
                   ->toArray();
    }
}
