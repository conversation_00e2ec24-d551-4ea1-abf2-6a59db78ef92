<?php

namespace App\Models;


/**
 * 会员备注表：会员在不同医院中的备注信息
 */
class MemberRemarkModel extends Model
{
    protected $table = 'member_remark';

    /**
     * 获取会员在某个医院下的备注
     *
     * @param int   $hospitalId
     * @param array $memberIds
     *
     * @return object|array
     */
    public static function getMemberRemark(int $hospitalId, array $memberIds): object|array
    {
        if (empty($hospitalId) || empty($memberIds))
        {
            return [];
        }

        return self::on()
                   ->where(['hospital_id' => $hospitalId, 'status' => 1])
                   ->whereIn('member_id', $memberIds)
                   ->get()
                   ->keyBy('member_id');
    }
}
