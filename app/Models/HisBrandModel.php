<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;

/**
 * 医院品牌表：组织下属品牌信息
 */
class HisBrandModel extends Model
{
    protected $table = 'his_brand';

    /**
     * 获取组织下的品牌
     *
     * @param int      $orgId
     * @param array    $excludeHospitalBrandId
     * @param int|null $isShare
     *
     * @return array|Collection
     */
    public static function getShareBrandByOrgId(int $orgId, array $excludeHospitalBrandId = [], ?int $isShare = null): array|Collection
    {
        if (empty($orgId))
        {
            return [];
        }

        if (is_numeric($isShare) && !in_array($isShare, [0, 1]))
        {
            return [];
        }

        return self::on()
                   ->where(['org_id' => $orgId])
                   ->when(!empty($excludeHospitalBrandId), function ($query) use ($excludeHospitalBrandId) {
                       $query->whereNotIn('id', $excludeHospitalBrandId);
                   })
                   ->when(is_numeric($isShare), function ($query) use ($isShare) {
                       $query->where(['is_share' => $isShare]);
                   })
                   ->get();
    }

    /**
     * 获取品牌是否互通
     *
     * @param int $brandId
     *
     * @return int
     */
    public static function getBrandIsShare(int $brandId): int
    {
        if (empty($brandId))
        {
            return 0;
        }

        return self::on()
                   ->where(['id' => $brandId])
                   ->value('is_share');
    }
}
