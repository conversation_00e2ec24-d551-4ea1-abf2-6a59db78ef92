<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

/**
 * 客户充值活动
 */
class BalanceRechargeActivitiesModel extends Model
{
    protected $table = 'member_balance_recharge_activities';

    /**
     * 获取医院可用的充值活动
     *
     * @param int $orgId
     * @param int $cityId
     * @param int $hospitalId
     *
     * @return Collection
     */
    public static function GetRechargeActivities(int $orgId, int $cityId, int $hospitalId): Collection
    {
        return DB::table(self::table() . ' as a')
                 ->where('a.org_id', $orgId)
                 ->where('a.status', 1)
                 ->where(function ($query) {
                     $query->whereNull('a.start_time')
                           ->orWhere('a.start_time', '<', now());
                 })
                 ->where(function ($query) {
                     $query->whereNull('a.end_time')
                           ->orWhere('a.end_time', '>', now());
                 })
                 ->where(function ($query) use ($cityId, $hospitalId) {
                     $query->where('a.limit_type', 1)
                           ->orWhere(function ($query) use ($cityId) {
                               $query->where('a.limit_type', 2)
                                     ->whereExists(function ($sub) use ($cityId) {
                                         $sub->select(DB::raw(1))
                                             ->from(BalanceRechargeActivitiesIncludeCityModel::table() . ' as c')
                                             ->whereRaw('c.activity_id = a.id')
                                             ->where('c.city_id', $cityId);
                                     });
                           })
                           ->orWhere(function ($query) use ($hospitalId) {
                               $query->where('a.limit_type', 3)
                                     ->whereExists(function ($sub) use ($hospitalId) {
                                         $sub->select(DB::raw(1))
                                             ->from(BalanceRechargeActivitiesIncludeHospitalModel::table() . ' as h')
                                             ->whereRaw('h.activity_id = a.id')
                                             ->where('h.hospital_id', $hospitalId);
                                     });
                           });
                 })
                 ->get();
    }
}
