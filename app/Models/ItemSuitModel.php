<?php

namespace App\Models;

/**
 * 商品组合：基础信息
 */
class ItemSuitModel extends Model
{
    protected $table = 'item_suit';

    /**
     * TODO 待完成
     * 获取组合商品明细
     *
     * @param int      $orderIg
     * @param array    $itemSuitId
     * @param array    $itemSuitUids
     * @param int|null $withStatus
     *
     * @return array|void
     */
    public static function getItemSuitDetailInfo(int $orderIg, array $itemSuitId = [], array $itemSuitUids = [], ?int $withStatus = null)
    {
        if (empty($orderIg))
        {
            return [];
        }

        if (empty($itemSuitId) && empty($itemSuitUids))
        {
            return [];
        }

        // 获取组合商品信息
        $getItemSuitInfoRes = self::on()
                                  ->where(['org_id' => $orderIg])
                                  ->when(!empty($itemSuitId), function ($query) use ($itemSuitId) {
                                      $query->whereIn('id', $itemSuitId);
                                  })
                                  ->when(!empty($itemSuitUids), function ($query) use ($itemSuitUids) {
                                      $query->whereIn('uid', $itemSuitUids);
                                  })
                                  ->when(is_int($withStatus), function ($query) use ($withStatus) {
                                      $query->where(['status' => $withStatus]);
                                  })
                                  ->get()
                                  ->toArray();
        if (empty($getItemSuitInfoRes))
        {
            return [];
        }

        // 获取组合关联商品及商品明细
        $itemSuitId = array_column($getItemSuitInfoRes, 'id');
        ItemSuitRelationModel::getItemSuitRelationItemDetailInfo($orderIg, $itemSuitId);
        exit;

        return $getItemSuitInfoRes;
    }

}
