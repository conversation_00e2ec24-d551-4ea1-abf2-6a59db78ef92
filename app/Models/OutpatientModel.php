<?php

namespace App\Models;

use DB;
use Illuminate\Support\Collection;
use App\Enums\OutpatientStatusEnum;

/**
 * 医院门诊表
 */
class OutpatientModel extends Model
{
    protected $table = 'his_outpatients';

    /**
     * 根据门诊编码获取门诊
     *
     * @param string $outpatientCode
     *
     * @return array
     */
    public static function getOutpatientByCode(string $outpatientCode): array
    {
        if (empty($outpatientCode))
        {
            return [];
        }

        $getOutpatientRes = self::on()
                                ->where(['outpatient_code' => $outpatientCode])
                                ->first();

        return $getOutpatientRes ? $getOutpatientRes->toArray() : [];
    }

    /**
     * 获取医生的门诊接诊统计信息
     *
     * @param int   $hospitalId
     * @param array $doctorIds
     *
     * @return Collection|array
     */
    public static function getDoctorOutpatientTotalInfo(int $hospitalId, array $doctorIds): Collection|array
    {
        if (empty($hospitalId) || empty($doctorIds))
        {
            return [];
        }

        $getOutpatientTotalInfoRes = self::on()
                                         ->select([
                                                      'doctor_id',
                                                      DB::raw(sprintf('SUM(CASE WHEN status = %d THEN 1 ELSE 0 END) as waiting_count',
                                                                      OutpatientStatusEnum::Waiting->value)),
                                                      DB::raw(sprintf('SUM(CASE WHEN status = %d THEN 1 ELSE 0 END) as in_treatment_count',
                                                                      OutpatientStatusEnum::InTreatment->value)),
                                                      DB::raw(sprintf('SUM(CASE WHEN status = %d THEN 1 ELSE 0 END) as stop_treatment_count',
                                                                      OutpatientStatusEnum::Stop->value))
                                                  ])
                                         ->where(['hospital_id' => $hospitalId, 'invalid' => 0])
                                         ->whereIn('doctor_id', $doctorIds)
                                         ->groupBy('doctor_id')
                                         ->get();
        if ($getOutpatientTotalInfoRes->isEmpty())
        {
            return [];
        }

        $returnResult = [];
        foreach ($getOutpatientTotalInfoRes as $outpatientInfo)
        {
            $returnResult[$outpatientInfo->doctor_id] = [
                'waiting_count'        => (int) $outpatientInfo->waiting_count,
                'in_treatment_count'   => (int) $outpatientInfo->in_treatment_count,
                'stop_treatment_count' => (int) $outpatientInfo->stop_treatment_count,
                'total_count'          => (int) $outpatientInfo->waiting_count + (int) $outpatientInfo->in_treatment_count + (int) $outpatientInfo->stop_treatment_count
            ];
        }

        // 确保所有请求的用户ID都有对应的统计数据，即使是0
        foreach ($doctorIds as $userId)
        {
            if (empty($returnResult[$userId]))
            {
                $returnResult[$userId] = [
                    'waiting_count'        => 0,
                    'in_treatment_count'   => 0,
                    'stop_treatment_count' => 0,
                    'total_count'          => 0
                ];
            }
        }

        return $returnResult;
    }

    /**
     * 获取用户的门诊记录
     *
     * @param int   $hospitalId
     * @param int   $memberId
     * @param array $petIds
     * @param array $status
     * @param bool  $onWritePdo
     *
     * @return Collection|array
     */
    public static function getOutpatientByPetId(int $hospitalId, int $memberId, array $petIds, array $status = [], bool $onWritePdo = false): Collection|array
    {
        if (empty($hospitalId) || empty($memberId) || empty($petIds))
        {
            return [];
        }

        return self::on()
                   ->select([
                                'his_outpatients.*',
                                'his_hospital_users.work_title as work_title',
                                'his_registrations.created_at as registration_time',
                                'his_users.name as doctor_name'
                            ])
                   ->leftJoin(new HospitalUserModel()->getTable() . ' as his_hospital_users', function ($query) {
                       $query->on('his_outpatients.hospital_id', '=', 'his_hospital_users.hospital_id')
                             ->on('his_outpatients.doctor_id', '=', 'his_hospital_users.user_id');
                   })
                   ->leftJoin(new UsersModel()->getTable() . ' as his_users', function ($query) {
                       $query->on('his_outpatients.doctor_id', '=', 'his_users.id');
                   })
                   ->leftJoin(new RegistrationsModel()->getTable() . ' as his_registrations', function ($query) {
                       $query->on('his_outpatients.registration_id', '=', 'his_registrations.id');
                   })
                   ->where([
                               'his_outpatients.hospital_id' => $hospitalId,
                               'his_outpatients.member_id'   => $memberId,
                               'his_outpatients.invalid'     => 0
                           ])
                   ->whereIn('his_outpatients.pet_id', $petIds)
                   ->when(!empty($status), function ($query) use ($status) {
                       $query->whereIn('his_outpatients.status', $status);
                   })
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->orderBy('his_outpatients.id', 'desc')
                   ->get();
    }

    /**
     * 获取医生的门诊列表
     *
     * @param int   $hospitalId
     * @param array $doctorIds
     * @param array $status
     * @param bool  $onWritePdo
     *
     * @return Collection|array
     */
    public static function getOutpatientByDoctorId(int $hospitalId, array $doctorIds = [], array $status = [], bool $onWritePdo = false): Collection|array
    {
        if (empty($hospitalId))
        {
            return [];
        }

        return self::on()
                   ->where(['hospital_id' => $hospitalId, 'invalid' => 0])
                   ->when(!empty($doctorIds), function ($query) use ($doctorIds) {
                       $query->whereIn('doctor_id', $doctorIds);
                   })
                   ->when(!empty($status), function ($query) use ($status) {
                       $query->whereIn('status', $status);
                   })
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->orderBy('created_at')
                   ->get();
    }

    /**
     * 获取医生有效的门诊
     *
     * @param int   $hospitalId
     * @param int   $doctorId
     * @param array $outpatientStatus
     *
     * @return Collection|array
     */
    public static function getValidOutpatientByDoctorIds(int $hospitalId, int $doctorId, array $outpatientStatus = []): Collection|array
    {
        if (empty($hospitalId) || empty($doctorId))
        {
            return [];
        }

        $where = ['hospital_id' => $hospitalId, 'doctor_id' => $doctorId, 'invalid' => 0];

        return self::on()
                   ->where($where)
                   ->when(!empty($outpatientStatus), function ($query) use ($outpatientStatus) {
                       $query->whereIn('status', $outpatientStatus);
                   })
                   ->get();
    }
}
