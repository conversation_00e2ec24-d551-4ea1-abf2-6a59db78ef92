<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;

/**
 * 商品中心:项目类型
 */
class ItemSaleTypeModel extends Model
{
    protected $table = 'item_sale_type';

    /**
     * 获取所有项目类型
     *
     * @param array $status
     * @param bool  $onWritePdo
     *
     * @return Collection
     */
    public static function getAllSaleType(array $status = [1], bool $onWritePdo = false): Collection
    {
        return self::on()
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->when(!empty($status), function ($query) use ($status) {
                       $query->whereIn('status', $status);
                   })
                   ->get()
                   ->keyBy('id');
    }
}
