<?php

namespace App\Models;

/**
 * 化验检测员
 */
class TestsTesterModel extends Model
{
    protected $table = 'his_tests_tester';

    /**
     * 获取化验检测员
     *
     * @param array $testIds
     *
     * @return array
     */
    public static function getTestTester(array $testIds, ?int $testerStatus = 1): array
    {
        if (empty($testIds))
        {
            return [];
        }

        return self::on()
                   ->select(['his_tests_tester.*', 'his_user.uid as user_uid', 'his_user.name as user_name'])
                   ->leftJoin(new UsersModel()->getTable() . ' as his_user',
                              'his_user.id',
                              '=',
                              'his_tests_tester.user_id')
                   ->when(is_int($testerStatus), function ($query) use ($testerStatus) {
                       $query->where(['his_tests_tester.tester_status' => $testerStatus]);
                   })
                   ->whereIn('his_tests_tester.test_id', $testIds)
                   ->get()
                   ->toArray();
    }
}
