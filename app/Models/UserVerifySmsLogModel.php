<?php

namespace App\Models;

/**
 * 用户短信验证码发送记录
 */
class UserVerifySmsLogModel extends Model
{
    protected $table = 'his_user_verify_sms_log';

    /**
     * 获取用户短信验证码发送数量
     *
     * @param string $phone
     * @param int    $type
     * @param int    $startTimestamp
     *
     * @return int
     */
    public static function getUserSmsSendCount(string $phone, int $type, int $startTimestamp): int
    {
        $startTime = date('Y-m-d H:i:s', $startTimestamp);

        return self::on()
                   ->where([
                               'phone'  => $phone,
                               'type'   => $type,
                               'status' => 1,
                           ])
                   ->where('created_at', '>=', $startTime)
                   ->count();
    }

    /**
     * 匹配出验证码发送的记录：用来对比验证码是否正确
     *
     * @param string $phone
     * @param string $code
     * @param int    $type
     * @param array  $extParams
     *
     * @return object|null
     */
    public static function getUserSmsSendLog(string $phone, string $code, int $type,
                                             array  $extParams = []): object|null
    {
        if ($phone == '' || $code == '' || $type <= 0)
        {
            return null;
        }

        return self::on()
                   ->where([
                               'phone'  => $phone,
                               'code'   => $code,
                               'type'   => $type,
                               'status' => 1,
                           ])
                   ->when(!empty($extParams), function ($query) use ($extParams) {
                       if (!empty($extParams['token']))
                       {
                           $query->where('token', '=', $extParams['token']);
                       }
                       if (!empty($extParams['uniqueId']))
                       {
                           $query->where('unique_id', '=', $extParams['uniqueId']);
                       }
                       if (!empty($extParams['extInfo']))
                       {
                           $query->where('ext_info', '=', $extParams['extInfo']);
                       }
                   })
                   ->orderBy('id', 'DESC')
                   ->first();
    }

    /**
     * 更新短信验证次数
     *
     * @param int   $id
     * @param array $data
     *
     * @return int
     */
    public static function incrementUserSmsLogVerifyTimes(int $id, array $data = []): int
    {
        if ($id <= 0)
        {
            return 0;
        }

        return self::on()
                   ->where(['id' => $id])
                   ->increment('verify_times', 1, $data);
    }
}
