<?php

namespace App\Models;

use DB;

/**
 * 仓储-库存库存表
 */
class StockItemShelfModel extends Model
{
    protected $table = 'stock_item_shelf';

    /**
     * 库存告警数量，低于该数量则告警
     */
    private const int WarningQuantity = 10;

    /**
     * 获取有效库存条件
     *
     * @param $query
     *
     * @return mixed
     */
    public static function getValidStockItemShelfCondition($query): mixed
    {
        $nowDate = getNowDateTime('Y-m-d');

        return $query->where(function ($q) use ($nowDate) {
            $q->whereNull('expired_date')
              ->orWhere('expired_date', '0000-00-00')
              ->orWhere('expired_date', '>', $nowDate);
        });
    }

    /**
     * 获取有库存的商品ID列表
     *
     * @param array $arrParams 搜索参数
     * @param int   $page      页码
     * @param int   $pageSize  每页条数
     *
     * @return array
     */
    public static function getEffectiveStockItemIdListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_item_shelf.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['stock_item_shelf.status', '=', 1];

        // 整装、散装需要有库存
        $getWhere[] = [
            function ($query) {
                return $query->where('stock_item_shelf.effective_pack_quantity', '>', 0)
                             ->orWhere('stock_item_shelf.effective_bulk_quantity', '>', 0);
            }
        ];

        // 多维度关键字查询
        $getOrWhere = [];
        if (!empty($arrParams['keywords']))
        {
            // 1.商品条码
            $getOrWhere[] = ['stock_item_shelf.item_barcode', 'like', '%' . $arrParams['keywords'] . '%'];

            // 2.商品名称
            $getOrWhere[] = ['item.name', 'like', '%' . $arrParams['keywords'] . '%'];

            // 3.商品别名
            $getOrWhere[] = ['item.alias_name', 'like', '%' . $arrParams['keywords'] . '%'];
        }

        $builderQuery = self::on()
                            ->select(['stock_item_shelf.item_id'])
                            ->leftJoin(new ItemModel()->getTable() . ' as item', 'stock_item_shelf.item_id', '=', 'item.id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->orderBy('stock_item_shelf.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count(DB::raw('distinct stock_item_shelf.item_id'));
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($page > 0 && $pageSize > 0)
        {
            $builderQuery->offset(($page - 1) * $pageSize)
                         ->limit($pageSize);
        }

        // 获取条目数据
        $stockItemList = $builderQuery->groupBy(['stock_item_shelf.item_id'])
                                      ->get()
                                      ->toArray();

        return ['total' => $totalCount, 'data' => $stockItemList];
    }

    public static function getEffectiveStockWarningItemListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_item_shelf.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['stock_item_shelf.status', '=', 1];

        // 多维度关键字查询
        $getOrWhere = [];
        if (!empty($arrParams['keywords']))
        {
            // 1.商品条码
            $getOrWhere[] = ['stock_item_shelf.item_barcode', 'like', '%' . $arrParams['keywords'] . '%'];

            // 2.商品名称
            $getOrWhere[] = ['item.name', 'like', '%' . $arrParams['keywords'] . '%'];

            // 3.商品别名
            $getOrWhere[] = ['item.alias_name', 'like', '%' . $arrParams['keywords'] . '%'];
        }

        $builderQuery = self::on()
                            ->select([
                                         'stock_item_shelf.item_id',
                                         DB::raw('SUM(stock_item_shelf.effective_pack_quantity + stock_item_shelf.effective_bulk_quantity) as total_quantity')
                                     ])
                            ->leftJoin(new ItemModel()->getTable() . ' as item', 'stock_item_shelf.item_id', '=', 'item.id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->groupBy(['stock_item_shelf.item_id'])
                            ->havingRaw('total_quantity < ?', [self::WarningQuantity])
                            ->orderBy('stock_item_shelf.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($page > 0 && $pageSize > 0)
        {
            $builderQuery->offset(($page - 1) * $pageSize)
                         ->limit($pageSize);
        }

        // 获取条目数据
        $stockItemList = $builderQuery->get()
                                      ->toArray();

        return ['total' => $totalCount, 'data' => $stockItemList];
    }

    /**
     * 获取临/过期预警商品列表数据
     *
     * @param array $arrParams 搜索参数
     * @param int   $page      页码
     * @param int   $pageSize  每页条数
     *
     * @return array
     */
    public static function getExpireWarningItemListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_item_shelf.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['stock_item_shelf.status', '=', 1];
        $getWhere[] = ['stock_item_shelf.expired_date', 'IS NOT', null];

        // 多维度关键字查询
        $getOrWhere = [];
        if (!empty($arrParams['keywords']))
        {
            // 1.商品条码
            $getOrWhere[] = ['stock_item_shelf.item_barcode', 'like', '%' . $arrParams['keywords'] . '%'];

            // 2.商品名称
            $getOrWhere[] = ['item.name', 'like', '%' . $arrParams['keywords'] . '%'];

            // 3.商品别名
            $getOrWhere[] = ['item.alias_name', 'like', '%' . $arrParams['keywords'] . '%'];
        }
        if (!empty($arrParams['expireStartDate']) && strtotime($arrParams['expireStartDate']) !== false)
        {
            $getWhere[] = ['stock_item_shelf.expired_date', '>=', $arrParams['expireStartDate']];
        }
        if (!empty($arrParams['expireEndDate']) && strtotime($arrParams['expireEndDate']) !== false)
        {
            $getWhere[] = ['stock_item_shelf.expired_date', '<=', $arrParams['expireEndDate']];
        }
        if (!empty($arrParams['expireType']))
        {
            // 临期
            if ($arrParams['expireType'] == 1)
            {
                $havingRaw = 'expire_days > 0 AND expire_days <= ifnull(item.over_life_day, 0)';
            }
            // 过期
            else
            {
                $havingRaw = 'expire_days <= 0';
            }
        }
        else
        {
            // 默认显示临期和过期的商品
            $havingRaw = 'expire_days <= IFNULL(item.over_life_day, 0)';
        }

        $builderQuery = self::on()
                            ->select([
                                         'stock_item_shelf.item_id',
                                         'stock_item_shelf.item_barcode',
                                         'stock_item_shelf.shelf_code',
                                         'stock_item_shelf.expired_date',
                                         'item.over_life_day',
                                         DB::raw('SUM(stock_item_shelf.effective_pack_quantity) as total_pack_quantity'),
                                         DB::raw('SUM(stock_item_shelf.effective_bulk_quantity) as total_bulk_quantity'),
                                         DB::raw('SUM(stock_item_shelf.effective_pack_quantity + stock_item_shelf.effective_bulk_quantity) as total_quantity'),
                                         DB::raw('DATEDIFF(stock_item_shelf.expired_date, CURDATE()) as expire_days')
                                     ])
                            ->leftJoin(new ItemModel()->getTable() . ' as item', 'stock_item_shelf.item_id', '=', 'item.id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->groupBy(['stock_item_shelf.item_id', 'stock_item_shelf.shelf_code', 'stock_item_shelf.expired_date'])
                            ->havingRaw($havingRaw)
                            ->orderBy('stock_item_shelf.expired_date');

        // 获取条目总数
        $totalCount = $builderQuery->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($page > 0 && $pageSize > 0)
        {
            $builderQuery->offset(($page - 1) * $pageSize)
                         ->limit($pageSize);
        }

        // 获取条目数据
        $stockItemList = $builderQuery->get()
                                      ->toArray();

        return ['total' => $totalCount, 'data' => $stockItemList];
    }
}
