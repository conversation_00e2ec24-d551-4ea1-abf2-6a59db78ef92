<?php

namespace App\Models;

/**
 * 商品:基础信息
 */
class ItemModel extends Model
{
    protected $table = 'item';

    /**
     * 获取商品需要展示的字段名
     *
     * @param string $tableAlias 表别名，默认 item
     * @param string $asAlias    字段别名，默认 item_display_name
     * @param array  $fields
     *
     * 最终Sql：COALESCE(NULLIF(item.alias_name, ''), NULLIF(item.basis_name, ''), NULLIF(item.name, '')) AS item_display_name
     *
     * @return string
     */
    public static function getItemDisplayNameField(string $tableAlias = 'item', string $asAlias = 'item_display_name', array $fields = [
        'alias_name',
        'basis_name',
        'name',
    ]): string
    {
        // 生成形如 NULLIF(item.alias_name, '') 的字段数组
        $nullIfFields = array_map(fn($field) => "NULLIF($tableAlias.$field, '')", $fields);

        // 用逗号拼接成 COALESCE 的参数部分
        $coalesceExpr = implode(', ', $nullIfFields);

        // 拼成完整表达式，加上 AS 别名
        return "COALESCE($coalesceExpr) AS $asAlias";
    }
}
