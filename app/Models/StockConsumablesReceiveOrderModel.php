<?php

namespace App\Models;

use DB;

/**
 * 耗材领用单
 */
class StockConsumablesReceiveOrderModel extends Model
{
    protected $table = 'stock_consumables_receive_order';

    public static function getStockConsumablesReceiveOrderListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 基础查询条件
        $getWhere[] = ['stock_consumables_receive_order.hospital_id', '=', $arrParams['hospitalId']];

        // 领用单ID
        if (!empty($arrParams['receiveOrderId']))
        {
            $getWhere[] = ['stock_consumables_receive_order.id', '=', $arrParams['receiveOrderId']];
        }

        // 关键字查询
        $getOrWhere = [];
        if (!empty($arrParams['keywords']))
        {
            $getOrWhere[] = ['stock_consumables_receive_order.receive_code', 'like', '%' . $arrParams['keywords'] . '%'];
            $getOrWhere[] = ['stock_consumables_receive_order_item.item_barcode', 'like', '%' . $arrParams['keywords'] . '%'];
        }

        // 领用单创建时间
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['stock_consumables_receive_order.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }

        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['stock_consumables_receive_order.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 领用单状态
        if (isset($arrParams['status']) && is_numeric($arrParams['status']))
        {
            $getWhere[] = ['stock_consumables_receive_order.status', '=', $arrParams['status']];
        }

        // 出库状态
        if (isset($arrParams['outboundStatus']) && is_numeric($arrParams['outboundStatus']))
        {
            $getWhere[] = ['stock_consumables_receive_order.outbound_status', '=', $arrParams['outboundStatus']];
        }

        // 订单创建人（订单的单据创建人）
        if (!empty($arrParams['createUserUid']) || !empty($arrParams['createUserId']))
        {
            if (!empty($arrParams['createUserId']))
            {
                $getWhere[] = ['stock_consumables_receive_order.created_by', '=', $arrParams['createUserId']];
            }
            else
            {
                $getWhere[] = ['user.uid', '=', $arrParams['createUserUid']];
            }
        }

        $builderQuery = self::on()
                            ->select([
                                         'stock_consumables_receive_order.*',
                                         'user.uid as user_uid',
                                         'user.name as user_name',
                                     ])
                            ->leftJoin(new UsersModel()->getTable() . ' as user', 'stock_consumables_receive_order.created_by', '=', 'user.id')
                            ->where($getWhere)
                            ->when(!empty($getOrWhere), function ($query) use ($getOrWhere) {
                                $query->leftJoin((new StockConsumablesReceiveOrderItemModel()->getTable()) . ' as stock_consumables_receive_order_item',
                                                 'stock_consumables_receive_order.id',
                                                 '=',
                                                 'stock_consumables_receive_order_item.receive_order_id');

                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->orderBy('stock_consumables_receive_order.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count(DB::raw('distinct stock_consumables_receive_order.id'));
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($page > 0 && $pageSize > 0)
        {
            $builderQuery->offset(($page - 1) * $pageSize)
                         ->limit($pageSize);
        }

        // 获取条目数据
        $receiveOrderList = $builderQuery->groupBy('stock_consumables_receive_order.id')
                                         ->get()
                                         ->toArray();

        return ['total' => $totalCount, 'data' => $receiveOrderList];
    }
}
