<?php

namespace App\Models;

use App\Enums\ItemTypeEnum;

/**
 * 商品:商品支持的宠物类别白名单
 * 不设置则为全部支持，设置则使用白名单
 */
class ItemSupportPetCategoryModel extends Model
{
    protected $table = 'item_support_pet_category';

    public static function getItemSupportPetCategory(int $itemType, array $itemIds)
    {
        if (empty($itemType) || !in_array($itemType, ItemTypeEnum::values()))
        {
            return false;
        }

        if (empty($itemIds))
        {
            return false;
        }

        $getItemSupportPetCategoryRes = self::on()
                                            ->where(['item_type' => $itemType, 'status' => 1])
                                            ->whereIn('item_id', $itemIds)
                                            ->get()
                                            ->toArray();
        if (empty($getItemSupportPetCategoryRes))
        {
            return [];
        }

        $returnItemSupportPetCategory = [];
        foreach ($getItemSupportPetCategoryRes as $itemSupportPetCategory)
        {
            $returnItemSupportPetCategory[$itemSupportPetCategory['item_id']][] = $itemSupportPetCategory['category_id'];
        }

        return $returnItemSupportPetCategory;
    }
}
