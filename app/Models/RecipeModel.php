<?php

namespace App\Models;

use DB;
use Log;
use Throwable;

/**
 * 处方表
 */
class RecipeModel extends Model
{
    protected $table = 'his_recipes';

    /**
     * 获取病历下指定支付状态处方
     *
     * @param int        $caseId
     * @param int        $sourceType
     * @param int        $sourceRelationId
     * @param array|null $paidStatus
     *
     * @return array
     */
    public static function getWithPaidStatusRecipeByCaseId(int $caseId, int $sourceType, int $sourceRelationId, ?array $paidStatus = []): array
    {
        if (empty($caseId) || empty($sourceType) || empty($sourceRelationId))
        {
            return [];
        }

        return self::on()
                   ->where([
                               'case_id'            => $caseId,
                               'source_type'        => $sourceType,
                               'source_relation_id' => $sourceRelationId,
                               'status'             => 1,
                           ])
                   ->when(is_array($paidStatus), function ($query) use ($paidStatus) {
                       $query->whereIn('is_paid', $paidStatus);
                   })
                   ->get()
                   ->toArray();
    }

    /**
     * 删除处方
     *
     * @param int $recipeId
     * @param int $deletedBy
     *
     * @return bool
     * @throws Throwable
     */
    public static function deleteRecipe(int $recipeId, int $deletedBy = 0): bool
    {
        if (empty($recipeId) || empty($deletedBy))
        {
            return false;
        }

        try
        {
            DB::beginTransaction();

            // 删除处方主表
            self::on()
                ->where(['id' => $recipeId])
                ->update(['status' => 0]);

            // 删除处方明细
            $recipeWhere = ['recipe_id' => $recipeId];
            RecipeItemModel::on()
                           ->where($recipeWhere)
                           ->update(['status' => 0]);

            // 删除处方化验
            TestModel::on()
                     ->where($recipeWhere)
                     ->update(['status' => 0, 'deleted_by' => $deletedBy]);

            // 删除处方影像
            ImagesModel::on()
                       ->where($recipeWhere)
                       ->update(['status' => 0, 'deleted_by' => $deletedBy]);

            // 删除处方处置
            NurseModel::on()
                      ->where($recipeWhere)
                      ->update(['status' => 0, 'deleted_by' => $deletedBy]);

            DB::commit();

            return true;
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 删除处方异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * 获取病历下处方汇总
     *
     * @param int $caseId
     *
     * @return array
     */
    public static function getRecipeSummaryByCaseId(int $caseId): array
    {
        if (empty($caseId))
        {
            return [];
        }

        $getRecipeSummaryRes = self::on()
                                   ->select([
                                                DB::raw('COUNT(*) as recipe_count'),
                                                DB::raw('SUM(price) as total_price'),
                                                DB::raw('SUM(CASE WHEN is_paid = 1 THEN price ELSE 0 END) as paid_price'),
                                                DB::raw('SUM(CASE WHEN (is_paid = 0 OR is_paid = 2) THEN price ELSE 0 END) as unpaid_price'),
                                            ])
                                   ->where(['case_id' => $caseId, 'status' => 1])
                                   ->first();

        return $getRecipeSummaryRes ? $getRecipeSummaryRes->toArray() : [];
    }
}
