<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * class SnowflakeFacade
 *
 * @method static array getConfiguration()
 * @method static string generateId()
 * @method static array|bool parseId(string $snowflakeId)
 *
 * @see \App\Services\Snowflake\Snowflake
 */
class SnowflakeFacade extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return \App\Services\Snowflake\Snowflake::class;
    }
}
