<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * Class TokenFacade
 *
 * @method static string|bool start(string|int $uid, string $token = '', array $data = [], int|null $lifetime = null)
 * @method static bool destroy(string $token)
 * @method static string|int|bool exists($token)
 * @method static bool refresh($token, int|null $lifetime = null)
 * @method static bool flush()
 *
 * @see \App\Services\Session\SessionManagerInterface
 */
class TokenFacade extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'App\Services\Session\SessionManagerInterface';
    }
}
