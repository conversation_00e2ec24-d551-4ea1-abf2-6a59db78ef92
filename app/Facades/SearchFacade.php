<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * class SearchFacade
 *
 * @method static \App\Services\Search\Contracts\SearchServiceInterface setIndexName(string $indexName)
 * @method static string getIndexName()
 * @method static array searchRecipeItems(string $keywords, int $purchaseHospitalId = 0, array $supportPetCategoryIds = [], array $firstSaleTypeId = [], int $limit = 200, int|null $orgId = null)
 * @method static array searchBeautyItems(string $keywords, array $supportPetCategoryIds = [], array $firstSaleTypeId = [], int $limit = 200, int|null $orgId = null)
 * @method static array searchRetailItems(string $keywords, array $firstSaleTypeId = [], int $limit = 200, int|null $orgId = null)
 * @method static array searchPurchaseItems(string $keywords, int $purchaseHospitalId = 0, array $itemIds = [], int $brandId = 0, int $limit = 200, int|null $orgId = null)
 *
 * @see \App\Services\Search\Contracts\SearchServiceInterface
 */
class SearchFacade extends Facade
{
    /**
     * 获取组件的注册名称
     */
    protected static function getFacadeAccessor(): string
    {
        return \App\Services\Search\Contracts\SearchServiceInterface::class;
    }
}
