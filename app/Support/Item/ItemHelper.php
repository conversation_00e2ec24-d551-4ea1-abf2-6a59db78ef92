<?php

namespace App\Support\Item;

use App\Enums\ItemSaleTyeEnum;
use App\Logics\Logic;

/**
 * 商品助手
 * Class ItemHelper
 * @package App\Support\Item
 */
class ItemHelper extends Logic
{
    /**
     * 格式化商品sku信息
     *
     * @param array $itemInfo
     *
     * @return array
     */
    public static function FormatItemInfoStructure(array $itemInfo): array
    {
        if (empty($itemInfo))
        {
            return [];
        }

        // 商品一级项目类型
        $curItemTypeInfo = [
            'id'   => $itemInfo['first_sale_type_info']['id'] ?? 0,
            'name' => $itemInfo['first_sale_type_info']['alias_name'] ?? '',
        ];

        // 商品出库单位规格
        $curItemUseUnit   = $itemInfo['use_unit'] ?? '';
        $curItemUseRatio  = $itemInfo['use_ratio'] ?? 0;
        $curItemBulkUnit  = $itemInfo['bulk_unit'] ?? '';
        $curItemBulkRatio = $itemInfo['bulk_ratio'] ?? 0;
        $curItemPackUnit  = $itemInfo['pack_unit'] ?? '';

        // 如果没有整装单位、散装单位使，用计量单位
        if (empty($curItemBulkUnit))
        {
            $curItemBulkUnit = $curItemUseUnit;
        }
        if (empty($curItemPackUnit))
        {
            $curItemPackUnit = $curItemUseUnit;
        }

        // 商品出库单位，1ml/ml、1次、1瓶。（计量比 + 计量单位 + 散装单位 ）
        if ($curItemTypeInfo['id'] == ItemSaleTyeEnum::FirstDrug->value)
        {
            $curItemSpec = formatDisplayNumber($curItemUseRatio) . $curItemUseUnit . '/' . $curItemBulkUnit;
        }
        else
        {
            $curItemSpec = 1 . $curItemUseUnit;
        }

        // 商品库存
        $curItemStock = [
            'packStock'  => $itemInfo['stock_info']['packQuantity'] ?? 0,
            'bulkStock'  => $itemInfo['stock_info']['bulkQuantity'] ?? 0,
            'totalStock' => $itemInfo['stock_info']['totalQuantityDesc'] ?? ''
        ];

        return [
            'uid'               => $itemInfo['uid'] ?? '',
            'type'              => $curItemTypeInfo,
            'name'              => !empty($itemInfo['item_display_name']) ? $itemInfo['item_display_name'] : self::ItemDisplayName($itemInfo),
            'itemName'          => $itemInfo['name'],
            'itemBarcode'       => $itemInfo['item_barcode_info']['item_barcode'] ?? '',
            'useUnit'           => $curItemUseUnit,
            'useRatio'          => $curItemUseRatio,
            'spec'              => $curItemSpec,
            'packUnit'          => $curItemPackUnit,
            'packPrice'         => formatDisplayNumber($itemInfo['sale_price_info']['pack_sale_price'] ?? 0),
            'bulkUnit'          => $curItemBulkUnit,
            'bulkPrice'         => formatDisplayNumber($itemInfo['sale_price_info']['bulk_sale_price'] ?? 0),
            'bulkRatio'         => $curItemBulkRatio,
            'stock'             => $curItemStock,
            'isShelfLife'       => $itemInfo['is_shelf_life'],
            'shelfLife'         => $itemInfo['shelf_life'],
            'shelfLifeDay'      => $itemInfo['shelf_life_day'],
            'isPreciseMetering' => $itemInfo['is_precise_metering'] ?? false,
            'isPackSaleAllow'   => $itemInfo['is_pack_sale_allow'] ?? false,
        ];
    }

    /**
     * 获取商品展示名称
     *
     * @param array $item
     *
     * @return string
     */
    public static function ItemDisplayName(array $item): string
    {
        // item_display_name是itemModel中getItemDisplayNameField()获取的需要展示的名称
        if (!empty($item['item_display_name']))
        {
            return $item['item_display_name'];
        }

        $keys = [
            'alias_name',
            'basis_name',
            'name'
        ];

        foreach ($keys as $key)
        {
            if (!empty($item[$key]))
            {
                return $item[$key];
            }
        }

        return '';
    }
}
