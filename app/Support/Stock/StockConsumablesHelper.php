<?php

namespace App\Support\Stock;

use Arr;
use App\Enums\ItemStatusEnum;
use App\Enums\ItemSaleTyeEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\ItemLogic;
use App\Logics\V1\StockItemDailyPriceLogic;

/**
 * 仓储-耗材出库辅助类
 * Class StockConsumablesHelper
 * @package App\Support\Stock
 */
class StockConsumablesHelper extends Logic
{
    /**
     * 验证耗材领用单提交是否正确，并返回完整信息
     *
     * @param array $addConsumablesParams
     * @param array $publicParams
     * @param array $oldConsumablesItemInfo
     *
     * @return LogicResult
     */
    public static function GetValidConsumablesParams(array $addConsumablesParams, array $publicParams, array $oldConsumablesItemInfo = []): LogicResult
    {
        if (empty($addConsumablesParams))
        {
            return self::Fail('验证耗材领用单，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证耗材领用单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证耗材领用单，缺少医院相关必选参数', 400);
        }

        // 业务参数
        $remark           = trimWhitespace(Arr::get($addConsumablesParams, 'remark', ''));
        $consumablesItems = Arr::get($addConsumablesParams, 'items', []);
        if (empty($remark))
        {
            return self::Fail('领用说明不可为空', 42003);
        }
        if (empty($consumablesItems))
        {
            return self::Fail('耗材商品不可为空', 42005);
        }

        // 旧的耗材领用单上存在的商品
        if (!empty($oldConsumablesItemInfo))
        {
            $oldConsumablesItemInfo = array_column($oldConsumablesItemInfo, null, 'uid');
        }

        // 验证领用商品
        $arrUniqueItemUids = [];
        foreach ($consumablesItems as $curConsumablesItem)
        {
            $curUid      = $curConsumablesItem['uid'] ?? '';
            $curItemName = $curConsumablesItem['itemName'] ?? '';
            $curItemUid  = $curConsumablesItem['itemUid'] ?? '';
            if (empty($curItemUid))
            {
                return self::Fail('验证耗材商品，缺少商品UID', 42007);
            }
            if (empty($curConsumablesItem['itemBarcode']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少商品条码';
                continue;
            }

            // 编辑提交时，是否商品一致
            $curOldConsumablesItem = $oldConsumablesItemInfo[$curUid] ?? [];
            if (!empty($curUid) && empty($curOldConsumablesItem))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，原耗材商品已不存在或已失效，请刷新重试';
                continue;
            }
            if (!empty($curOldConsumablesItem) && $curConsumablesItem['itemBarcode'] != $curOldConsumablesItem['item_barcode'])
            {
                $errorMsg[] = '名称【' . $curItemName . '】，提交耗材商品与原商品条码不一致';
                continue;
            }

            // 验证领用数量
            $curPackQuantity = $curConsumablesItem['packQuantity'] ?: 0;
            $curBulkQuantity = $curConsumablesItem['bulkQuantity'] ?: 0;
            if ($curPackQuantity <= 0 && $curBulkQuantity <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，散装数量、整装数量不可同时为0';
                continue;
            }

            if (in_array($curConsumablesItem['itemUid'], $arrUniqueItemUids))
            {
                $errorMsg[]          = '名称【' . $curItemName . '】，不可重复添加商品';
                $arrUniqueItemUids[] = $curConsumablesItem['itemUid'];
            }

            $arrUniqueItemUids[] = $curConsumablesItem['itemUid'];
        }

        // 采购商品基本校验
        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42007);
        }

        // 获取耗材商品信息
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemUids: $arrUniqueItemUids, publicParams: $publicParams, withItemStock: true);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData();
        $getItemInfoRes = array_column($getItemInfoRes, null, 'uid');
        if (empty($getItemInfoRes))
        {
            return self::Fail('耗材商品基本信息不存在', 42007);
        }

        // 获取耗材商品加权价格
        $arrItemIds           = array_column($getItemInfoRes, 'id');
        $getItemDailyPriceRes = StockItemDailyPriceLogic::GetItemNowDailyPrice($arrItemIds, $publicParams);
        if ($getItemDailyPriceRes->isFail())
        {
            return $getItemDailyPriceRes;
        }

        // 计算耗材单领用总金额（申请发起时的库存加权成本价之和）
        $consumablesReceiveTotalPrice = 0;
        foreach ($consumablesItems as &$newConsumablesItem)
        {
            $curItemUid  = $newConsumablesItem['itemUid'] ?? '';
            $curItemName = $newConsumablesItem['itemName'] ?? '';

            // 商品、商品条码信息
            $curItemBaseInfo    = $getItemInfoRes[$curItemUid] ?? [];
            $curItemBarcodeInfo = $curItemBaseInfo['item_barcode_info'] ?? '';
            $curItemStockInfo   = $curItemBaseInfo['stock_info'] ?? [];
            if (empty($curItemBaseInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品基本信息不存在';
                continue;
            }
            if (empty($curItemBarcodeInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品条码信息不存在';
                continue;
            }
            if ($curItemBarcodeInfo['item_barcode'] != $newConsumablesItem['itemBarcode'])
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品条码不一致';
                continue;
            }
            if ($curItemBaseInfo['status'] != ItemStatusEnum::Online->value)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非上线状态';
                continue;
            }
            if ($curItemBaseInfo['first_sale_type_id'] != ItemSaleTyeEnum::FirstDrug->value)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非普通商品、药品';
                continue;
            }
            if ($curItemBaseInfo['is_receive_allow'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非耗材领用可出库';
                continue;
            }

            // 验证商品库存是否满足
            $curPackQuantity       = $newConsumablesItem['packQuantity'] ?: 0;
            $curBulkQuantity       = $newConsumablesItem['bulkQuantity'] ?: 0;
            $getCheckSufficientRes = StockQuantityConversionHelper::checkQuantityIsSufficient($curPackQuantity,
                                                                                              $curBulkQuantity,
                                                                                              $curItemStockInfo['packQuantity'],
                                                                                              $curItemStockInfo['bulkQuantity'],
                                                                                              $curItemStockInfo['bulkRatio']);
            if (empty($getCheckSufficientRes))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品库存不足';
                continue;
            }

            // 商品加权价
            $curItemDailyPrice = $getItemDailyPriceRes->getData($curItemBaseInfo['id'], []);
            if (empty($curItemDailyPrice))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品加权价不存在';
                continue;
            }

            // 整、散加权价
            $curPackDailyPrice = $curItemDailyPrice['packPrice'] ?? 0;
            $curBulkDailyPrice = $curItemDailyPrice['bulkPrice'] ?? 0;
            if ($curPackDailyPrice <= 0 || $curBulkDailyPrice <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品加权价无效';
                continue;
            }

            // 计算申请耗材商品整、散总价
            $curTotalBulkPrice = 0;
            $curTotalPackPrice = 0;
            if ($curPackQuantity > 0)
            {
                $curTotalPackPrice = numberMul([$curPackQuantity, $curPackDailyPrice], 4);
            }
            if ($curBulkQuantity > 0)
            {
                $curTotalBulkPrice = numberMul([$curBulkQuantity, $curBulkDailyPrice], 4);
            }
            if ($curTotalPackPrice <= 0 && $curTotalBulkPrice <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，耗材总领用总金额错误';
                continue;
            }

            $newConsumablesItem['packPrice'] = $curPackQuantity > 0 ? $curPackDailyPrice : 0;
            $newConsumablesItem['bulkPrice'] = $curBulkQuantity > 0 ? $curBulkDailyPrice : 0;
            $newConsumablesItem['itemId']    = $curItemBaseInfo['id'];
            $newConsumablesItem['itemInfo']  = $curItemBaseInfo;

            $consumablesReceiveTotalPrice = numberAdd([$consumablesReceiveTotalPrice, $curTotalPackPrice, $curTotalBulkPrice]);
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42007);
        }

        // 验证后的耗材领用商品
        $addConsumablesParams['remark']            = $remark;
        $addConsumablesParams['items']             = $consumablesItems;
        $addConsumablesParams['receiveTotalPrice'] = $consumablesReceiveTotalPrice;

        return self::Success($addConsumablesParams);
    }

    /**
     * 对比耗材领用商品明细差异，构建更新数据和日志
     *
     * @param array $oldConsumablesItemInfo
     * @param array $newConsumablesItemInfo
     *
     * @return array [$updateData, $logContent]
     */
    public static function CompareConsumablesItemChange(array $oldConsumablesItemInfo, array $newConsumablesItemInfo): array
    {
        $fieldMap = [
            'packQuantity' => ['label' => '整装领用数量', 'unit' => '个', 'db_field' => 'pack_quantity', 'type' => 'int'],
            'bulkQuantity' => ['label' => '散装领用数量', 'unit' => '个', 'db_field' => 'bulk_quantity', 'type' => 'int'],
        ];

        $updateData = [];
        $logLines   = [];
        foreach ($fieldMap as $camelField => $meta)
        {
            $oldVal = $oldConsumablesItemInfo[$meta['db_field']] ?? null;
            $newVal = $newConsumablesItemInfo[$camelField] ?? null;

            $isDifferent = match ($meta['type'])
            {
                'decimal' => bccomp($oldVal, $newVal, 4) !== 0,
                'int' => (int) $oldVal !== (int) $newVal,
                default => (string) $oldVal !== (string) $newVal,
            };

            if ($isDifferent)
            {
                $updateData[$meta['db_field']] = $newVal;
                $logLines[]                    = "  {$meta['label']}由$oldVal{$meta['unit']}变为$newVal{$meta['unit']}";
            }
        }

        if (!empty($updateData))
        {
            $logContent = "修改商品:【{$newConsumablesItemInfo['itemInfo']['id']}】{$newConsumablesItemInfo['itemName']}" . implode(";", $logLines);

            return [$updateData, $logContent];
        }

        return [];
    }
}
