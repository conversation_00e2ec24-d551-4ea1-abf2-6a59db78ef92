<?php

namespace App\Support\Stock;

/**
 * 库存数量转化计算辅助类
 *
 * 主要用于处理整装和散装之间的换算验证
 * 通过将所有数量统一转换为散装单位进行计算，简化整散比验证逻辑
 */
class StockQuantityConversionHelper
{
    /**
     * 将整装+散装转换为总散装数量
     *
     * @param int $packQuantity 整装数量
     * @param int $bulkQuantity 散装数量
     * @param int $bulkRatio    整散比（1个整装=多少个散装）
     *
     * @return int 总散装数量
     */
    public static function convertToTotalBulkQuantity(int $packQuantity, int $bulkQuantity, int $bulkRatio): int
    {
        if ($bulkRatio <= 0)
        {
            return $bulkQuantity;
        }

        return numberAdd([numberMul([$packQuantity, $bulkRatio]), $bulkQuantity]);
    }

    /**
     * 验证操作的整散数量是满足
     *
     * @param int $actualPackQuantity  实际操作整装数量
     * @param int $actualBulkQuantity  实际操作散装数量
     * @param int $allowedPackQuantity 库存整装数量
     * @param int $allowedBulkQuantity 库存散装数量
     * @param int $bulkRatio           整散比
     *
     * @return bool 是否验证通过（true: 满足，库存足够，false: 不满足，超限）
     */
    public static function checkQuantityIsSufficient(int $actualPackQuantity, int $actualBulkQuantity, int $allowedPackQuantity, int $allowedBulkQuantity, int $bulkRatio = 1): bool
    {
        // 参数验证
        if ($actualPackQuantity < 0 || $actualBulkQuantity < 0 || $allowedPackQuantity < 0 || $allowedBulkQuantity < 0 || $bulkRatio < 1)
        {
            return false;
        }

        $actualTotal  = self::convertToTotalBulkQuantity($actualPackQuantity, $actualBulkQuantity, $bulkRatio);
        $allowedTotal = self::convertToTotalBulkQuantity($allowedPackQuantity, $allowedBulkQuantity, $bulkRatio);

        return $actualTotal <= $allowedTotal;
    }

    /**
     * 获取剩余可操作库存，按整装 + 散装方式返回（优先整装）
     *
     * @param int $allowedPackQuantity 允许的整装数量
     * @param int $allowedBulkQuantity 允许的散装数量
     * @param int $usedPackQuantity    已使用整装数量
     * @param int $usedBulkQuantity    已使用散装数量
     * @param int $bulkRatio           整散比
     *
     * @return array ['remainPackQuantity' => int, 'remainBulkQuantity' => int, 'totalRemainBulk' => int]
     */
    public static function getRemainPackAndBulkQuantity(int $allowedPackQuantity, int $allowedBulkQuantity, int $usedPackQuantity, int $usedBulkQuantity, int $bulkRatio): array
    {
        // 参数验证
        if ($allowedPackQuantity < 0 || $allowedBulkQuantity < 0 || $usedPackQuantity < 0 || $usedBulkQuantity < 0 || $bulkRatio < 0)
        {
            return [
                'remainPackQuantity' => 0,
                'remainBulkQuantity' => 0,
                'totalRemainBulk'    => 0
            ];
        }

        $allowedTotal = self::convertToTotalBulkQuantity($allowedPackQuantity, $allowedBulkQuantity, $bulkRatio);
        $usedTotal    = self::convertToTotalBulkQuantity($usedPackQuantity, $usedBulkQuantity, $bulkRatio);

        $remainTotal = max(0, $allowedTotal - $usedTotal);

        // 剩余数量按整装优先分配
        if ($bulkRatio > 0)
        {
            $remainPackQuantity = intval($remainTotal / $bulkRatio);
            $remainBulkQuantity = $remainTotal % $bulkRatio;
        }
        else
        {
            $remainPackQuantity = 0;
            $remainBulkQuantity = $remainTotal;
        }

        return [
            'remainPackQuantity' => $remainPackQuantity,
            'remainBulkQuantity' => $remainBulkQuantity,
            'totalRemainBulk'    => $remainTotal
        ];
    }

    /**
     * [新增]根据总散装数，获取整装和散装的数量
     *
     * @param int $totalBulkQuantity 总散装数
     * @param int $bulkRatio         整散比
     *
     * @return array ['packQuantity' => 0, 'bulkQuantity' => 0]
     */
    public static function getPackAndBulkQuantity(int $totalBulkQuantity, int $bulkRatio): array
    {
        if ($totalBulkQuantity <= 0)
        {
            return ['packQuantity' => 0, 'bulkQuantity' => 0];
        }

        if ($bulkRatio <= 0)
        {
            return ['packQuantity' => 0, 'bulkQuantity' => $totalBulkQuantity];
        }

        $packQuantity = intdiv($totalBulkQuantity, $bulkRatio);
        $bulkQuantity = $totalBulkQuantity % $bulkRatio;

        return ['packQuantity' => $packQuantity, 'bulkQuantity' => $bulkQuantity];
    }
}
