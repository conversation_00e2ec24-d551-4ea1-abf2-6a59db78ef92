<?php

namespace App\Support\Member;

use App\Models\MemberModel;

class MemberHelper
{
    /**
     * 根据会员ID列表获取会员选项
     *
     * @param array $memberIds
     *
     * @return array
     */
    public static function GetMemberOptionsByMemberIds(array $memberIds): array
    {
        $members = MemberModel::getManyByIds($memberIds)
                              ->keyBy('id');

        $result = [];
        foreach ($memberIds as $memberId)
        {
            $member = $members[$memberId] ?? null;
            if (!$member)
            {
                continue;
            }

            $result[] = [
                'uid'   => $member['uid'],
                'name'  => $member['name'],
                'phone' => secretCellphone($member['phone'])
            ];
        }

        return $result;
    }
}
