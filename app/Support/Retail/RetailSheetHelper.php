<?php

namespace App\Support\Retail;

use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\ItemTypeEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Models\ItemModel;
use App\Models\ItemSalePriceModel;
use App\Logics\V1\HospitalLogic;

/**
 * 零售购买单单助手
 * Class RetailSheetHelper
 * @package App\Support\Retail
 */
class RetailSheetHelper extends Logic
{
    /**
     * 验证购买单内商品基本信息（商品状态、整装是否可售卖、商品价格、开具数量）
     *
     * @param array $items
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidSheetItems(array $items, array $publicParams): LogicResult
    {
        if (empty($items))
        {
            return self::Fail('缺少商品明细', 400);
        }

        //公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('公共参数缺少医院信息', 400);
        }

        // 商品明细基础验证
        $errorMsg   = [];
        $itemInfos  = [];
        $itemUids   = [];
        $totalPrice = 0;
        foreach ($items as $value)
        {
            $curItemName = $value['name'] ?? '';
            if (empty($value['itemUid']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少商品UID';
                continue;
            }
            if (in_array($value['itemUid'], $itemUids))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，重复';
                continue;
            }
            if (!empty($value['isSuit']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】为组合商品，零售业务暂不支持';
                continue;
            }
            if (!empty($value['type']) && !empty($value['type']['id']) && !in_array($value['type']['id'],
                                                                                    ItemSaleTyeEnum::RETAIL_SALE_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】非商品、药品的项目，零售业务暂不支持';
                continue;
            }
            if (!is_numeric($value['quantity']) || $value['quantity'] <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，数量参数错误';
                continue;
            }
            // 开具单位，1:散；2:整。
            $curUnitType = $value['unitType'];
            if (!in_array($curUnitType, ItemUnitTypeEnum::RETAIL_UNIT_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，单位类型参数错误';
                continue;
            }
            if (bccomp($value['price'], '0', 2) != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，单价参数错误';
                continue;
            }
            if (bccomp($value['totalPrice'], '0', 2) != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，金额参数错误';
                continue;
            }

            $itemInfos[] = $value;
            $itemUids[]  = $value['itemUid'];
        }

        // 商品基本校验
        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500600);
        }

        // 无有效商品
        if (empty($itemInfos) || empty($itemUids))
        {
            return self::Fail('零售购买单中无有效的商品', 500601);
        }

        // 购买单中单品
        $itemsBaseInfo = ItemModel::getData(where: ['org_id' => $hospitalOrgId],
            whereIn:                               ['uid' => $itemUids],
            keyBy:                                 'uid');

        if (empty($itemsBaseInfo))
        {
            return self::Fail('洗美购买单中单品商品不存在', 500202);
        }

        // 购买单中单品、组合商品ID
        $itemIds = array_column($itemsBaseInfo, 'id');

        // 获取医院信息，为了获取商品价格使用。
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        // 医院所属城市
        $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
        $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

        // 获取商品价格
        $getItemsPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Sku->value,
                                                                 $itemIds,
                                                                 $hospitalOrgId,
                                                                 $hospitalBrandId,
                                                                 $hospitalProvinceId,
                                                                 $hospitalCityId,
                                                                 $hospitalId);
        if (empty($getItemsPriceRes))
        {
            return self::Fail('零售商品价格获取失败', 500610);
        }

        $returnResult = [
            'item'       => [],
            'totalPrice' => 0,
            'itemsInfo'  => [],
        ];
        foreach ($items as $value)
        {
            $curItemUid  = $value['itemUid'] ?? '';
            $curItemName = $value['name'] ?? '';

            $curItemBaseInfo = $itemsBaseInfo[$curItemUid] ?? [];

            // 商品不存在
            $curItemId = $curItemBaseInfo['id'] ?? 0;
            if (empty($curItemBaseInfo) || empty($curItemId))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品不存在';
                continue;
            }

            // 商品非上线状态
            if ($curItemBaseInfo['status'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非上线状态，不能保存购买单';
                continue;
            }

            // 商品一级类型是否可开具
            if (!in_array($curItemBaseInfo['first_sale_type_id'], ItemSaleTyeEnum::RETAIL_SALE_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品一级类型非零售可开具，不能保存购买单';
                continue;
            }

            // 商品是否零售可开具
            if (isset($curItemBaseInfo['is_retail_allow']) && $curItemBaseInfo['is_retail_allow'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非零售可开具，不能保存购买单';
                continue;
            }

            // 商品整装是否可售卖
            $curUnitType = $value['unitType'] ?? ItemUnitTypeEnum::getRetailDefaultUnit();
            if ($curUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value && $curItemBaseInfo['is_pack_sale_allow'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品整装不可售卖';
                continue;
            }

            // 获取商品价格
            $curItemPriceInfo = $getItemsPriceRes[$curItemId]['sale_price'] ?? [];

            // 价格可以为0，但是不可以不存在价格记录
            if (empty($curItemPriceInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格不存在';
                continue;
            }

            // 商品价格
            $curItemPrice = $curItemPriceInfo[ItemUnitTypeEnum::UNIT_TYPE_PRICE_FIELD[$curUnitType]] ?? 0;
            if (bccomp($curItemPrice, '0', 2) == - 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格不能小于0';
                continue;
            }

            if (bccomp($value['price'], $curItemPrice, 2) != 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';
            }

            if (bccomp($curItemPrice * $value['quantity'], $value['totalPrice'], 2) != 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';
            }

            // 增加商品ID、价格
            $value['itemId']    = $curItemId;
            $value['itemPrice'] = $curItemPrice;

            $totalPrice = numberAdd([$totalPrice, $curItemPrice * $value['quantity']], 2);

            $returnResult['item'][] = $value;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500690);
        }

        $returnResult['itemsInfo']  = array_column($itemsBaseInfo, null, 'id');
        $returnResult['totalPrice'] = $totalPrice;

        return self::Success($returnResult);
    }

    /**
     * 获取添加购买单内单品
     *
     * @param array $addItems
     * @param array $itemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddItems(array $addItems, array $itemsInfo, array $publicParams): LogicResult
    {
        if (empty($addItems) || empty($itemsInfo) || empty($publicParams))
        {
            return self::Fail('保存购买单，商品参数错误', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId', 0));

        $statusErrorMsg = [];
        $addTotalPrice  = 0;
        $returnAddItems = [];
        foreach ($addItems as $value)
        {
            $itemId      = $value['itemId'] ?? '';
            $curItemName = $value['name'] ?? '';
            $curItemInfo = $itemsInfo[$itemId] ?? [];
            if (empty($curItemInfo))
            {
                $statusErrorMsg[] = '商品【' . $curItemName . '】，信息不存在';
                continue;
            }

            // 当前商品库存管理非精确计量，采用进一法
            $curQuantity = bcmul($value['quantity'], 1, 2);
            if (empty($curItemInfo['is_precise_metering']))
            {
                $curQuantity = ceil($curQuantity);
            }

            // 商品整装是否可售卖
            $curUnitType = $value['unitType'] ?? ItemUnitTypeEnum::getRetailDefaultUnit();
            if ($curUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value && $curItemInfo['is_pack_sale_allow'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品整装不可售卖';
                continue;
            }

            // 商品是否零售可开具
            if (isset($curItemInfo['is_retail_allow']) && $curItemInfo['is_retail_allow'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非零售可开具，不能保存购买单';
                continue;
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($value['itemPrice']))
            {
                $statusErrorMsg[] = '商品【' . $curItemName . '】，价格不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 500690);
            }

            if (bccomp($value['price'], $value['itemPrice'], 2) != 0)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';

                return self::Fail(implode("；\n", $statusErrorMsg), 500690);
            }

            if (bccomp($value['itemPrice'] * $curQuantity, $value['totalPrice'], 2) != 0)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';

                return self::Fail(implode("；\n", $statusErrorMsg), 500690);
            }

            // 如果是编辑会存在uid，新增则为null
            if (!empty($value['uid']))
            {
                $curUid = $value['uid'];
            }
            else
            {
                $curUid = generateUUID();
            }

            $tmpData = [
                'item_uid'   => $value['itemUid'],
                'uid'        => $curUid,
                'sheet_id'   => 0,
                'item_id'    => $itemId,
                'unit_type'  => $curUnitType,
                'price'      => $value['itemPrice'],
                'quantity'   => $curQuantity,
                'status'     => 1,
                'created_by' => $userId,
            ];

            $addTotalPrice    = numberAdd([
                                              $addTotalPrice,
                                              numberMul([$curQuantity, $value['itemPrice']])
                                          ]);
            $returnAddItems[] = $tmpData;
        }

        if (!empty($statusErrorMsg))
        {
            return self::Fail(implode("；\n", $statusErrorMsg), 500690);
        }

        return self::Success(['totalPrice' => $addTotalPrice, 'items' => $returnAddItems]);
    }
}
