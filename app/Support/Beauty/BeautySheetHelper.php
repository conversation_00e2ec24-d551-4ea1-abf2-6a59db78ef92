<?php

namespace App\Support\Beauty;

use ArrayAccess;
use Illuminate\Support\Arr;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\ItemTypeEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Enums\SheetStatusEnum;
use App\Enums\BeautyExecuteStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\HospitalLogic;
use App\Logics\V1\PetLogic;
use App\Models\ItemSaleTypeModel;
use App\Models\ItemBeautyModel;
use App\Models\ItemSalePriceModel;
use App\Models\ItemSuitModel;
use App\Models\ItemSuitRelationModel;
use App\Models\ItemSupportPetCategoryModel;
use App\Models\MemberPetsModel;
use App\Models\BeautyServiceExecutorModel;
use App\Models\BeautyServiceSheetModel;
use App\Support\Item\ItemHelper;
use App\Support\User\HospitalUserHelper;


/**
 * 洗美服务单助手
 * Class BeautySheetHelper
 * @package App\Support\Beauty
 */
class BeautySheetHelper extends Logic
{
    /**
     * 获取有效的洗美服务单
     *
     * @param string $sheetCode
     * @param int    $sheetId
     * @param int    $hospitalId
     *
     * @return LogicResult
     */
    public static function GetValidSheet(string $sheetCode = '', int $sheetId = 0, int $hospitalId = 0): LogicResult
    {
        $sheet = BeautyServiceSheetModel::GetSheetByCodeOrId($sheetCode, $sheetId, $hospitalId);
        if (empty($sheet))
        {
            return self::Fail('洗美服务单不存在', 500310);
        }

        if ($sheet->status < SheetStatusEnum::Unpaid->value)
        {
            return self::Fail('洗美服务单已删除', 500312);
        }

        return self::Success($sheet->toArray());
    }

    /**
     * 验证服务单内商品基本信息（商品状态、商品价格、开具数量、是否匹配当前宠物）
     *
     * @param int   $petId
     * @param array $items
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidSheetItems(int $petId, array $items, array $publicParams): LogicResult
    {
        if (!$petId || empty($items))
        {
            return self::Fail('缺少宠物ID或服务项目', 400);
        }

        //公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('公共参数缺少医院信息', 400);
        }

        $pet = MemberPetsModel::getOneByIdOrUid(id: $petId, orgId: $hospitalOrgId);
        if (empty($pet))
        {
            return self::Fail('宠物不存在', 32000);
        }

        // 宠物类别
        $petCategoryId = $pet->category_id;

        // 服务项目基础验证
        $errorMsg      = [];
        $itemInfos     = [];
        $suitItemInfos = [];
        $totalPrice    = 0;
        foreach ($items as $value)
        {
            $curItemName   = $value['name'] ?? '';
            $curItemIsSuit = $value['isSuit'] ?? false;
            if (empty($value['itemUid']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少商品UID';
                continue;
            }
            if (!empty($value['isSuit']) && empty($value['suitItems']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少组合商品明细';
                continue;
            }
            if (!is_numeric($value['quantity']) || $value['quantity'] <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，数量参数错误';
                continue;
            }
            if ($curItemIsSuit && $value['quantity'] > 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，的组合商品数量不可大于1';
                continue;
            }
            if (bccomp($value['price'], '0', 2) != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，单价参数错误';
                continue;
            }
            if (bccomp($value['totalPrice'], '0', 2) != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，金额参数错误';
                continue;
            }

            // 区分单品、组合商品
            if ($curItemIsSuit)
            {
                $suitItemInfos[] = $value;
            }
            else
            {
                $itemInfos[] = $value;
            }
        }

        // 商品基本校验
        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500200);
        }

        // 无有效商品
        if (empty($itemInfos) && empty($suitItemInfos))
        {
            return self::Fail('洗美服务单中无有效的洗美商品', 500201);
        }

        // 服务单中单品
        $itemUids      = array_column($itemInfos, 'itemUid');
        $itemsBaseInfo = [];
        if (!empty($itemUids))
        {
            $itemsBaseInfo = ItemBeautyModel::getData(where: ['org_id' => $hospitalOrgId],
                whereIn:                                     ['uid' => $itemUids],
                keyBy:                                       'uid');

            if (empty($itemsBaseInfo))
            {
                return self::Fail('洗美服务单中单品商品不存在', 500202);
            }
        }

        // 服务单中组合商品
        $suitItemUids      = array_column($suitItemInfos, 'itemUid');
        $suitItemsBaseInfo = [];
        if (!empty($suitItemUids))
        {
            $suitItemsBaseInfo = ItemSuitModel::getData(where: ['org_id' => $hospitalOrgId],
                whereIn:                                       ['uid' => $suitItemUids],
                keyBy:                                         'uid');

            if (empty($suitItemsBaseInfo))
            {
                return self::Fail('洗美服务单中组合商品不存在', 500203);
            }
        }

        // 服务单中商品、组合都不存在
        if (empty($itemsBaseInfo) && empty($suitItemsBaseInfo))
        {
            return self::Fail('洗美服务单中无有效的洗美商品', 500201);
        }

        // 服务单中单品、组合商品ID
        $itemIds     = array_column($itemsBaseInfo, 'id');
        $suitItemIds = array_column($suitItemsBaseInfo, 'id');

        // 如果宠物类别存在，则获取服务单中的商品支持的宠物类别白名单。验证是否适用此宠物
        $getItemSupportPetCategoryRes     = [];
        $getSuitItemSupportPetCategoryRes = [];
        if ($petCategoryId > 0)
        {
            // 获取处方的商品支持的宠物类别白名单
            $getItemSupportPetCategoryRes = ItemSupportPetCategoryModel::getItemSupportPetCategory(ItemTypeEnum::Sku->value,
                                                                                                   $itemIds);

            // 获取处方的组合商品支持的宠物类别白名单
            $getSuitItemSupportPetCategoryRes = ItemSupportPetCategoryModel::getItemSupportPetCategory(ItemTypeEnum::Spu->value,
                                                                                                       $suitItemIds);
        }

        // 获取医院信息，为了获取商品价格使用。
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        // 医院所属城市
        $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
        $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

        // 获取商品、组合价格
        $getItemsPriceRes     = [];
        $getSuitItemsPriceRes = [];
        if (!empty($itemIds))
        {
            $getItemsPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Beauty->value,
                                                                     $itemIds,
                                                                     $hospitalOrgId,
                                                                     $hospitalBrandId,
                                                                     $hospitalProvinceId,
                                                                     $hospitalCityId,
                                                                     $hospitalId);
            if (empty($getItemsPriceRes))
            {
                return self::Fail('洗美商品价格获取失败', 500210);
            }
        }
        if (!empty($suitItemIds))
        {
            $getSuitItemsPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Spu->value,
                                                                         $suitItemIds,
                                                                         $hospitalOrgId,
                                                                         $hospitalBrandId,
                                                                         $hospitalProvinceId,
                                                                         $hospitalCityId,
                                                                         $hospitalId);
            if (empty($getSuitItemsPriceRes))
            {
                return self::Fail('洗美组合商品价格获取失败', 500211);
            }
        }
        if (empty($getItemsPriceRes) && empty($getSuitItemsPriceRes))
        {
            return self::Fail('商品价格获取失败', 500212);
        }

        $returnResult = [
            'item'          => [],
            'suit'          => [],
            'totalPrice'    => 0,
            'itemsInfo'     => [],
            'suitItemsInfo' => [],
        ];
        foreach ($items as $value)
        {
            $curItemUid    = $value['itemUid'] ?? '';
            $curItemName   = $value['name'] ?? '';
            $curItemIsSuit = $value['isSuit'] ?? false;

            if ($curItemIsSuit)
            {
                $curItemBaseInfo = $suitItemsBaseInfo[$curItemUid] ?? [];

                // 增加与单品级售卖类型一样字段，保持与item结构一致。
                $curItemBaseInfo['first_sale_type_id'] = $curItemBaseInfo['sale_type_id'];

                // 增加组合详情
                $curItemBaseInfo['suitItems'] = $suitRelationDetail[$curItemBaseInfo['id']] ?? [];
            }
            else
            {
                $curItemBaseInfo = $itemsBaseInfo[$curItemUid] ?? [];
            }

            // 商品不存在
            $curItemId = $curItemBaseInfo['id'] ?? 0;
            if (empty($curItemBaseInfo) || empty($curItemId))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品不存在';
                continue;
            }

            // 商品非上线状态
            if ($curItemBaseInfo['status'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非上线状态，不能保存服务单';
                continue;
            }

            // 商品一级类型是否可开具
            if (!in_array($curItemBaseInfo['first_sale_type_id'], ItemSaleTyeEnum::BEAUTY_SALE_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品一级类型非洗美可开具，不能保存服务单';
                continue;
            }

            // 商品是否适用当前宠物
            if ($petCategoryId > 0)
            {
                if ($curItemIsSuit)
                {
                    $curItemSupportPetCategoryIds = $getSuitItemSupportPetCategoryRes[$curItemId] ?? [];
                }
                else
                {
                    $curItemSupportPetCategoryIds = $getItemSupportPetCategoryRes[$curItemId] ?? [];
                }

                if (!empty($curItemSupportPetCategoryIds) && !in_array($petCategoryId,
                                                                       $curItemSupportPetCategoryIds))
                {
                    $errorMsg[] = '名称【' . $curItemName . '】，商品不适用于当前宠物，不能保存服务单';
                    continue;
                }
            }

            // 获取商品价格
            if ($curItemIsSuit)
            {
                $curItemPriceInfo = $getSuitItemsPriceRes[$curItemId]['sale_price'] ?? [];
            }
            else
            {
                $curItemPriceInfo = $getItemsPriceRes[$curItemId]['sale_price'] ?? [];
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (empty($curItemPriceInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格不存在';
                continue;
            }

            // 商品价格
            $curUnitType  = ItemUnitTypeEnum::getBeautyDefaultUnit();//洗美使用固定散装单位
            $curItemPrice = $curItemPriceInfo[ItemUnitTypeEnum::UNIT_TYPE_PRICE_FIELD[$curUnitType]] ?? 0;
            if (bccomp($curItemPrice, '0', 2) == - 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格不能小于0';
                continue;
            }

            if (bccomp($value['price'], $curItemPrice, 2) != 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';
            }

            if (bccomp($curItemPrice * $value['quantity'], $value['totalPrice'], 2) != 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';
            }

            // 增加商品ID、价格
            $value['itemId']    = $curItemId;
            $value['itemPrice'] = $curItemPrice;

            $totalPrice = numberAdd([$totalPrice, $curItemPrice * $value['quantity']], 2);

            // 处方内的商品分组，组合单独为一组。剩下的根据一级项目类型分组
            if ($curItemIsSuit)
            {
                $returnResult['suit'][] = $value;
            }
            else
            {
                $returnResult['item'][] = $value;
            }
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500291);
        }

        $returnResult['itemsInfo']     = array_column($itemsBaseInfo, null, 'id');
        $returnResult['suitItemsInfo'] = array_column($suitItemsBaseInfo, null, 'id');
        $returnResult['totalPrice']    = $totalPrice;

        return self::Success($returnResult);
    }

    /**
     * 获取添加服务单内单品
     *
     * @param int   $memberId
     * @param int   $petId
     * @param array $addItems
     * @param array $itemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddItems(int $memberId, int $petId, array $addItems, array $itemsInfo, array $publicParams
    ): LogicResult
    {
        if (empty($memberId) || empty($petId))
        {
            return self::Fail('保存服务单，会员ID、宠物ID参数错误', 400);
        }

        if (empty($addItems) || empty($itemsInfo) || empty($publicParams))
        {
            return self::Fail('保存服务单，商品参数错误', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $userId     = intval(Arr::get($publicParams, '_userId', 0));

        $statusErrorMsg = [];
        $addTotalPrice  = 0;
        $returnAddItems = [];
        foreach ($addItems as $value)
        {
            $itemId      = $value['itemId'] ?? '';
            $curItemName = $value['name'] ?? '';
            $curItemInfo = $itemsInfo[$itemId] ?? [];
            if (empty($curItemInfo))
            {
                $statusErrorMsg[] = '商品【' . $curItemName . '】，信息不存在';
                continue;
            }

            // 当前商品数量使用int类型，不支持小数
            $curQuantity = intval($value['quantity']);

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($value['itemPrice']))
            {
                $statusErrorMsg[] = '商品【' . $curItemName . '】，价格不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 500290);
            }

            if (bccomp($value['price'], $value['itemPrice'], 2) != 0)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';

                return self::Fail(implode("；\n", $statusErrorMsg), 500290);
            }

            if (bccomp($value['itemPrice'] * $curQuantity, $value['totalPrice'], 2) != 0)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';

                return self::Fail(implode("；\n", $statusErrorMsg), 500290);
            }

            // 如果是编辑会存在uid，新增则为null
            if (!empty($value['uid']))
            {
                $curUid = $value['uid'];
            }
            else
            {
                $curUid = generateUUID();
            }

            $tmpData = [
                'item_uid'        => $value['itemUid'],
                'uid'             => $curUid,
                'sheet_id'        => 0,
                'hospital_id'     => $hospitalId,
                'member_id'       => $memberId,
                'pet_id'          => $petId,
                'item_suit_id'    => 0,
                'item_id'         => $itemId,
                'price'           => $value['itemPrice'],
                'quantity'        => $curQuantity,
                'remark'          => $value['remark'] ?? '',
                'suit_group'      => 0,
                'is_suit'         => 0,
                'suit_unique_uid' => '',
                'status'          => 1,
                'created_by'      => $userId,
            ];

            $addTotalPrice    = numberAdd([
                                              $addTotalPrice,
                                              numberMul([$curQuantity, $value['itemPrice']])
                                          ]);
            $returnAddItems[] = $tmpData;
        }

        if (!empty($statusErrorMsg))
        {
            return self::Fail(implode("；\n", $statusErrorMsg), 500290);
        }

        return self::Success(['totalPrice' => $addTotalPrice, 'items' => $returnAddItems]);
    }

    /**
     * 获取添加服务单内组合
     *
     * @param int   $memberId
     * @param int   $petId
     * @param array $addSuitItems
     * @param array $suitsItemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddSuits(
        int $memberId, int $petId, array $addSuitItems, array $suitsItemsInfo, array $publicParams
    ): LogicResult
    {
        if (empty($memberId) || empty($petId))
        {
            return self::Fail('保存服务单，会员ID、宠物ID参数错误', 400);
        }

        if (empty($addSuitItems) || empty($suitsItemsInfo) || empty($publicParams))
        {
            return self::Fail('保存服务单，商品参数错误', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $userId     = intval(Arr::get($publicParams, '_userId', 0));

        // 获取组合明细
        $suitItemIds              = array_column($addSuitItems, 'itemId');
        $getSuitRelationDetailRes = ItemSuitRelationModel::getItemSuitRelationItemDetailInfo($orgId, $suitItemIds);
        if (empty($getSuitRelationDetailRes))
        {
            return self::Fail('组合，明细信息获取失败', 400);
        }

        $statusErrorMsg     = [];
        $addSuitTotalPrice  = 0;
        $returnAddSuitItems = [];
        foreach ($addSuitItems as $suitItem)
        {
            $curSuitItemId   = $suitItem['itemId'] ?? '';
            $curItemName     = $suitItem['name'] ?? '';
            $curSuitSubItems = $suitItem['suitItems'] ?? [];
            $curSuitItemInfo = $suitsItemsInfo[$curSuitItemId] ?? [];
            if (empty($curSuitItemInfo))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，信息不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            // 开具数量
            $curSuitQuantity = intval($suitItem['quantity']);
            if ($curSuitQuantity > 1)
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，请分开开具';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            // 是否标记组合
            $curIsSuit = boolval($suitItem['isSuit']) ?? false;
            if (empty($curIsSuit))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，组合标记错误';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($suitItem['itemPrice']))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，价格不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            if (bccomp($suitItem['price'], $suitItem['itemPrice'], 2) != 0)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            if (bccomp($suitItem['itemPrice'] * $curSuitQuantity, $suitItem['totalPrice'], 2) != 0)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            // 组合明细
            $curSubRelationItems = $getSuitRelationDetailRes[$curSuitItemId] ?? [];
            if (empty($curSubRelationItems))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，关联明细不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            // 组合子项是否限
            if (count($curSuitSubItems) > count($curSubRelationItems))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，开具子项与关联子项不一致';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            // 组合项验证
            $subStatusError      = [];
            $curSuitSubItemInfos = [];
            $diffCurSuitSubItems = $curSuitSubItems;
            foreach ($curSuitSubItems as $subItemKey => &$curSubItem)
            {
                $curSuitItemName = $curSubItem['name'] ?? '';
                foreach ($curSubRelationItems as $relationItemKey => $relationItemInfo)
                {
                    $itemInfo = $relationItemInfo['item_info'] ?? [];
                    $itemId   = $itemInfo['id'] ?? '';
                    $itemUid  = $itemInfo['uid'] ?? '';
                    if ($curSubItem['itemUid'] != $itemUid)
                    {
                        continue;
                    }

                    // 子项开具数量
                    $curSuitItemQuantity = intval($curSubItem['quantity']);
                    if ($curSuitItemQuantity <= 0 || $curSuitItemQuantity > 1)
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，数量参数错误';
                    }

                    // 子项目标记是否组合子项
                    $curSubIsSuitItem = boolval($curSubItem['isSuitItem']) ?? false;
                    if (empty($curSubIsSuitItem))
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，组合子项标记错误';
                        break;
                    }

                    // 子项目分组必须与组合分组一致
                    if ($curSubItem['group'] != $suitItem['group'])
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，分组与主项不一致';
                        break;
                    }

                    // 子项状态
                    if ($itemInfo['status'] != 1)
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，非上线状态';
                        break;
                    }

                    // 子项一级类型是否是可开具的类型
                    if (!in_array($itemInfo['first_sale_type_id'], ItemSaleTyeEnum::BEAUTY_SALE_TYPE))
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，商品一级类型非洗美，不可开具';
                        break;
                    }

                    // 记录子项商品附属信息
                    $curSubItem['itemId']    = $itemId;
                    $curSubItem['itemPrice'] = 0;

                    // 当前组合明细下商品item的信息
                    $curSuitSubItemInfos[$itemId] = $relationItemInfo;

                    unset($curSubRelationItems[$relationItemKey]);
                    unset($diffCurSuitSubItems[$subItemKey]);
                    break;
                }
            }

            if (!empty($subStatusError))
            {
                $statusErrorMsg[] = implode("；\n", $subStatusError);

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            // 提交的子项与关联子项不一致
            if (!empty($diffCurSuitSubItems))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，提交子项与设定关联子项内容不一致';

                return self::Fail(implode("；\n", $statusErrorMsg), 500291);
            }

            if (!empty($suitItem['uid']) && !empty($suitItem['suitUniqueId']))
            {
                $curUid           = $suitItem['uid'];
                $curSuitUniqueUid = $suitItem['suitUniqueId'];
            }
            else
            {
                $curUid           = generateUUID();
                $curSuitUniqueUid = generateUUID();
            }

            // 组合主项
            $tmpSuitItemData = [
                'item_uid'        => $suitItem['itemUid'],
                'uid'             => $curUid,
                'sheet_id'        => 0,
                'hospital_id'     => $hospitalId,
                'member_id'       => $memberId,
                'pet_id'          => $petId,
                'item_suit_id'    => $curSuitItemId,
                'item_id'         => 0,
                'price'           => $suitItem['itemPrice'],
                'quantity'        => $curSuitQuantity,
                'remark'          => $suitItem['remark'] ?? '',
                'suit_group'      => self::GetItemGroupInfo('suit_group', $suitItem['group'] ?? ''),
                'is_suit'         => 1,
                'suit_unique_uid' => $curSuitUniqueUid,
                'status'          => 1,
                'created_by'      => $userId,
            ];

            // 组合子项，按照组合内子项的顺序来组合
            foreach ($curSuitSubItems as $sortSubItem)
            {
                $curSuitItemQuantity = intval($sortSubItem['quantity']);

                $tmpSuitSubItemData = [
                    'item_uid'        => $sortSubItem['itemUid'],
                    'uid'             => !empty($sortSubItem['uid']) ? $sortSubItem['uid'] : generateUUID(),
                    'sheet_id'        => 0,
                    'hospital_id'     => $hospitalId,
                    'member_id'       => $memberId,
                    'pet_id'          => $petId,
                    'item_suit_id'    => $curSuitItemId,
                    'item_id'         => $sortSubItem['itemId'],
                    'price'           => $sortSubItem['itemPrice'],
                    'remark'          => $sortSubItem['remark'] ?? '',
                    'quantity'        => $curSuitItemQuantity,
                    'suit_group'      => self::GetItemGroupInfo('suit_group', $sortSubItem['group'] ?? ''),
                    'is_suit'         => 0,
                    'suit_unique_uid' => $curSuitUniqueUid,
                    'status'          => 1,
                    'created_by'      => $userId,
                ];

                if (!isset($tmpSuitItemData['suit_items']))
                {
                    $tmpSuitItemData['suit_items'] = [];
                }

                // 组合子项
                $tmpSuitItemData['suit_items'][] = $tmpSuitSubItemData;
            }

            $addSuitTotalPrice    = numberAdd([
                                                  $addSuitTotalPrice,
                                                  numberMul([$curSuitQuantity, $suitItem['itemPrice']])
                                              ]);
            $returnAddSuitItems[] = $tmpSuitItemData;
        }

        return self::Success(['totalPrice' => $addSuitTotalPrice, 'suitItems' => $returnAddSuitItems]);
    }

    /**
     * 格式化构建洗美服务单主信息
     *
     * @param array $sheets
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function FormatDetailStructure(array $sheets, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheets))
        {
            return self::Fail('洗美服务单信息错误', 400);
        }
        if (empty($publicParams) || empty($hospitalId))
        {
            return self::Fail('公共参数错误', 400);
        }


        //获取关联的数据
        $petIds        = array_unique(array_column($sheets, 'pet_id'));
        $createUserIds = array_unique(array_column($sheets, 'created_by'));

        //获取宠物和会员信息
        $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }
        $petRes = $getPetRes->getData();

        //获取创建人信息
        $getCreateUserRes = HospitalUserHelper::GetUserOptionsByUserIds($createUserIds, $hospitalId, true);

        $result = [];
        foreach ($sheets as $sheet)
        {
            $curPet = $petRes[$sheet['pet_id']] ?? [];
            if (empty($curPet))
            {
                return self::Fail('宠物不存在', 32000);
            }
            $curPetInfo    = $curPet['petInfo'] ?? null;
            $curMemberInfo = $curPet['memberInfo'] ?? null;
            $curCreateUser = $getCreateUserRes[$sheet['created_by']] ?? null;

            $checkEditRes = self::CheckSheetEditOrDelete($sheet, $publicParams);
            $editAble     = $checkEditRes->isSuccess();

            $result[] = [
                'sheetCode'  => $sheet['sheet_code'],
                'memberInfo' => $curMemberInfo,
                'petInfo'    => $curPetInfo,
                'price'      => formatDisplayNumber($sheet['price'], 2, false),
                'status'     => [
                    'id'   => $sheet['status'],
                    'name' => SheetStatusEnum::getDescription($sheet['status']),
                ],
                'createUser' => $curCreateUser,
                'orderTime'  => formatDisplayDateTime($sheet['order_time']),
                'createTime' => formatDisplayDateTime($sheet['created_at']),
                'updateTime' => formatDisplayDateTime($sheet['updated_at']),
                'editAble'   => $editAble,
            ];
        }

        return self::Success($result);
    }

    /**
     * 格式化构建洗美服务单内商品明细
     *
     * @param array $sheetItems
     * @param array $publicParams
     * @param bool  $withItemPrice
     *
     * @return LogicResult
     */
    public static function FormatSheetItemStructure(array $sheetItems, array $publicParams, bool $withItemPrice = false
    ): LogicResult
    {
        if (empty($sheetItems))
        {
            return self::Fail('格式化洗美服务单详情，明细不存在', 500311);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('缺少医院必选参数', 400);
        }

        // 服务单内商品ID、组合ID
        $itemIds     = [];
        $itemSuitIds = [];
        array_walk($sheetItems, function ($item) use (&$itemIds, &$itemSuitIds) {
            if (empty($item['is_suit']) && $item['item_id'] > 0)
            {
                $itemIds[$item['item_id']] = $item['item_id'];
            }
            else
            {
                $itemSuitIds[$item['item_suit_id']] = $item['item_suit_id'];
            }
        });

        // 获取服务单内商品
        $getItemInfoRes     = [];
        $getSuitItemInfoRes = [];
        if (!empty($itemIds))
        {
            $getItemInfoRes = ItemBeautyModel::getManyByIds($itemIds);
            $getItemInfoRes = $getItemInfoRes->isNotEmpty() ? $getItemInfoRes->keyBy('id')
                                                                             ->toArray() : [];
        }
        if (!empty($itemSuitIds))
        {
            $getSuitItemInfoRes = ItemSuitModel::getManyByIds($itemSuitIds);
            $getSuitItemInfoRes = $getSuitItemInfoRes->isNotEmpty() ? $getSuitItemInfoRes->keyBy('id')
                                                                                         ->toArray() : [];
        }
        if (empty($getItemInfoRes) && empty($getSuitItemInfoRes))
        {
            return self::Fail('洗美服务单中商品全部无效', 500204);
        }

        // 获取商品项目类型
        $getItemSaleTypeRes = ItemSaleTypeModel::getAllSaleType([0, 1]);
        $getItemSaleTypeRes = $getItemSaleTypeRes->isNotEmpty() ? $getItemSaleTypeRes->keyBy('id')
                                                                                     ->toArray() : [];

        // 获取商品价格
        $getItemPriceRes     = [];
        $getSuitItemPriceRes = [];
        if ($withItemPrice)
        {
            // 获取医院信息
            $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
            if ($getHospitalRes->isFail())
            {
                return $getHospitalRes;
            }

            // 医院所属城市
            $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
            $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

            if (!empty($itemIds))
            {
                $getItemPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Beauty->value,
                                                                        $itemIds,
                                                                        $orgId,
                                                                        $brandId,
                                                                        $hospitalProvinceId,
                                                                        $hospitalCityId,
                                                                        $hospitalId);
            }
            if (!empty($itemSuitIds))
            {
                $getSuitItemPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Spu->value,
                                                                            $itemSuitIds,
                                                                            $orgId,
                                                                            $brandId,
                                                                            $hospitalProvinceId,
                                                                            $hospitalCityId,
                                                                            $hospitalId);
            }
        }

        // 预处理组合项子项映射（suit_unique_uid => [[子项列表], [子项列表]]）
        $suitChildrenMap = [];
        foreach ($sheetItems as $item)
        {
            if (empty($item['is_suit']) && !empty($item['suit_unique_uid']))
            {
                $suitChildrenMap[$item['suit_unique_uid']][] = $item;
            }
        }

        // 当前方法内定义递归构建逻辑
        $buildItem = function ($sheetItem) use (
            &$buildItem, $getItemInfoRes, $getSuitItemInfoRes, $getItemPriceRes,
            $getSuitItemPriceRes, $getItemSaleTypeRes, $suitChildrenMap
        ) {
            // 商品信息、价格
            if (empty($sheetItem['is_suit']) && !empty($getItemInfoRes[$sheetItem['item_id']]))
            {
                $curItemInfo      = $getItemInfoRes[$sheetItem['item_id']];
                $curItemPriceInfo = $getItemPriceRes[$sheetItem['item_id']]['sale_price'] ?? [];
                $saleTypeFiledKey = 'first_sale_type_id';
            }
            else
            {
                $curItemInfo      = $getSuitItemInfoRes[$sheetItem['item_suit_id']];
                $curItemPriceInfo = $getSuitItemPriceRes[$sheetItem['item_suit_id']]['sale_price'] ?? [];
                $saleTypeFiledKey = 'sale_type_id';
            }

            // 商品出库单位规格
            $curItemUseUnit   = $curItemInfo['use_unit'] ?? '';
            $curItemUseRatio  = $curItemInfo['use_ratio'] ?? 0;
            $curItemBulkUnit  = $curItemInfo['bulk_unit'] ?? '';
            $curItemBulkRatio = $curItemInfo['bulk_ratio'] ?? 0;
            $curItemPackUnit  = $curItemInfo['pack_unit'] ?? '';

            // 如果没有整装单位、散装单位使，用计量单位
            if (empty($curItemBulkUnit))
            {
                $curItemBulkUnit = $curItemUseUnit;
            }
            if (empty($curItemPackUnit))
            {
                $curItemPackUnit = $curItemUseUnit;
            }

            // 商品出库单位，1ml/ml、1次、1瓶。（计量比 + 计量单位 + 散装单位 ）
            $curItemSpec = 1 . $curItemUseUnit;

            // 商品类型
            $curItemTypeInfo = [
                'id'   => $curItemInfo[$saleTypeFiledKey] ?? 0,
                'name' => $getItemSaleTypeRes[$curItemInfo[$saleTypeFiledKey]]['alias_name'] ?? '',
            ];

            $tmpItemData = [
                'uid'          => $sheetItem['uid'] ?? '',
                'itemUid'      => $curItemInfo['uid'] ?? '',
                'type'         => $curItemTypeInfo,
                'isSuit'       => $sheetItem['is_suit'],
                'suitUniqueId' => $sheetItem['suit_unique_uid'],
                'group'        => self::FormatGroupDisplay(0, $sheetItem['suit_group']),
                'name'         => ItemHelper::ItemDisplayName($curItemInfo),
                'spec'         => $curItemSpec,
                'unit'         => $curItemBulkUnit,
                'quantity'     => formatDisplayNumber($sheetItem['quantity']),
                'price'        => isset($sheetItem['price']) ? formatDisplayNumber($sheetItem['price']) : 0,
                'totalPrice'   => isset($sheetItem['price']) ? formatDisplayNumber(numberMul([
                                                                                                 $sheetItem['price'],
                                                                                                 $sheetItem['quantity']
                                                                                             ])) : 0,
                'remark'       => $sheetItem['remark'],
                'createTime'   => formatDisplayDateTime($sheetItem['created_at']),
                'itemInfo'     => [
                    'uid'               => $curItemInfo['uid'] ?? '',
                    'type'              => $curItemTypeInfo,
                    'name'              => ItemHelper::ItemDisplayName($curItemInfo),
                    'useUnit'           => $curItemUseUnit,
                    'useRatio'          => $curItemUseRatio,
                    'spec'              => $curItemSpec,
                    'packUnit'          => $curItemPackUnit,
                    'packPrice'         => formatDisplayNumber($curItemPriceInfo['pack_sale_price'] ?? 0),
                    'bulkUnit'          => $curItemBulkUnit,
                    'bulkPrice'         => formatDisplayNumber($curItemPriceInfo['bulk_sale_price'] ?? 0),
                    'bulkRatio'         => $curItemBulkRatio,
                    'stock'             => [],
                    'isPreciseMetering' => false,
                    'isPackSaleAllow'   => false,
                ],
                'isPrintItems' => null,
                'suitItems'    => [],
            ];

            // 如果是组合，返回是否打印子项
            if (!empty($sheetItem['is_suit']))
            {
                $tmpItemData['isPrintItems'] = $curItemInfo['is_print_items'];
            }

            // 如果是组合，递归构建子项
            if (!empty($sheetItem['is_suit']) && !empty($sheetItem['suit_unique_uid']))
            {
                foreach ($suitChildrenMap[$sheetItem['suit_unique_uid']] ?? [] as $childItem)
                {
                    $tmpItemData['suitItems'][] = $buildItem($childItem); // 递归构建
                }
            }

            return $tmpItemData;
        };

        // 执行主构建逻辑（排除掉子项）
        $returnItemData = [];
        foreach ($sheetItems as $item)
        {
            // 处方内组合子项过滤，由主项递归构建
            if (empty($item['is_suit']) && !empty($item['suit_unique_uid']))
            {
                continue;
            }

            $returnItemData[] = $buildItem($item);
        }

        return self::Success($returnItemData);
    }

    /**
     * 格式化构建洗美服务单列表
     *
     * @param array $sheetList
     * @param array $publicParams
     * @param bool  $withExecutor
     *
     * @return array
     */
    public static function FormatSheetListStructure(array $sheetList, array $publicParams, bool $withExecutor = false
    ): array
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetList) || empty($hospitalId))
        {
            return [];
        }

        //获取关联数据ID
        $sheetIds      = array_column($sheetList, 'id');
        $petIds        = array_column($sheetList, 'pet_id');
        $createUserIds = array_column($sheetList, 'created_by');
        //批量获取关联数据
        $petRes           = [];
        $getCreateUserRes = [];
        $getExecutorRes   = [];
        if (!empty($petIds))
        {
            $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
            if ($getPetRes->isFail())
            {
                return [];
            }

            $petRes = $getPetRes->getData();
        }
        if (!empty($createUserIds))
        {
            $getCreateUserRes = HospitalUserHelper::GetUserOptionsByUserIds($createUserIds, $hospitalId, true);
        }
        if ($withExecutor)
        {
            $getExecutorRes = self::FormatServiceExecutorStructure($sheetIds, $publicParams);
        }

        $result = [];
        foreach ($sheetList as $sheet)
        {
            //从宠物详情中获取会员和宠物信息
            $curPetRes     = $petRes[$sheet['pet_id']] ?? [];
            $curMemberInfo = $curPetRes['memberInfo'] ?? null;
            $curPetInfo    = $curPetRes['petInfo'] ?? null;

            // 创建人
            $curCreateUser = $getCreateUserRes[$sheet['created_by']] ?? null;

            $checkEditRes = self::CheckSheetEditOrDelete($sheet, $publicParams);
            $editAble     = $checkEditRes->isSuccess();

            $tmp = [
                'sheetCode'  => $sheet['sheet_code'],
                'memberInfo' => $curMemberInfo,
                'petInfo'    => $curPetInfo,
                'price'      => formatDisplayNumber($sheet['price'], 2, false),
                'status'     => [
                    'id'   => $sheet['status'],
                    'name' => SheetStatusEnum::getDescription($sheet['status']),
                ],
                'createUser' => $curCreateUser,
                'orderTime'  => formatDisplayDateTime($sheet['order_time']),
                'createTime' => formatDisplayDateTime($sheet['created_at']),
                'updateTime' => formatDisplayDateTime($sheet['updated_at']),
                'payTime'    => formatDisplayDateTime($sheet['paid_at']),
                'editAble'   => $editAble,
            ];

            if ($withExecutor)
            {
                $tmp['executeStatus'] = [
                    'id'   => $sheet['execute_status'],
                    'name' => BeautyExecuteStatusEnum::getDescription($sheet['execute_status']),
                ];
                $tmp['executors']     = $getExecutorRes[$sheet['id']] ?? null;
            }

            $result[] = $tmp;
        }

        return $result;
    }

    /**
     * 格式化构建洗美服务单内执行人信息
     *
     * @param array $sheetIds
     * @param array $publicParams
     *
     * @return array
     */
    public static function FormatServiceExecutorStructure(array $sheetIds, array $publicParams): array
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));

        if (empty($sheetIds) || empty($publicParams) || $hospitalId <= 0)
        {
            return [];
        }

        $sheetExecutors = BeautyServiceExecutorModel::getData(
            where:    ['hospital_id' => $hospitalId, 'assistant_status' => BeautyExecuteStatusEnum::Completed->value],
            whereIn:  ['sheet_id' => $sheetIds],
            orderBys: ['sheet_id' => 'asc', 'assistant_level' => 'asc']
        );

        if (empty($sheetExecutors))
        {
            return [];
        }

        $executorUserIds = array_unique(array_column($sheetExecutors, 'user_id'));
        $executorUsers   = HospitalUserHelper::GetUserOptionsByUserIds($executorUserIds, $hospitalId, true);

        $result = [];
        foreach ($sheetIds as $sheetId)
        {
            $curExecutors = [];
            foreach ($sheetExecutors as $key => $executor)
            {
                if ($executor['sheet_id'] != $sheetId)
                {
                    continue;
                }
                $curExecutorUser = $executorUsers[$executor['user_id']] ?? null;

                $curExecutors[] = [
                    'uid'   => $curExecutorUser ? $curExecutorUser['uid'] : null,
                    'name'  => $curExecutorUser ? $curExecutorUser['name'] : null,
                    'level' => $executor['assistant_level'],
                ];

                unset($sheetExecutors[$key]);
            }

            $result[$sheetId] = $curExecutors;
        }

        return $result;
    }

    /**
     * 验证服务单是否可以编辑或删除
     *
     * @param array|ArrayAccess $sheet
     * @param array             $publicParams
     *
     * @return LogicResult
     */
    public static function CheckSheetEditOrDelete(array|ArrayAccess $sheet, array $publicParams): LogicResult
    {
        $userId = intval(Arr::get($publicParams, '_userId'));

        if (empty($sheet) || empty($publicParams) || empty($userId))
        {
            return self::Fail('验证洗美服务单是否可操作，缺少必选参数', 400);
        }

        if (!SheetStatusEnum::EditAble($sheet['status']))
        {
            return self::Fail('洗美服务单当前状态不允许此操作', 500340);
        }

        //TODO:根据是否本人和是否院长角色来判断

        return self::Success();
    }

    /**
     * 获取商品分组信息
     *
     * @param string $fieldKey
     * @param string $group
     *
     * @return int
     */
    private static function GetItemGroupInfo(string $fieldKey, string $group): int
    {
        // 判断是否为组合分组（如 G1 或 G-1）
        if (preg_match('/^G-?(\d+)$/i', $group, $matches))
        {
            return $fieldKey === 'suit_group' ? (int) $matches[1] : 0;
        }

        // 普通分组
        return $fieldKey === 'group' ? (int) $group : 0;
    }

    /**
     * 格式化分组展示
     *
     * @param int $group     单品分组，1、2
     * @param int $suitGroup 组合分组，G1、G2
     *
     * @return string|null
     */
    private static function FormatGroupDisplay(int $group = 0, int $suitGroup = 0): string|null
    {
        if (empty($group) && empty($suitGroup))
        {
            return null;
        }

        if ($suitGroup > 0)
        {
            return 'G' . $suitGroup;
        }

        return (string) $group;
    }
}
