<?php

namespace App\Support\User;

use App\Models\HospitalUserModel;

class HospitalUserHelper
{
    /**
     * 根据用户ID列表获取医院用户选项
     *
     * @param array $userIds
     * @param int   $hospitalId
     * @param bool  $keyByUserId
     *
     * @return array
     */
    public static function GetUserOptionsByUserIds(array $userIds, int $hospitalId, bool $keyByUserId = false): array
    {
        $getUsersRes = HospitalUserModel::getHospitalUsers(
            hospitalId: $hospitalId,
            userIds:    $userIds
        );

        $result = [];

        if ($getUsersRes === null || $getUsersRes->isEmpty())
        {
            return $result;
        }

        $users = $getUsersRes->keyBy('user_id');

        foreach ($userIds as $userId)
        {
            $user = $users[$userId] ?? null;
            if (!$user)
            {
                continue;
            }

            if ($keyByUserId)
            {
                $result[$userId] = [
                    'uid'  => $user['user_uid'],
                    'name' => $user['user_name'],
                ];
            }
            else
            {
                $result[] = [
                    'uid'  => $user['user_uid'],
                    'name' => $user['user_name'],
                ];
            }
        }

        return $result;
    }
}
