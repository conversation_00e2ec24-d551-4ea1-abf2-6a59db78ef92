<?php

namespace App\Support\Purchase;

use Arr;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Enums\PurchaseEnum;
use App\Enums\ItemStatusEnum;
use App\Enums\PurchaseTypeEnum;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\PurchaseOrderStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\ItemLogic;
use App\Logics\V1\HospitalLogic;
use App\Logics\V1\StockItemShelfLogic;
use App\Logics\V1\PurchaseSupplierLogic;
use App\Logics\V1\StockItemDailyPriceLogic;
use App\Models\ItemSelfPurchaseWhitelistModel;

class PurchaseHelper extends Logic
{
    /**
     * 验证采购单提交是否正确，并返回完整信息
     *
     * @param array $addPurchaseParams
     * @param array $publicParams
     * @param array $oldPurchaseInfo
     * @param array $oldPurchaseItemInfo
     *
     * @return LogicResult
     */
    public static function GetValidPurchaseParams(array $addPurchaseParams, array $publicParams, array $oldPurchaseInfo = [], array $oldPurchaseItemInfo = []): LogicResult
    {
        if (empty($addPurchaseParams))
        {
            return self::Fail('验证采购单，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证采购单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证采购单，缺少医院相关必选参数', 400);
        }

        // 业务参数
        $purchaseType          = intval(Arr::get($addPurchaseParams, 'purchaseType', 0));
        $supplierUid           = trim(Arr::get($addPurchaseParams, 'supplierUid', ''));
        $allocationHospitalUid = trim(Arr::get($addPurchaseParams, 'fromHospitalUid', ''));
        $purchaseRemark        = trimWhitespace(Arr::get($addPurchaseParams, 'remark', ''));
        $submitType            = intval(Arr::get($addPurchaseParams, 'submitType', 0));
        $totalPrice            = (string) Arr::get($addPurchaseParams, 'totalPrice', '');
        $purchaseItems         = Arr::get($addPurchaseParams, 'items', []);
        if (empty($purchaseType) || !in_array($purchaseType, PurchaseTypeEnum::values()))
        {
            return self::Fail('采购类型错误', 42000);
        }
        if (!empty($oldPurchaseInfo) && $oldPurchaseInfo['purchase_type'] != $purchaseType)
        {
            return self::Fail('采购类型不可修改', 42014);
        }
        if (PurchaseTypeEnum::getPurchaseIsSelfPurchase($purchaseType) && empty($supplierUid))
        {
            return self::Fail('采购供应商选择错误', 42000);
        }
        if (PurchaseTypeEnum::getPurchaseIsTransfer($purchaseType) && empty($allocationHospitalUid))
        {
            return self::Fail('调拨源门店选择错误', 42000);
        }
        if (PurchaseTypeEnum::getPurchaseIsGroupPurchase($purchaseType) && (!empty($supplierUid) || !empty($allocationHospitalUid)))
        {
            return self::Fail('集采不可选择供应商或调拨源门店', 42000);
        }
        if (empty($purchaseRemark))
        {
            return self::Fail('采购说明不可为空', 42003);
        }
        if (!in_array($submitType, [PurchaseOrderStatusEnum::Draft->value, PurchaseOrderStatusEnum::Pending->value]))
        {
            return self::Fail('提交类型错误', 42004);
        }
        if (bccomp($totalPrice, 0, 4) == - 1)
        {
            return self::Fail('采购总价不可小于0', 42008);
        }
        if (empty($purchaseItems))
        {
            return self::Fail('采购商品不可为空', 42005);
        }

        // 供应商ID、调拨源门店ID
        $supplierId           = 0;
        $allocationHospitalId = 0;

        // 集采，使用默认供应商
        if (PurchaseTypeEnum::getPurchaseIsGroupPurchase($purchaseType))
        {
            $supplierId = PurchaseEnum::GroupPurchaseDefaultSupplier->value;
        }
        else
        {
            // 自采，采购供应商
            if (!empty($supplierUid))
            {
                $getSupplierRes = PurchaseSupplierLogic::GetValidPurchaseSupplier(supplierUid: $supplierUid);
                if ($getSupplierRes->isFail())
                {
                    return $getSupplierRes;
                }
                if ($getSupplierRes->getData('purchase_type', 0) != $purchaseType)
                {
                    return self::Fail('选择的采购供应商非当前采购类型', 42000);
                }
                $supplierId = $getSupplierRes->getData('id', 0);
            }

            // 调拨，验证医院
            if (!empty($allocationHospitalUid))
            {
                $getAllocationHospitalRes = HospitalLogic::GetHospitalBaseInfo(uid: $allocationHospitalUid, withId: true);
                if ($getAllocationHospitalRes->isFail())
                {
                    return $getAllocationHospitalRes;
                }

                // 调拨源门店不能是当前门店
                $allocationHospitalId = $getAllocationHospitalRes->getData('id', 0);
                if ($allocationHospitalId == $hospitalId)
                {
                    return self::Fail('调拨源门店不能是当前门店', 42006);
                }

                // 验证调拨源门店是否存在，并且是否可以接受
                $getAllocationHospitalRes = HospitalLogic::getTransferHospitalList($publicParams, $allocationHospitalId);
                if ($getAllocationHospitalRes->isFail())
                {
                    return $getAllocationHospitalRes;
                }
                if (empty($getAllocationHospitalRes->getData()))
                {
                    return self::Fail('调拨源门店不存在或不可接受调拨', 42006);
                }
            }
        }

        // 编辑时，采购供应商、调拨源门店不可修改
        if (!empty($oldPurchaseInfo) && ($oldPurchaseInfo['supplier_id'] != $supplierId || $oldPurchaseInfo['allot_hospital_id'] != $allocationHospitalId))
        {
            return self::Fail('采购供应商或调拨源门店不可修改', 42014);
        }

        // 采购供应商、调拨源门店ID
        $addPurchaseParams['supplierId']           = $supplierId;
        $addPurchaseParams['allocationHospitalId'] = $allocationHospitalId;

        // 验证采购商品
        $getValidPurchaseItemParamsRes = self::GetValidPurchaseItemParams($addPurchaseParams, $publicParams, $oldPurchaseItemInfo);
        if ($getValidPurchaseItemParamsRes->isFail())
        {
            return $getValidPurchaseItemParamsRes;
        }
        if (empty($getValidPurchaseItemParamsRes->getData()))
        {
            return self::Fail('采购商品全部无效', 42005);
        }

        // 验证后的采购商品，保持原结构。重新赋值是因为可能存在对价格等重新计算
        $addPurchaseParams['items'] = $getValidPurchaseItemParamsRes->getData();

        // 如果是集采、调拨，计算出采购单总价。自采已经存在总价并且是验证过的
        if (PurchaseTypeEnum::getPurchaseIsTransfer($purchaseType) || PurchaseTypeEnum::getPurchaseIsGroupPurchase($purchaseType))
        {
            $totalPrice = 0;
            foreach ($addPurchaseParams['items'] as $curPurchaseItem)
            {
                $totalPrice = numberAdd([$totalPrice, $curPurchaseItem['totalPrice']], 4);
            }

            $addPurchaseParams['totalPrice'] = $totalPrice;
        }

        return self::Success($addPurchaseParams);
    }

    /**
     * 验证采购单内添加的商品
     *
     * @param array $addPurchaseParams
     * @param array $publicParams
     * @param array $oldPurchaseItemInfo
     *
     * @return LogicResult
     */
    public static function GetValidPurchaseItemParams(array $addPurchaseParams, array $publicParams, array $oldPurchaseItemInfo = []): LogicResult
    {
        if (empty($addPurchaseParams))
        {
            return self::Fail('验证采购商品，缺少采购商品必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证采购商品，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($hospitalId))
        {
            return self::Fail('验证采购商品，缺少医院相关必选参数', 400);
        }

        // 业务参数
        $purchaseType         = intval(Arr::get($addPurchaseParams, 'purchaseType', 0));
        $supplierId           = intval(Arr::get($addPurchaseParams, 'supplierId', 0));
        $allocationHospitalId = intval(Arr::get($addPurchaseParams, 'allocationHospitalId', 0));
        $purchaseItems        = Arr::get($addPurchaseParams, 'items', []);
        $totalPrice           = Arr::get($addPurchaseParams, 'totalPrice', 0);
        if (empty($purchaseType) || !in_array($purchaseType, PurchaseTypeEnum::values()))
        {
            return self::Fail('验证采购商品，采购类型错误', 42000);
        }
        if (empty($supplierId) && empty($allocationHospitalId))
        {
            return self::Fail('验证采购商品，缺少供应商或调拨源门店ID', 42000);
        }
        if (empty($purchaseItems))
        {
            return self::Fail('验证采购商品，采购商品不可为空', 42005);
        }

        // 旧的采购商品信息
        if (!empty($oldPurchaseItemInfo))
        {
            $oldPurchaseItemInfo = array_column($oldPurchaseItemInfo, null, 'uid');
        }

        // 验证参数
        $errorMsg           = [];
        $arrUniqueItemUids  = [];
        $totalPurchasePrice = 0;
        foreach ($purchaseItems as $curPurchaseItem)
        {
            $curUid      = $curPurchaseItem['uid'] ?? '';
            $curItemName = $curPurchaseItem['itemName'] ?? '';
            $curItemUid  = $curPurchaseItem['itemUid'] ?? '';
            if (empty($curItemUid))
            {
                return self::Fail('验证采购商品，缺少商品UID', 42007);
            }
            if (empty($curPurchaseItem['itemBarcode']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少商品条码';
                continue;
            }

            // 编辑提交时，是否商品一致
            $curOldPurchaseItem = $oldPurchaseItemInfo[$curUid] ?? [];
            if (!empty($curUid) && empty($curOldPurchaseItem))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，原采购商品已不存在或已失效，请刷新重试';
                continue;
            }
            if (!empty($curOldPurchaseItem) && $curPurchaseItem['itemBarcode'] != $curOldPurchaseItem['item_barcode'])
            {
                $errorMsg[] = '名称【' . $curItemName . '】，提交采购商品与原商品条码不一致';
                continue;
            }

            // 验证采购数量
            $curPackQuantity = $curPurchaseItem['packQuantity'] ?: 0;
            $curBulkQuantity = $curPurchaseItem['bulkQuantity'] ?: 0;
            if ($curPackQuantity <= 0 && $curBulkQuantity <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，散装数量、整装数量不可同时为0';
                continue;
            }

            // 如果为集采、调拨，整装采购价、散装采购价设置为0，不可填写采购价
            if (PurchaseTypeEnum::getPurchaseIsGroupPurchase($purchaseType) || PurchaseTypeEnum::getPurchaseIsTransfer($purchaseType))
            {
                // 集采、调拨，采购价必须为0
                if (bccomp($curPurchaseItem['packPurchasePrice'], 0, 4) != 0 || bccomp($curPurchaseItem['bulkPurchasePrice'], 0, 4) != 0)
                {
                    $errorMsg[] = '名称【' . $curItemName . '】，集采、调拨不可填写整装、散装采购价';
                    continue;
                }
            }

            // 验证自采采购价是否正确
            $curTotalPrice = $curPurchaseItem['totalPrice'];
            $curPackPrice  = $curPurchaseItem['packPurchasePrice'];
            $curBulkPrice  = $curPurchaseItem['bulkPurchasePrice'];
            if (PurchaseTypeEnum::getPurchaseIsSelfPurchase($purchaseType))
            {
                if (bccomp($curPackPrice, 0, 4) != 1 && bccomp($curBulkPrice, 0, 4) != 1)
                {
                    $errorMsg[] = '名称【' . $curItemName . '】，散装采购价、整装采购价不可同时为0';
                    continue;
                }
            }
            if ($curPackQuantity > 0 && bccomp($curPackPrice, 0, 4) == - 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，整装采购数量大于0时，整装采购价不可小于0';
                continue;
            }
            if ($curBulkQuantity > 0 && bccomp($curBulkPrice, 0, 4) == - 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，散装采购数量大于0时，散装采购价不可小于0';
                continue;
            }

            // 验证整装采购总价
            $curPackTotalPrice = 0;
            if ($curPackQuantity > 0)
            {
                $curPackTotalPrice = numberMul([$curPackQuantity, $curPackPrice], 4);
                if (bccomp($curPackTotalPrice, $curPurchaseItem['packTotalPrice'], 4) != 0)
                {
                    $errorMsg[] = '名称【' . $curItemName . '】，整装采购数量、单价与总价不匹配';
                    continue;
                }
            }

            // 验证散装采购总价
            $curBulkTotalPrice = 0;
            if ($curBulkQuantity > 0)
            {
                $curBulkTotalPrice = numberMul([$curBulkQuantity, $curBulkPrice], 4);
                if (bccomp($curBulkTotalPrice, $curPurchaseItem['bulkTotalPrice'], 4) != 0)
                {
                    $errorMsg[] = '名称【' . $curItemName . '】，散装采购数量、单价与总价不匹配';
                    continue;
                }
            }

            // 散装总采购价 + 整装总采购价 != 当前商品采购总价
            if (bccomp($curTotalPrice, numberAdd([$curPackTotalPrice, $curBulkTotalPrice], 4), 4) != 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，整装、散装采购总价与商品总价不匹配';
                continue;
            }

            if (in_array($curPurchaseItem['itemUid'], $arrUniqueItemUids))
            {
                $errorMsg[]          = '名称【' . $curItemName . '】，不可重复添加商品';
                $arrUniqueItemUids[] = $curPurchaseItem['itemUid'];
            }

            $totalPurchasePrice  = numberAdd([$totalPurchasePrice, $curPackTotalPrice, $curBulkTotalPrice], 4);
            $arrUniqueItemUids[] = $curPurchaseItem['itemUid'];
        }

        // 采购商品基本校验
        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42007);
        }

        // 总采购金额是否一致
        if (bccomp($totalPurchasePrice, $totalPrice, 4) != 0)
        {
            return self::Fail('采购商品总价不一致', 42007);
        }

        // 采购商品信息
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemUids: $arrUniqueItemUids, publicParams: $publicParams, withItemStock: true);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData();
        $getItemInfoRes = array_column($getItemInfoRes, null, 'uid');
        if (empty($getItemInfoRes))
        {
            return self::Fail('采购商品基本信息不存在', 42007);
        }

        foreach ($purchaseItems as &$curPurchaseItem)
        {
            $curItemUid  = $curPurchaseItem['itemUid'] ?? '';
            $curItemName = $curPurchaseItem['itemName'] ?? '';

            // 商品、商品条码信息
            $curItemBaseInfo    = $getItemInfoRes[$curItemUid] ?? [];
            $curItemBarcodeInfo = $curItemBaseInfo['item_barcode_info'] ?? '';
            if (empty($curItemBaseInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品基本信息不存在';
                continue;
            }
            if (empty($curItemBarcodeInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品条码信息不存在';
                continue;
            }
            if ($curItemBarcodeInfo['item_barcode'] != $curPurchaseItem['itemBarcode'])
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品条码不一致';
                continue;
            }
            if ($curItemBaseInfo['status'] != ItemStatusEnum::Online->value)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非上线状态，不能保存采购单';
                continue;
            }
            if ($curItemBaseInfo['is_purchasable'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品已标记不在采购，不能保存采购单';
                continue;
            }
            if ($curItemBaseInfo['first_sale_type_id'] != ItemSaleTyeEnum::FirstDrug->value)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非普通商品、药品，不能保存采购单';
                continue;
            }

            $curPurchaseItem['itemId']   = $curItemBaseInfo['id'];
            $curPurchaseItem['itemInfo'] = $curItemBaseInfo;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42007);
        }

        // 根据不同采购类型，验证不同的信息
        match ($purchaseType)
        {
            // 集采
            PurchaseTypeEnum::GroupPurchase->value => $getCheckRes = self::CheckGroupPurchase($supplierId, $purchaseItems, $publicParams, $oldPurchaseItemInfo),

            // 自采
            PurchaseTypeEnum::SelfPurchase->value => $getCheckRes = self::CheckSelfPurchase($supplierId, $purchaseItems, $publicParams),

            // 调拨
            PurchaseTypeEnum::Transfer->value => $getCheckRes = self::CheckAllocationPurchase($allocationHospitalId, $purchaseItems, $publicParams, $oldPurchaseItemInfo),
        };

        // 验证失败
        if ($getCheckRes->isFail())
        {
            return $getCheckRes;
        }

        // 获取验证后的采购商品
        $purchaseItems = $getCheckRes->getData();

        return self::Success($purchaseItems);
    }

    /**
     * 验证集采
     *
     * @param int   $supplierId            集采供应商ID
     * @param array $addPurchaseItemParams 提交采购单商品
     * @param array $publicParams          公参
     * @param array $oldPurchaseItemInfo   编辑采购单时，旧的采购单商品
     *
     * @return LogicResult
     */
    private static function CheckGroupPurchase(int $supplierId, array $addPurchaseItemParams, array $publicParams, array $oldPurchaseItemInfo = []): LogicResult
    {
        if (empty($supplierId))
        {
            return self::Fail('验证集采采购商品，缺少供应商ID', 400);
        }
        if (empty($addPurchaseItemParams))
        {
            return self::Fail('验证集采采购商品，缺少采购商品必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($hospitalId))
        {
            return self::Fail('验证集采采购商品，缺少医院相关必选参数', 400);
        }

        $errorMsg = [];
        foreach ($addPurchaseItemParams as &$curPurchaseItem)
        {
            // 初始采购总价、整散价格
            $curPackPrice = 0;
            $curBulkPrice = 0;

            $curPackQuantity = $curPurchaseItem['packQuantity'] ?: 0;
            $curBulkQuantity = $curPurchaseItem['bulkQuantity'] ?: 0;

            // 编辑时，如果存在旧的采购价格，则使用旧的价格
            $curUid = $curPurchaseItem['uid'] ?? '';
            if (!empty($oldPurchaseItemInfo[$curUid]))
            {
                if ($oldPurchaseItemInfo[$curUid]['pack_purchase_price'] > 0)
                {
                    $curPackPrice = $oldPurchaseItemInfo[$curUid]['pack_purchase_price'];
                }
                if ($oldPurchaseItemInfo[$curUid]['bulk_purchase_price'] > 0)
                {
                    $curBulkPrice = $oldPurchaseItemInfo[$curUid]['bulk_purchase_price'];
                }
            }

            // 计算采购总数量，转化散为准
            $curPurchaseTotalQuantity = 0;
            if ($curPurchaseItem['packQuantity'] > 0)
            {
                $curItemBulkRatio         = $curPurchaseItem['itemInfo']['bulk_ratio'] ?? 1;
                $curPurchaseTotalQuantity = numberMul([$curPurchaseItem['packQuantity'], $curItemBulkRatio]);
            }
            if ($curPurchaseItem['bulkQuantity'] > 0)
            {
                $curPurchaseTotalQuantity = numberAdd([$curPurchaseTotalQuantity, $curPurchaseItem['bulkQuantity']]);
            }

            // 本次集采，整装、散装采购总价
            $curPackTotalPrice = numberMul([$curPackQuantity, $curPackPrice], 4);
            $curBulkTotalPrice = numberMul([$curBulkQuantity, $curBulkPrice], 4);
            $curTotalPrice     = numberAdd([$curPackTotalPrice, $curBulkTotalPrice], 4);

            // 计算本次散装平均价，入库时使用此价格。如果入库整装的话，当前散装平均价 * 整散比
            $curPurchaseItemAvgPrice = numberDiv([$curTotalPrice, $curPurchaseTotalQuantity], 4);
            if (bccomp($curPurchaseItemAvgPrice, 0, 4) == - 1)
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，散装平均价小于0，不能保存采购单';
                continue;
            }

            $curPurchaseItem['totalPrice']        = $curTotalPrice;
            $curPurchaseItem['packPurchasePrice'] = $curPackQuantity > 0 ? $curPackPrice : 0;
            $curPurchaseItem['packTotalPrice']    = $curPackTotalPrice;
            $curPurchaseItem['bulkPurchasePrice'] = $curBulkQuantity > 0 ? $curBulkPrice : 0;
            $curPurchaseItem['bulkTotalPrice']    = $curBulkTotalPrice;
            $curPurchaseItem['bulkAvgPrice']      = $curPurchaseItemAvgPrice;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42007);
        }

        return self::Success($addPurchaseItemParams);
    }

    /**
     * 验证自采
     *
     * @param int   $supplierId
     * @param array $addPurchaseItemParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function CheckSelfPurchase(int $supplierId, array $addPurchaseItemParams, array $publicParams): LogicResult
    {
        if (empty($supplierId))
        {
            return self::Fail('验证自采采购商品，缺少供应商ID', 400);
        }
        if (empty($addPurchaseItemParams))
        {
            return self::Fail('验证自采采购商品，缺少采购商品必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($hospitalId))
        {
            return self::Fail('验证自采采购商品，缺少医院相关必选参数', 400);
        }

        // 自采白名单商品ID
        $selfPurchaseItemIds = [];
        array_walk($addPurchaseItemParams, function ($item) use (&$selfPurchaseItemIds) {
            if (!empty($item['itemInfo']['id']))
            {
                $selfPurchaseItemIds[] = $item['itemInfo']['id'];
            }
        });

        // 获取自采白名单商品
        $getSelfPurchaseItemWhere = [
            'orgId'      => $orgId,
            'hospitalId' => $hospitalId,
            'supplierId' => $supplierId,
            'itemIds'    => $selfPurchaseItemIds,
        ];
        $getSelfPurchaseItemRes   = ItemSelfPurchaseWhitelistModel::getSelfPurchaseItemList($getSelfPurchaseItemWhere, 0);
        if (empty($getSelfPurchaseItemRes))
        {
            return self::Fail('采购商品全部不在自采白名单中', 42007);
        }

        // 是否存在不在自采白名单中的商品
        $errorMsg                 = [];
        $notInSelfPurchaseItemIds = array_diff($selfPurchaseItemIds, array_column($getSelfPurchaseItemRes, 'id'));

        foreach ($addPurchaseItemParams as &$curPurchaseItem)
        {
            if (in_array($curPurchaseItem['itemInfo']['id'], $notInSelfPurchaseItemIds))
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，商品不在自采白名单中';
                continue;
            }

            // 计算采购总数量，转化散为准
            $curPurchaseTotalQuantity = 0;
            if ($curPurchaseItem['packQuantity'] > 0)
            {
                $curItemBulkRatio         = $curPurchaseItem['itemInfo']['bulk_ratio'] ?? 1;
                $curPurchaseTotalQuantity = numberMul([$curPurchaseItem['packQuantity'], $curItemBulkRatio]);
            }
            if ($curPurchaseItem['bulkQuantity'] > 0)
            {
                $curPurchaseTotalQuantity = numberAdd([$curPurchaseTotalQuantity, $curPurchaseItem['bulkQuantity']]);
            }

            // 计算本次散装平均价，入库时使用此价格。如果入库整装的话，当前散装平均价 * 整散比
            $curPurchaseItemAvgPrice = numberDiv([$curPurchaseItem['totalPrice'], $curPurchaseTotalQuantity], 4);
            if (bccomp($curPurchaseItemAvgPrice, 0, 4) == - 1)
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，散装平均价小于0，不能保存采购单';
                continue;
            }

            $curPurchaseItem['bulkAvgPrice'] = $curPurchaseItemAvgPrice;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42007);
        }

        return self::Success($addPurchaseItemParams);
    }

    /**
     * 验证调拨
     *
     * @param int   $allocationHospitalId  调拨医院ID
     * @param array $addPurchaseItemParams 提交采购单商品
     * @param array $publicParams          公参
     * @param array $oldPurchaseItemInfo   编辑采购单时，旧的采购单商品
     *
     * @return LogicResult
     */
    private static function CheckAllocationPurchase(int $allocationHospitalId, array $addPurchaseItemParams, array $publicParams, array $oldPurchaseItemInfo = []): LogicResult
    {
        if (empty($allocationHospitalId))
        {
            return self::Fail('验证调拨采购商品，缺少调拨源门店ID', 400);
        }
        if (empty($addPurchaseItemParams))
        {
            return self::Fail('验证调拨采购商品，缺少采购商品必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($hospitalId))
        {
            return self::Fail('验证调拨采购商品，缺少医院相关必选参数', 400);
        }

        // 自采白名单商品ID
        $allocationItemIds = [];
        array_walk($addPurchaseItemParams, function ($item) use (&$allocationItemIds) {
            if (!empty($item['itemInfo']['id']))
            {
                $allocationItemIds[] = $item['itemInfo']['id'];
            }
        });

        // 获取调拨源门店库存，验证剩余库存是否足够采购数量
        $allocationPublicParams                = $publicParams;
        $allocationPublicParams['_hospitalId'] = $allocationHospitalId;
        $getAllocationStockRes                 = StockItemShelfLogic::GetEffectiveQuantity($allocationItemIds, $allocationPublicParams);
        if ($getAllocationStockRes->isFail())
        {
            return $getAllocationStockRes;
        }

        $errorMsg = [];
        foreach ($addPurchaseItemParams as $curPurchaseItem)
        {
            $curStockInfo    = $getAllocationStockRes->getData($curPurchaseItem['itemId'], []);
            $curBulkRatio    = $curStockInfo['bulkRatio'] ?? 1;
            $curPackQuantity = $curStockInfo['packQuantity'] ?: 0;
            $curBulkQuantity = $curStockInfo['bulkQuantity'] ?: 0;
            if ($curPackQuantity <= 0 && $curBulkQuantity <= 0)
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，调拨源门店整装、散装库存不足';
                continue;
            }

            // 库存是否足够
            $getCheckSufficientRes = StockQuantityConversionHelper::checkQuantityIsSufficient($curPurchaseItem['packQuantity'],
                                                                                              $curPurchaseItem['bulkQuantity'],
                                                                                              $curPackQuantity,
                                                                                              $curBulkQuantity,
                                                                                              $curBulkRatio);
            if (empty($getCheckSufficientRes))
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，调拨源门店库存不足';
            }
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42007);
        }

        // 采购价格使用调拨源门店的加权成本价
        $getItemDailyPriceRes = StockItemDailyPriceLogic::GetItemNowDailyPrice($allocationItemIds, $allocationPublicParams);
        if ($getItemDailyPriceRes->isFail())
        {
            return $getItemDailyPriceRes;
        }

        $errorMsg = [];
        foreach ($addPurchaseItemParams as &$curPurchaseItem)
        {
            // 调拨商品最新加权价
            $curItemDailyPrice = $getItemDailyPriceRes->getData($curPurchaseItem['itemId'], []);
            $curPackPrice      = $curItemDailyPrice['packPrice'] ?? 0;
            $curBulkPrice      = $curItemDailyPrice['bulkPrice'] ?? 0;
            if (bccomp($curPackPrice, 0, 4) != 1 && bccomp($curBulkPrice, 0, 4) != 1)
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，调拨源门店加权价格有误，不能保存采购单';
                continue;
            }

            // 编辑时，如果存在旧的采购价格，则使用旧的价格
            $curUid = $curPurchaseItem['uid'] ?? '';
            if (!empty($oldPurchaseItemInfo[$curUid]))
            {
                if ($oldPurchaseItemInfo[$curUid]['pack_purchase_price'] > 0)
                {
                    $curPackPrice = $oldPurchaseItemInfo[$curUid]['pack_purchase_price'];
                }
                if ($oldPurchaseItemInfo[$curUid]['bulk_purchase_price'] > 0)
                {
                    $curBulkPrice = $oldPurchaseItemInfo[$curUid]['bulk_purchase_price'];
                }
            }

            // 整装、散装采购数量，对应加权价是否正确
            $curPackQuantity = $curPurchaseItem['packQuantity'] ?: 0;
            $curBulkQuantity = $curPurchaseItem['bulkQuantity'] ?: 0;
            if ($curPackQuantity > 0 && bccomp($curPackPrice, 0, 4) == - 1)
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，调拨源门店加权价格有误，整装采购价不可小于0';
                continue;
            }
            if ($curBulkQuantity > 0 && bccomp($curBulkPrice, 0, 4) == - 1)
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，调拨源门店加权价格有误，散装采购价不可小于0';
                continue;
            }

            // 计算采购总数量，转化散为准
            $curPurchaseTotalQuantity = 0;
            if ($curPurchaseItem['packQuantity'] > 0)
            {
                $curItemBulkRatio         = $curPurchaseItem['itemInfo']['bulk_ratio'] ?? 1;
                $curPurchaseTotalQuantity = numberMul([$curPurchaseItem['packQuantity'], $curItemBulkRatio]);
            }
            if ($curPurchaseItem['bulkQuantity'] > 0)
            {
                $curPurchaseTotalQuantity = numberAdd([$curPurchaseTotalQuantity, $curPurchaseItem['bulkQuantity']]);
            }

            // 本次调拨，整装、散装采购总价
            $curPackTotalPrice = numberMul([$curPackQuantity, $curPackPrice], 4);
            $curBulkTotalPrice = numberMul([$curBulkQuantity, $curBulkPrice], 4);
            $curTotalPrice     = numberAdd([$curPackTotalPrice, $curBulkTotalPrice], 4);

            // 计算本次散装平均价，入库时使用此价格。如果入库整装的话，当前散装平均价 * 整散比
            $curPurchaseItemAvgPrice = numberDiv([$curTotalPrice, $curPurchaseTotalQuantity], 4);
            if (bccomp($curPurchaseItemAvgPrice, 0, 4) == - 1)
            {
                $errorMsg[] = '名称【' . $curPurchaseItem['itemName'] . '】，散装平均价小于0，不能保存采购单';
                continue;
            }

            // 补全调拨缺少的价格信息
            $curPurchaseItem['totalPrice']        = $curTotalPrice;
            $curPurchaseItem['packPurchasePrice'] = $curPackQuantity > 0 ? $curPackPrice : 0;
            $curPurchaseItem['packTotalPrice']    = numberMul([$curPackQuantity, $curPackPrice], 4);
            $curPurchaseItem['bulkPurchasePrice'] = $curBulkPrice;
            $curPurchaseItem['bulkTotalPrice']    = $curBulkQuantity > 0 ? $curBulkPrice : 0;
            $curPurchaseItem['bulkAvgPrice']      = $curPurchaseItemAvgPrice;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 42007);
        }

        return self::Success($addPurchaseItemParams);
    }

    /**
     * 对比采购商品明细差异，构建更新数据和日志
     *
     * @param array $oldPurchaseItemInfo purchase_order_item中的一行数据
     * @param array $newPurchaseItemInfo 提交的采购商品数据
     *
     * @return array [$updateData, $logContent]
     */
    public static function ComparePurchaseItemChange(array $oldPurchaseItemInfo, array $newPurchaseItemInfo): array
    {
        $fieldMap = [
            'packPurchasePrice' => ['label' => '整装采购价', 'unit' => '元', 'db_field' => 'pack_purchase_price', 'type' => 'decimal'],
            'bulkPurchasePrice' => ['label' => '散装采购价', 'unit' => '元', 'db_field' => 'bulk_purchase_price', 'type' => 'decimal'],
            'packQuantity'      => ['label' => '整装采购数量', 'unit' => '个', 'db_field' => 'pack_quantity', 'type' => 'int'],
            'bulkQuantity'      => ['label' => '散装采购数量', 'unit' => '个', 'db_field' => 'bulk_quantity', 'type' => 'int'],
            'bulkAvgPrice'      => ['label' => '散装平均价', 'unit' => '元', 'db_field' => 'bulk_avg_price', 'type' => 'decimal'],
        ];

        $updateData = [];
        $logLines   = [];
        foreach ($fieldMap as $camelField => $meta)
        {
            $oldVal = $oldPurchaseItemInfo[$meta['db_field']] ?? null;
            $newVal = $newPurchaseItemInfo[$camelField] ?? null;

            $isDifferent = match ($meta['type'])
            {
                'decimal' => bccomp($oldVal, $newVal, 4) !== 0,
                'int' => (int) $oldVal !== (int) $newVal,
                default => (string) $oldVal !== (string) $newVal,
            };

            if ($isDifferent)
            {
                $updateData[$meta['db_field']] = $newVal;
                $logLines[]                    = "  {$meta['label']}由$oldVal{$meta['unit']}变为$newVal{$meta['unit']}";
            }
        }

        if (!empty($updateData))
        {
            $logContent = "修改商品:【{$newPurchaseItemInfo['itemInfo']['id']}】{$newPurchaseItemInfo['itemName']}" . implode(";", $logLines);

            return [$updateData, $logContent];
        }

        return [];
    }
}
