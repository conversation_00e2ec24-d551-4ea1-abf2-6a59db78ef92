<?php

namespace App\Support\Recipe;

use Arr;
use App\Support\Item\ItemHelper;
use App\Enums\RecipeEnum;
use App\Enums\ItemTypeEnum;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Enums\CaseSourceTypeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\PetLogic;
use App\Logics\V1\HospitalLogic;
use App\Models\ItemModel;
use App\Models\UsersModel;
use App\Models\ItemSuitModel;
use App\Models\ItemUsageModel;
use App\Models\ItemSaleTypeModel;
use App\Models\ItemSalePriceModel;
use App\Models\ItemSuitRelationModel;
use App\Models\RecipesAssistantModel;
use App\Models\ItemSupportPetCategoryModel;

/**
 * 处方助手
 * Class RecipeHelper
 * @package App\Support\Recipe
 */
class RecipeHelper extends Logic
{
    /**
     * 验证处方内商品基本信息（商品状态、开具数量、是否匹配当前宠物）
     *
     * @param array $caseInfo
     * @param array $paramsRecipeItems
     * @param array $publicParams
     *
     * @return LogicResult 返回分组后的结构（药品、化验、影像、处置、组合）、商品基本结构
     */
    public static function GetValidRecipeItems(array $caseInfo, array $paramsRecipeItems, array $publicParams): LogicResult
    {
        if (empty($caseInfo) || empty($paramsRecipeItems))
        {
            return self::Fail('caseInfo、recipeItems，缺少必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('orgId、brandId、hospitalId、缺少必选参数', 400);
        }

        // 获取病历关联的宠物信息
        $memberId  = $caseInfo['member_id'];
        $petId     = $caseInfo['pet_id'];
        $getPetRes = PetLogic::GetValidPetByIdOrUid($petId, memberId: $memberId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 宠物类别
        $petCategoryId = $getPetRes->getData('category_id', 0);

        // 验证参数
        $errorMsg            = [];
        $recipeItemInfos     = [];
        $recipeSuitItemInfos = [];
        foreach ($paramsRecipeItems as $rItem)
        {
            $curItemName   = $rItem['name'] ?? '';
            $curItemIsSuit = $rItem['isSuit'] ?? false;
            if (empty($rItem['itemUid']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少商品UID';
                continue;
            }
            if (!empty($rItem['isSuit']) && empty($rItem['suitItems']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少组合商品明细';
                continue;
            }
            if ($rItem['quantity'] <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，数量参数错误';
                continue;
            }

            // 开具单位，1:散；2:整。
            $curUnitType = $rItem['unitType'];
            if (!in_array($curUnitType, RecipeEnum::RECIPE_UNIT_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，单位类型参数错误';
                continue;
            }

            // 区分单品、组合商品
            if ($curItemIsSuit)
            {
                $recipeSuitItemInfos[] = $rItem;
            }
            else
            {
                $recipeItemInfos[] = $rItem;
            }
        }

        // 处方商品基本校验
        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 39010);
        }

        // 处方内无有效商品
        if (empty($recipeSuitItemInfos) && empty($recipeItemInfos))
        {
            return self::Fail('处方中无有效商品', 400);
        }

        // 处方中单品
        $recipeItemUids            = array_column($recipeItemInfos, 'itemUid');
        $getRecipeItemsBaseInfoRes = [];
        if (!empty($recipeItemUids))
        {
            $getRecipeItemsBaseInfoRes = ItemModel::getData(where  : ['org_id' => $orgId],
                                                            whereIn: ['uid' => $recipeItemUids],
                                                            keyBy  : 'uid');
            if (empty($getRecipeItemsBaseInfoRes))
            {
                return self::Fail('处方中单品商品不存在', 33000);
            }
        }

        // 处方中组合商品
        $recipeSuitItemUids            = array_column($recipeSuitItemInfos, 'itemUid');
        $getRecipeSuitItemsBaseInfoRes = [];
        if (!empty($recipeSuitItemUids))
        {
            $getRecipeSuitItemsBaseInfoRes = ItemSuitModel::getData(where  : ['org_id' => $orgId],
                                                                    whereIn: ['uid' => $recipeSuitItemUids],
                                                                    keyBy  : 'uid');

            if (empty($getRecipeSuitItemsBaseInfoRes))
            {
                return self::Fail('处方中组合商品不存在', 33000);
            }
        }

        // 处方中商品、组合都不存在
        if (empty($getRecipeItemsBaseInfoRes) && empty($getRecipeSuitItemsBaseInfoRes))
        {
            return self::Fail('处方中商品、组合不存在', 400);
        }

        // 处方内单品、组合商品ID
        $getRecipeItemIds     = array_column($getRecipeItemsBaseInfoRes, 'id');
        $getRecipeSuitItemIds = array_column($getRecipeSuitItemsBaseInfoRes, 'id');

        // 如果宠物类别存在，则获取处方的商品支持的宠物类别白名单。验证是否适用此宠物
        $getItemSupportPetCategoryRes     = [];
        $getSuitItemSupportPetCategoryRes = [];
        if ($petCategoryId > 0)
        {
            // 获取处方的商品支持的宠物类别白名单
            $getItemSupportPetCategoryRes = ItemSupportPetCategoryModel::getItemSupportPetCategory(ItemTypeEnum::Sku->value,
                                                                                                   $getRecipeItemIds);

            // 获取处方的组合商品支持的宠物类别白名单
            $getSuitItemSupportPetCategoryRes = ItemSupportPetCategoryModel::getItemSupportPetCategory(ItemTypeEnum::Spu->value,
                                                                                                       $getRecipeSuitItemIds);
        }

        // 获取医院信息，为了获取商品价格使用。
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        // 医院所属城市
        $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
        $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

        // 获取商品、组合价格
        $getItemsPriceRes     = [];
        $getSuitItemsPriceRes = [];
        if (!empty($getRecipeItemIds))
        {
            $getItemsPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Sku->value,
                                                                     $getRecipeItemIds,
                                                                     $orgId,
                                                                     $brandId,
                                                                     $hospitalProvinceId,
                                                                     $hospitalCityId,
                                                                     $hospitalId);
            if (empty($getItemsPriceRes))
            {
                return self::Fail('商品价格获取失败', 400);
            }
        }
        if (!empty($getRecipeSuitItemIds))
        {
            $getSuitItemsPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Spu->value,
                                                                         $getRecipeSuitItemIds,
                                                                         $orgId,
                                                                         $brandId,
                                                                         $hospitalProvinceId,
                                                                         $hospitalCityId,
                                                                         $hospitalId);
            if (empty($getSuitItemsPriceRes))
            {
                return self::Fail('组合价格获取失败', 400);
            }
        }
        if (empty($getItemsPriceRes) && empty($getSuitItemsPriceRes))
        {
            return self::Fail('商品、组合价格获取失败', 400);
        }

        $returnRecipeItems = [
            'drug'         => [],
            'test'         => [],
            'image'        => [],
            'nurses'       => [],
            'suit'         => [],
            'itemInfo'     => [],
            'suitItemInfo' => [],
        ];
        foreach ($paramsRecipeItems as $rItem)
        {
            $curItemUid    = $rItem['itemUid'] ?? '';
            $curItemName   = $rItem['name'] ?? '';
            $curItemIsSuit = $rItem['isSuit'] ?? false;

            if ($curItemIsSuit)
            {
                $curItemBaseInfo = $getRecipeSuitItemsBaseInfoRes[$curItemUid] ?? [];

                // 增加与单品级售卖类型一样字段，保持与item结构一致。
                $curItemBaseInfo['first_sale_type_id'] = $curItemBaseInfo['sale_type_id'];
                $curItemFirstSaleType                  = $curItemBaseInfo['sale_type_id'];
            }
            else
            {
                $curItemBaseInfo      = $getRecipeItemsBaseInfoRes[$curItemUid] ?? [];
                $curItemFirstSaleType = $curItemBaseInfo['first_sale_type_id'];
            }

            // 商品不存在
            $curItemId = $curItemBaseInfo['id'] ?? 0;
            if (empty($curItemBaseInfo) || empty($curItemId))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品不存在';
                continue;
            }

            // 商品非上线状态
            if ($curItemBaseInfo['status'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非上线状态，不能保存处方';
                continue;
            }

            // 商品是否处方可开具
            if (isset($curItemBaseInfo['is_recipe_allow']) && $curItemBaseInfo['is_recipe_allow'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非处方可开具，不能保存处方';
                continue;
            }

            // 商品一级类型是否可开具
            if (!in_array($curItemBaseInfo['first_sale_type_id'], ItemSaleTyeEnum::RECIPE_FIRST_SALE_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品一级类型非处方可开具，不能保存处方';
                continue;
            }

            // 商品是否适用当前宠物
            if ($petCategoryId > 0)
            {
                if ($curItemIsSuit)
                {
                    $curItemSupportPetCategoryIds = $getSuitItemSupportPetCategoryRes[$curItemId] ?? [];
                }
                else
                {
                    $curItemSupportPetCategoryIds = $getItemSupportPetCategoryRes[$curItemId] ?? [];
                }

                if (!empty($curItemSupportPetCategoryIds) && !in_array($petCategoryId, $curItemSupportPetCategoryIds))
                {
                    $errorMsg[] = '名称【' . $curItemName . '】，商品不适用于当前宠物，不能保存处方';
                    continue;
                }
            }

            // 获取商品价格
            if ($curItemIsSuit)
            {
                $curItemPriceInfo = $getSuitItemsPriceRes[$curItemId]['sale_price'] ?? [];
            }
            else
            {
                $curItemPriceInfo = $getItemsPriceRes[$curItemId]['sale_price'] ?? [];
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (empty($curItemPriceInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格不存在';
                continue;
            }

            // 商品价格
            $curUnitType  = $rItem['unitType'] ?? ItemUnitTypeEnum::UNIT_TYPE_BULK->value;
            $curItemPrice = $curItemPriceInfo[ItemUnitTypeEnum::UNIT_TYPE_PRICE_FIELD[$curUnitType]] ?? 0;
            if (bccomp($curItemPrice, '0', 2) == - 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格不能小于0';
                continue;
            }

            // 增加商品ID、价格
            $rItem['itemId']    = $curItemId;
            $rItem['itemPrice'] = $curItemPrice;

            // 处方内的商品分组，组合单独为一组。剩下的根据一级项目类型分组
            if ($curItemIsSuit)
            {
                $returnRecipeItems['suit'][] = $rItem;
                continue;
            }

            switch ($curItemFirstSaleType)
            {
                // 药品
                case ItemSaleTyeEnum::FirstDrug->value:
                    $returnRecipeItems['drug'][] = $rItem;
                    break;

                // 化验
                case ItemSaleTyeEnum::FirstTest->value:
                    $returnRecipeItems['test'][] = $rItem;
                    break;

                // 影像
                case ItemSaleTyeEnum::FirstImage->value:
                    $returnRecipeItems['image'][] = $rItem;
                    break;

                // 处置
                case ItemSaleTyeEnum::FirstNurses->value:
                    $returnRecipeItems['nurses'][] = $rItem;
                    break;
            }
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 39010);
        }

        $returnRecipeItems['itemInfo']     = array_column($getRecipeItemsBaseInfoRes, null, 'id');
        $returnRecipeItems['suitItemInfo'] = array_column($getRecipeSuitItemsBaseInfoRes, null, 'id');

        return self::Success($returnRecipeItems);
    }

    /**
     * 获取添加处方内药品
     *
     * @param array $caseInfo
     * @param array $addDrugItems
     * @param array $getItemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddRecipeDrugs(array $caseInfo, array $addDrugItems, array $getItemsInfo, array $publicParams): LogicResult
    {
        if (empty($caseInfo))
        {
            return self::Fail('保存药品处方，病历信息错误', 400);
        }
        if (empty($addDrugItems) || empty($getItemsInfo) || empty($publicParams))
        {
            return self::Fail('保存处方，药品参数错误', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));

        // 获取用药方式
        $getDrugUsageRes = ItemUsageModel::getData(where: ['status' => 1], keyBy: 'id');

        // TODO 获取药品在当前医院下库存

        $statusErrorMsg     = [];
        $addDrugTotalPrice  = 0;
        $returnAddDrugItems = [];
        foreach ($addDrugItems as $drugItem)
        {
            $drugItemId  = $drugItem['itemId'] ?? '';
            $curItemName = $drugItem['name'] ?? '';
            $curItemInfo = $getItemsInfo[$drugItemId] ?? [];
            if (empty($curItemInfo))
            {
                $statusErrorMsg[] = '药品【' . $curItemName . '】，信息不存在';
                continue;
            }

            // 当前商品库存管理非精确计量，则数量必须为整数。如果为精确计量，则可以为小数
            $curDrugQuantity = $drugItem['quantity'];
            if (empty($curItemInfo['is_precise_metering']))
            {
                // 非精确计量，数量必须为整数。
                if (!ctype_digit((string) $curDrugQuantity))
                {
                    $statusErrorMsg[] = '药品【' . $curItemName . '】，非精确计量商品，数量必须为整数';
                    continue;
                }
            }

            // 开具数量 * 单价，价格是否正确
            $curDrugTotalPrice = numberMul([$curDrugQuantity, $drugItem['itemPrice']]);
            if (bccomp($curDrugTotalPrice, $drugItem['totalPrice']) != 0)
            {
                $statusErrorMsg[] = '药品【' . $curItemName . '】，价格有变动，请刷新重试';
                continue;
            }

            // 药品开具单位，1:散；2:整。如果精确计量，不可以开具整装
            $curDrugUnitType = $drugItem['unitType'];
            if (!in_array($curDrugUnitType, RecipeEnum::RECIPE_UNIT_TYPE))
            {
                $statusErrorMsg[] = '药品【' . $curItemName . '】，单位类型参数错误';
                continue;
            }
            if (!empty($curItemInfo['is_precise_metering']) && $curDrugUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
            {
                $statusErrorMsg[] = '药品【' . $curItemName . '】，精确计量商品不可以开具整装';
                continue;
            }

            // 药品使用信息
            $curDrugOnceUse = (string) $drugItem['once'];
            $curDrugDays    = intval($drugItem['days']);
            if ($curDrugOnceUse <= 0)
            {
                $statusErrorMsg[] = '药品【' . $curItemName . '】，单次用量不能小于1';
                continue;
            }
            if ($curDrugDays <= 0)
            {
                $statusErrorMsg[] = '药品【' . $curItemName . '】，用药天数不能小于1';
                continue;
            }

            // 用药方式
            $curDrugUsageId = intval($drugItem['usage'] ?? 0);
            if (!empty($curDrugUsageId) && empty($getDrugUsageRes[$curDrugUsageId]))
            {
                $statusErrorMsg[] = '药品【' . $curItemName . '】，用药方式不存在';
                continue;
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($drugItem['itemPrice']))
            {
                $statusErrorMsg[] = '药品【' . $curItemName . '】，价格不存在';
                continue;
            }

            // TODO 验证出库库存是否大于实际库存？？

            // 用药频次
            $curDrugTimes = isset(RecipeEnum::RECIPE_DRUG_TIMES[$drugItem['times']]) ? $drugItem['times'] : 0;

            // 如果是编辑会存在uid，新增则为null
            if (!empty($drugItem['uid']))
            {
                $curUid = $drugItem['uid'];
            }
            else
            {
                $curUid = generateUUID();
            }

            $tmpDrugData = [
                'uid'             => $curUid,
                'hospital_id'     => $hospitalId,
                'member_id'       => $caseInfo['member_id'],
                'pet_id'          => $caseInfo['pet_id'],
                'item_suit_id'    => 0,
                'item_sale_type'  => $curItemInfo['first_sale_type_id'],
                'item_id'         => $drugItemId,
                'item_uid'        => $curItemInfo['uid'],
                'unit_type'       => $curDrugUnitType,
                'price'           => $drugItem['itemPrice'],
                'once_use'        => $curDrugOnceUse,
                'times'           => $curDrugTimes,
                'days'            => $curDrugDays,
                'quantity'        => $curDrugQuantity,
                'usage_id'        => $curDrugUsageId,
                'usage_name'      => $getDrugUsageRes[$curDrugUsageId]['name'] ?? '',
                'remark'          => trimWhitespace($drugItem['remark'] ?? ''),
                'use_unit'        => $curItemInfo['use_unit'] ?? '',
                'group'           => self::GetItemGroupInfo('group', $drugItem['group'] ?? ''),
                'suit_group'      => self::GetItemGroupInfo('suit_group', $drugItem['group'] ?? ''),
                'is_suit'         => 0,
                'suit_unique_uid' => ''
            ];

            $addDrugTotalPrice    = numberAdd([$addDrugTotalPrice, $curDrugTotalPrice]);
            $returnAddDrugItems[] = $tmpDrugData;
        }

        if (!empty($statusErrorMsg))
        {
            return self::Fail(implode("；\n", $statusErrorMsg), 39003);
        }

        return self::Success(['totalPrice' => $addDrugTotalPrice, 'drugItems' => $returnAddDrugItems]);
    }

    /**
     * 获取添加处方内化验
     *
     * @param array $caseInfo
     * @param array $addTestItems
     * @param array $getItemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddRecipeTests(array $caseInfo, array $addTestItems, array $getItemsInfo, array $publicParams): LogicResult
    {
        if (empty($caseInfo))
        {
            return self::Fail('化验项，病历信息错误', 400);
        }
        if (empty($addTestItems) || empty($getItemsInfo) || empty($publicParams))
        {
            return self::Fail('化验项，必须参数错误', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $doctorId   = intval(Arr::get($publicParams, '_userId', 0));

        $statusErrorMsg      = [];
        $addTestTotalPrice   = 0;
        $returnAddTestItems  = [];
        $returnTestTaskItems = [];
        foreach ($addTestItems as $testItem)
        {
            $testItemId  = $testItem['itemId'] ?? '';
            $curItemName = $testItem['name'] ?? '';
            $curItemInfo = $getItemsInfo[$testItemId] ?? [];
            if (empty($curItemInfo))
            {
                $statusErrorMsg[] = '化验项【' . $curItemName . '】，信息不存在';
                continue;
            }

            // 开具数量
            $curTestQuantity = intval($testItem['quantity']);

            // 化验开具单位，1:散；2:整。默认为1
            $curTestUnitType = $testItem['unitType'];
            if (!in_array($curTestUnitType, RecipeEnum::RECIPE_UNIT_TYPE))
            {
                $statusErrorMsg[] = '化验项【' . $curItemName . '】，单位类型参数错误';
                continue;
            }
            if ($curTestUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
            {
                $statusErrorMsg[] = '化验项【' . $curItemName . '】，不可以开具整装';
                continue;
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($testItem['itemPrice']))
            {
                $statusErrorMsg[] = '化验项【' . $curItemName . '】，价格不存在';
                continue;
            }

            // TODO 验证化验耗材绑定、以及库存？？

            // 如果是编辑会存在uid，新增则为null
            if (!empty($testItem['uid']))
            {
                $curUid = $testItem['uid'];
            }
            else
            {
                $curUid = generateUUID();
            }

            // 处方表化验信息
            $tmpTestItemData = [
                'uid'             => $curUid,
                'hospital_id'     => $hospitalId,
                'member_id'       => $caseInfo['member_id'],
                'pet_id'          => $caseInfo['pet_id'],
                'item_suit_id'    => 0,
                'item_sale_type'  => $curItemInfo['first_sale_type_id'],
                'item_id'         => $testItemId,
                'item_uid'        => $curItemInfo['uid'],
                'unit_type'       => $curTestUnitType,
                'price'           => $testItem['itemPrice'],
                'once_use'        => 0,
                'times'           => 0,
                'days'            => 0,
                'quantity'        => $curTestQuantity,
                'usage_id'        => 0,
                'usage_name'      => '',
                'remark'          => trimWhitespace($testItem['remark'] ?? ''),
                'use_unit'        => $curItemInfo['use_unit'] ?? '',
                'group'           => self::GetItemGroupInfo('group', $testItem['group'] ?? ''),
                'suit_group'      => self::GetItemGroupInfo('suit_group', $testItem['group'] ?? ''),
                'is_suit'         => 0,
                'suit_unique_uid' => '',
            ];

            // 化验任务表信息
            $tmpTestTaskData = [
                'recipe_item_uid'      => $curUid,
                'hospital_id'          => $hospitalId,
                'brand_id'             => $brandId,
                'org_id'               => $orgId,
                'case_id'              => $caseInfo['id'],
                'member_id'            => $caseInfo['member_id'],
                'pet_id'               => $caseInfo['pet_id'],
                'doctor_id'            => $doctorId,
                'source_type'          => $caseInfo['source_type'],
                'source_relation_id'   => $caseInfo['source_relation_id'],
                'item_id'              => $testItemId,
                'price'                => $testItem['itemPrice'],
                'material_template_id' => 0,
                'remark'               => trimWhitespace($testItem['remark'] ?? ''),
            ];

            // 化验任务中，根据化验数量拆开
            for ($i = 0; $i < $curTestQuantity; $i ++)
            {
                $tmpTestTaskData['test_code'] = generateBusinessCodeNumber(BusinessCodePrefixEnum::MZHY);
                $returnTestTaskItems[]        = $tmpTestTaskData;
            }

            $addTestTotalPrice    = numberAdd([$addTestTotalPrice, numberMul([$curTestQuantity, $testItem['itemPrice']])]);
            $returnAddTestItems[] = $tmpTestItemData;
        }

        if (!empty($statusErrorMsg))
        {
            return self::Fail(implode("；\n", $statusErrorMsg), 39004);
        }

        return self::Success([
                                 'totalPrice'    => $addTestTotalPrice,
                                 'testItems'     => $returnAddTestItems,
                                 'testTaskItems' => $returnTestTaskItems
                             ]);
    }

    /**
     * 获取添加处方内影像检查
     *
     * @param array $caseInfo
     * @param array $addImageItems
     * @param array $getItemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddRecipeImages(array $caseInfo, array $addImageItems, array $getItemsInfo, array $publicParams): LogicResult
    {
        if (empty($caseInfo))
        {
            return self::Fail('影像检查，病历信息错误', 400);
        }
        if (empty($addImageItems) || empty($getItemsInfo) || empty($publicParams))
        {
            return self::Fail('影像检查，必须参数错误', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $doctorId   = intval(Arr::get($publicParams, '_userId', 0));

        $statusErrorMsg       = [];
        $addImageTotalPrice   = 0;
        $returnAddImageItems  = [];
        $returnImageTaskItems = [];
        foreach ($addImageItems as $imageItem)
        {
            $imageItemId = $imageItem['itemId'] ?? '';
            $curItemName = $imageItem['name'] ?? '';
            $curItemInfo = $getItemsInfo[$imageItemId] ?? [];
            if (empty($curItemInfo))
            {
                $statusErrorMsg[] = '影像检查【' . $curItemName . '】，信息不存在';
                continue;
            }

            // 开具数量
            $curImageQuantity = intval($imageItem['quantity']);

            // 影像开具单位，1:散；2:整。默认为1
            $curImageUnitType = $imageItem['unitType'];
            if (!in_array($curImageUnitType, RecipeEnum::RECIPE_UNIT_TYPE))
            {
                $statusErrorMsg[] = '影像检查【' . $curItemName . '】，单位类型参数错误';
                continue;
            }
            if ($curImageUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
            {
                $statusErrorMsg[] = '影像检查【' . $curItemName . '】，不可以开具整装';
                continue;
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($imageItem['itemPrice']))
            {
                $statusErrorMsg[] = '影像检查【' . $curItemName . '】，价格不存在';
                continue;
            }

            // TODO 验证影像库存？？

            // 如果是编辑会存在uid，新增则为null
            if (!empty($imageItem['uid']))
            {
                $curUid = $imageItem['uid'];
            }
            else
            {
                $curUid = generateUUID();
            }

            // 处方表影像信息
            $tmpImageItemData = [
                'uid'             => $curUid,
                'hospital_id'     => $hospitalId,
                'member_id'       => $caseInfo['member_id'],
                'pet_id'          => $caseInfo['pet_id'],
                'item_suit_id'    => 0,
                'item_sale_type'  => $curItemInfo['first_sale_type_id'],
                'item_id'         => $imageItemId,
                'item_uid'        => $curItemInfo['uid'],
                'unit_type'       => $curImageUnitType,
                'price'           => $imageItem['itemPrice'],
                'once_use'        => 0,
                'times'           => 0,
                'days'            => 0,
                'quantity'        => $curImageQuantity,
                'usage_id'        => 0,
                'usage_name'      => '',
                'remark'          => trimWhitespace($imageItem['remark'] ?? ''),
                'use_unit'        => $curItemInfo['use_unit'] ?? '',
                'group'           => self::GetItemGroupInfo('group', $imageItem['group'] ?? ''),
                'suit_group'      => self::GetItemGroupInfo('suit_group', $imageItem['group'] ?? ''),
                'is_suit'         => 0,
                'suit_unique_uid' => '',
            ];

            // 影像任务表信息
            $tmpImageTaskData = [
                'recipe_item_uid'    => $curUid,
                'hospital_id'        => $hospitalId,
                'brand_id'           => $brandId,
                'org_id'             => $orgId,
                'case_id'            => $caseInfo['id'],
                'member_id'          => $caseInfo['member_id'],
                'pet_id'             => $caseInfo['pet_id'],
                'doctor_id'          => $doctorId,
                'source_type'        => $caseInfo['source_type'],
                'source_relation_id' => $caseInfo['source_relation_id'],
                'item_id'            => $imageItemId,
                'price'              => $imageItem['itemPrice'],
                'remark'             => trimWhitespace($imageItem['remark'] ?? ''),
            ];

            // 影像任务中，根据影像数量拆开
            for ($i = 0; $i < $curImageQuantity; $i ++)
            {
                $tmpImageTaskData['image_code'] = generateBusinessCodeNumber(BusinessCodePrefixEnum::MZYX);
                $returnImageTaskItems[]         = $tmpImageTaskData;
            }

            $addImageTotalPrice    = numberAdd([$addImageTotalPrice, numberMul([$curImageQuantity, $imageItem['itemPrice']])]);
            $returnAddImageItems[] = $tmpImageItemData;
        }

        if (!empty($statusErrorMsg))
        {
            return self::Fail(implode("；\n", $statusErrorMsg), 39005);
        }

        return self::Success([
                                 'totalPrice'     => $addImageTotalPrice,
                                 'imageItems'     => $returnAddImageItems,
                                 'imageTaskItems' => $returnImageTaskItems
                             ]);
    }

    /**
     * 获取添加处方内处置
     *
     * @param array $caseInfo
     * @param array $addNursesItems
     * @param array $getItemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddRecipeNurses(array $caseInfo, array $addNursesItems, array $getItemsInfo, array $publicParams): LogicResult
    {
        if (empty($caseInfo))
        {
            return self::Fail('处置，病历信息错误', 400);
        }
        if (empty($addNursesItems) || empty($getItemsInfo) || empty($publicParams))
        {
            return self::Fail('处置，必须参数错误', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $doctorId   = intval(Arr::get($publicParams, '_userId', 0));

        $statusErrorMsg        = [];
        $addNursesTotalPrice   = 0;
        $returnAddNursesItems  = [];
        $returnNursesTaskItems = [];
        foreach ($addNursesItems as $nursesItem)
        {
            $nursesItemId = $nursesItem['itemId'] ?? '';
            $curItemName  = $nursesItem['name'] ?? '';
            $curItemInfo  = $getItemsInfo[$nursesItemId] ?? [];
            if (empty($curItemInfo))
            {
                $statusErrorMsg[] = '处置【' . $curItemName . '】，信息不存在';
                continue;
            }

            // 开具数量
            $curNursesQuantity = intval($nursesItem['quantity']);

            // 化验开具单位，1:散；2:整。默认为1
            $curNursesUnitType = $nursesItem['unitType'];
            if (!in_array($curNursesUnitType, RecipeEnum::RECIPE_UNIT_TYPE))
            {
                $statusErrorMsg[] = '处置【' . $curItemName . '】，单位类型参数错误';
                continue;
            }
            if ($curNursesUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
            {
                $statusErrorMsg[] = '处置【' . $curItemName . '】，不可以开具整装';
                continue;
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($nursesItem['itemPrice']))
            {
                $statusErrorMsg[] = '处置【' . $curItemName . '】，价格不存在';
                continue;
            }

            // TODO 验证处置库存？？

            // 如果是编辑会存在uid，新增则为null
            if (!empty($nursesItem['uid']))
            {
                $curUid = $nursesItem['uid'];
            }
            else
            {
                $curUid = generateUUID();
            }

            // 处方表处置信息
            $tmpNursesItemData = [
                'uid'             => $curUid,
                'hospital_id'     => $hospitalId,
                'member_id'       => $caseInfo['member_id'],
                'pet_id'          => $caseInfo['pet_id'],
                'item_suit_id'    => 0,
                'item_sale_type'  => $curItemInfo['first_sale_type_id'],
                'item_id'         => $nursesItemId,
                'item_uid'        => $curItemInfo['uid'],
                'unit_type'       => $curNursesUnitType,
                'price'           => $nursesItem['itemPrice'],
                'once_use'        => 0,
                'times'           => 0,
                'days'            => 0,
                'quantity'        => $curNursesQuantity,
                'usage_id'        => 0,
                'usage_name'      => '',
                'remark'          => trimWhitespace($nursesItem['remark'] ?? ''),
                'use_unit'        => $curItemInfo['use_unit'] ?? '',
                'group'           => self::GetItemGroupInfo('group', $nursesItem['group'] ?? ''),
                'suit_group'      => self::GetItemGroupInfo('suit_group', $nursesItem['group'] ?? ''),
                'is_suit'         => 0,
                'suit_unique_uid' => '',
            ];

            // 处置任务表信息
            $tmpNursesTaskData = [
                'recipe_item_uid'    => $curUid,
                'hospital_id'        => $hospitalId,
                'brand_id'           => $brandId,
                'org_id'             => $orgId,
                'case_id'            => $caseInfo['id'],
                'member_id'          => $caseInfo['member_id'],
                'pet_id'             => $caseInfo['pet_id'],
                'doctor_id'          => $doctorId,
                'source_type'        => $caseInfo['source_type'],
                'source_relation_id' => $caseInfo['source_relation_id'],
                'item_id'            => $nursesItemId,
                'price'              => $nursesItem['itemPrice'],
                'remark'             => trimWhitespace($nursesItem['remark'] ?? ''),
            ];

            // 影像任务中，根据影像数量拆开
            for ($i = 0; $i < $curNursesQuantity; $i ++)
            {
                $tmpNursesTaskData['nurse_code'] = generateBusinessCodeNumber(BusinessCodePrefixEnum::MZCZ);
                $returnNursesTaskItems[]         = $tmpNursesTaskData;
            }

            $addNursesTotalPrice    = numberAdd([$addNursesTotalPrice, numberMul([$curNursesQuantity, $nursesItem['itemPrice']])]);
            $returnAddNursesItems[] = $tmpNursesItemData;
        }

        if (!empty($statusErrorMsg))
        {
            return self::Fail(implode("；\n", $statusErrorMsg), 39006);
        }

        return self::Success([
                                 'totalPrice'      => $addNursesTotalPrice,
                                 'nursesItems'     => $returnAddNursesItems,
                                 'nursesTaskItems' => $returnNursesTaskItems
                             ]);
    }

    /**
     * 获取添加处方内组合商品
     *
     * @param array $caseInfo
     * @param array $addSuitItems
     * @param array $getSuitItemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddRecipeSuits(array $caseInfo, array $addSuitItems, array $getSuitItemsInfo, array $publicParams): LogicResult
    {
        if (empty($caseInfo))
        {
            return self::Fail('组合，病历信息错误', 400);
        }
        if (empty($addSuitItems) || empty($getSuitItemsInfo) || empty($publicParams))
        {
            return self::Fail('组合，必选参数错误', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));

        // 获取组合明细
        $suitItemIds              = array_column($addSuitItems, 'itemId');
        $getSuitRelationDetailRes = ItemSuitRelationModel::getItemSuitRelationItemDetailInfo($orgId, $suitItemIds);
        if (empty($getSuitRelationDetailRes))
        {
            return self::Fail('组合，明细信息获取失败', 400);
        }

        $statusErrorMsg     = [];
        $addSuitTotalPrice  = 0;
        $returnAddSuitItems = [];
        foreach ($addSuitItems as $suitItem)
        {
            $curSuitItemId   = $suitItem['itemId'] ?? '';
            $curItemName     = $suitItem['name'] ?? '';
            $curSuitSubItems = $suitItem['suitItems'] ?? [];
            $curSuitItemInfo = $getSuitItemsInfo[$curSuitItemId] ?? [];
            if (empty($curSuitItemInfo))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，信息不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 化验开具单位，1:散；2:整。默认为1
            $curSuitUnitType = $suitItem['unitType'];
            if (!in_array($curSuitUnitType, RecipeEnum::RECIPE_UNIT_TYPE))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，单位类型参数错误';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }
            if ($curSuitUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，不可以开具整装';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 开具数量
            $curSuitQuantity = intval($suitItem['quantity']);
            if ($curSuitQuantity > 1)
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，请分开开具';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 是否标记组合
            $curIsSuit = boolval($suitItem['isSuit']) ?? false;
            if (empty($curIsSuit))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，组合标记错误';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($suitItem['itemPrice']))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，价格不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 组合明细
            $curSubRelationItems = $getSuitRelationDetailRes[$curSuitItemId] ?? [];
            if (empty($curSubRelationItems))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，关联明细不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 组合子项是否限
            if (count($curSuitSubItems) > count($curSubRelationItems))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，开具子项与关联子项不一致';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 组合项中包含的商品，药品、检测、影像、处置，以及这些子项的商品信息
            $subStatusError      = [];
            $curAddSuitDrugs     = [];
            $curAddSuitTests     = [];
            $curAddSuitImages    = [];
            $curAddSuitNurses    = [];
            $curSuitSubItemInfos = [];
            $diffCurSuitSubItems = $curSuitSubItems;
            foreach ($curSuitSubItems as $subItemKey => &$curSubItem)
            {
                foreach ($curSubRelationItems as $relationItemKey => $relationItemInfo)
                {
                    // 商品本身的uid
                    $curSuitItemName = $curSubItem['name'] ?? '';
                    $itemInfo        = $relationItemInfo['item_info'] ?? [];
                    $itemId          = $itemInfo['id'] ?? '';
                    $itemUid         = $itemInfo['uid'] ?? '';
                    if ($curSubItem['itemUid'] != $itemUid)
                    {
                        continue;
                    }

                    // 子项开具数量
                    $curSuitItemQuantity = intval($curSubItem['quantity']);
                    if ($curSuitItemQuantity <= 0)
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，数量参数错误';
                    }

                    // 子项开具单位
                    $curSuitItemUnitType = $curSubItem['unitType'];
                    if (!in_array($curSuitItemUnitType, RecipeEnum::RECIPE_UNIT_TYPE))
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，单位类型参数错误';
                        break;
                    }

                    // 子项目标记是否组合子项
                    $curSubIsSuitItem = boolval($curSubItem['isSuitItem']) ?? false;
                    if (empty($curSubIsSuitItem))
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，组合子项标记错误';
                        break;
                    }

                    // 子项目分组必须与组合分组一致
                    if ($curSubItem['group'] != $suitItem['group'])
                    {
                        $subStatusError[] = '组合【' . $curItemName . '】，子项【' . $curSuitItemName . '】，分组与主项不一致';
                        break;
                    }

                    // 记录子项商品附属信息
                    $curSubItem['itemId']    = $itemId;
                    $curSubItem['itemPrice'] = 0;

                    // 把组合内商品明细分组不同类型
                    $curItemInfo = $relationItemInfo['item_info'];
                    switch ($curItemInfo['first_sale_type_id'])
                    {
                        // 药品
                        case ItemSaleTyeEnum::FirstDrug->value:
                            $curAddSuitDrugs[] = $curSubItem;
                            break;

                        // 化验
                        case ItemSaleTyeEnum::FirstTest->value:
                            $curAddSuitTests[] = $curSubItem;
                            break;

                        // 影像
                        case ItemSaleTyeEnum::FirstImage->value:
                            $curAddSuitImages[] = $curSubItem;
                            break;

                        // 处置
                        case ItemSaleTyeEnum::FirstNurses->value:
                            $curAddSuitNurses[] = $curSubItem;
                            break;
                    }

                    // 当前组合明细下商品item的信息
                    $curSuitSubItemInfos[$itemId] = $relationItemInfo;

                    unset($curSubRelationItems[$relationItemKey]);
                    unset($diffCurSuitSubItems[$subItemKey]);
                    break;
                }
            }

            if (!empty($subStatusError))
            {
                $statusErrorMsg[] = implode("；\n", $subStatusError);

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 提交的子项与关联子项不一致
            if (!empty($diffCurSuitSubItems))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，提交子项与设定关联子项内容不一致';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 组合内子项对应的item信息，以商品ID为key
            $suitSubItemInfos = array_column(array_column($curSuitSubItemInfos, 'item_info'), null, 'id');

            // 组合内添加的商品药品
            $curAddDrugInfo = [];
            if (!empty($curAddSuitDrugs))
            {
                $curAddSuitDrugRes = self::GetAddRecipeDrugs($caseInfo,
                                                             $curAddSuitDrugs,
                                                             $suitSubItemInfos,
                                                             $publicParams);
                if ($curAddSuitDrugRes->isFail())
                {
                    $statusErrorMsg[] = '组合【' . $curItemName . '】，' . $curAddSuitDrugRes->getMessage();

                    return self::Fail(implode("；\n", $statusErrorMsg), 39007);
                }

                $curAddDrugInfo = $curAddSuitDrugRes->getData();
            }

            // 组合内添加的化验
            $curAddTestInfo = [];
            if (!empty($curAddSuitTests))
            {
                $curAddSuitTestRes = self::GetAddRecipeTests($caseInfo,
                                                             $curAddSuitTests,
                                                             $suitSubItemInfos,
                                                             $publicParams);
                if ($curAddSuitTestRes->isFail())
                {
                    $statusErrorMsg[] = '组合【' . $curItemName . '】，' . $curAddSuitTestRes->getMessage();

                    return self::Fail(implode("；\n", $statusErrorMsg), 39007);
                }

                $curAddTestInfo = $curAddSuitTestRes->getData();
            }

            // 组合内添加的影像
            $curAddImageInfo = [];
            if (!empty($curAddSuitImages))
            {
                $curAddSuitImageRes = self::GetAddRecipeImages($caseInfo,
                                                               $curAddSuitImages,
                                                               $suitSubItemInfos,
                                                               $publicParams);
                if ($curAddSuitImageRes->isFail())
                {
                    $statusErrorMsg[] = '组合【' . $curItemName . '】，' . $curAddSuitImageRes->getMessage();

                    return self::Fail(implode("；\n", $statusErrorMsg), 39007);
                }

                $curAddImageInfo = $curAddSuitImageRes->getData();
            }

            // 组合内添加的处置
            $curAddNurseInfo = [];
            if (!empty($curAddSuitNurses))
            {
                $curAddSuitNursesRes = self::GetAddRecipeNurses($caseInfo,
                                                                $curAddSuitNurses,
                                                                $suitSubItemInfos,
                                                                $publicParams);
                if ($curAddSuitNursesRes->isFail())
                {
                    $statusErrorMsg[] = '组合【' . $curItemName . '】，' . $curAddSuitNursesRes->getMessage();

                    return self::Fail(implode("；\n", $statusErrorMsg), 39007);
                }

                $curAddNurseInfo = $curAddSuitNursesRes->getData();
            }

            if (!empty($suitItem['uid']) && !empty($suitItem['suitUniqueId']))
            {
                $curUid           = $suitItem['uid'];
                $curSuitUniqueUid = $suitItem['suitUniqueId'];
            }
            else
            {
                $curUid           = generateUUID();
                $curSuitUniqueUid = generateUUID();
            }

            // 组合主项
            $tmpSuitItemData = [
                'uid'             => $curUid,
                'hospital_id'     => $hospitalId,
                'member_id'       => $caseInfo['member_id'],
                'pet_id'          => $caseInfo['pet_id'],
                'item_suit_id'    => $curSuitItemId,
                'item_sale_type'  => $curSuitItemInfo['sale_type_id'],
                'item_id'         => 0,
                'item_uid'        => $curSuitItemInfo['uid'],
                'unit_type'       => $curSuitUnitType,
                'price'           => $suitItem['itemPrice'],
                'once_use'        => 0,
                'times'           => 0,
                'days'            => 0,
                'quantity'        => $curSuitQuantity,
                'usage_id'        => 0,
                'usage_name'      => '',
                'remark'          => trimWhitespace($suitItem['remark'] ?? ''),
                'use_unit'        => '',
                'group'           => self::GetItemGroupInfo('group', $suitItem['group'] ?? ''),
                'suit_group'      => self::GetItemGroupInfo('suit_group', $suitItem['group'] ?? ''),
                'is_suit'         => 1,
                'suit_unique_uid' => $curSuitUniqueUid,
            ];

            // 组合子项，按照组合内子项的顺序来组合
            $tmpSuitSubItemData = [];
            foreach ($curSuitSubItems as $subItemKey => $sortSubItem)
            {
                // 匹配组合内药品
                if (!empty($curAddDrugInfo['drugItems']))
                {
                    foreach ($curAddDrugInfo['drugItems'] as $drugKey => $drugItem)
                    {
                        if ($sortSubItem['itemId'] != $drugItem['item_id'] || ($sortSubItem['type']['id'] ?? 0) != $drugItem['item_sale_type'])
                        {
                            continue;
                        }

                        $drugItem['item_suit_id']    = $curSuitItemId;
                        $drugItem['suit_unique_uid'] = $curSuitUniqueUid;
                        $tmpSuitSubItemData[]        = $drugItem;

                        unset($curAddDrugInfo['drugItems'][$drugKey], $curSuitSubItems[$subItemKey]);
                        break;
                    }
                }

                // 匹配组合内化验
                if (!empty($curAddTestInfo['testItems']))
                {
                    foreach ($curAddTestInfo['testItems'] as $testKey => $testItem)
                    {
                        if ($sortSubItem['itemId'] != $testItem['item_id'] || ($sortSubItem['type']['id'] ?? 0) != $testItem['item_sale_type'])
                        {
                            continue;
                        }

                        $testItem['item_suit_id']    = $curSuitItemId;
                        $testItem['suit_unique_uid'] = $curSuitUniqueUid;
                        $tmpSuitSubItemData[]        = $testItem;

                        unset($curAddTestInfo['testItems'][$testKey], $curSuitSubItems[$subItemKey]);
                        break;
                    }
                }

                // 匹配组合内影像
                if (!empty($curAddImageInfo['imageItems']))
                {
                    foreach ($curAddImageInfo['imageItems'] as $imageKey => $imageItem)
                    {
                        if ($sortSubItem['itemId'] != $imageItem['item_id'] || ($sortSubItem['type']['id'] ?? 0) != $imageItem['item_sale_type'])
                        {
                            continue;
                        }

                        $imageItem['item_suit_id']    = $curSuitItemId;
                        $imageItem['suit_unique_uid'] = $curSuitUniqueUid;
                        $tmpSuitSubItemData[]         = $imageItem;

                        unset($curAddImageInfo['imageItems'][$imageKey], $curSuitSubItems[$subItemKey]);
                        break;
                    }
                }

                // 匹配组合内处置
                if (!empty($curAddNurseInfo['nursesItems']))
                {
                    foreach ($curAddNurseInfo['nursesItems'] as $nursesKey => $nursesItem)
                    {
                        if ($sortSubItem['itemId'] != $nursesItem['item_id'] || ($sortSubItem['type']['id'] ?? 0) != $nursesItem['item_sale_type'])
                        {
                            continue;
                        }

                        $nursesItem['item_suit_id']    = $curSuitItemId;
                        $nursesItem['suit_unique_uid'] = $curSuitUniqueUid;
                        $tmpSuitSubItemData[]          = $nursesItem;

                        unset($curAddNurseInfo['nursesItems'][$nursesKey], $curSuitSubItems[$subItemKey]);
                        break;
                    }
                }
            }

            if (!empty($curSuitSubItems))
            {
                $statusErrorMsg[] = '组合【' . $curItemName . '】，子项内容有增项未匹配';

                return self::Fail(implode("；\n", $statusErrorMsg), 39007);
            }

            // 组合子项
            $tmpSuitItemData['suit_items'] = $tmpSuitSubItemData;

            $addSuitTotalPrice    = numberAdd([$addSuitTotalPrice, numberMul([$curSuitQuantity, $suitItem['itemPrice']])]);
            $returnAddSuitItems[] = [
                'suitItems'       => $tmpSuitItemData,
                'testTaskItems'   => $curAddTestInfo['testTaskItems'] ?? [],
                'imageTaskItems'  => $curAddImageInfo['imageTaskItems'] ?? [],
                'nursesTaskItems' => $curAddNurseInfo['nursesTaskItems'] ?? [],
            ];
        }

        return self::Success(['totalPrice' => $addSuitTotalPrice, 'suitItems' => $returnAddSuitItems]);
    }

    /**
     * 格式化处方信息
     *
     * @param string $caseCode
     * @param array  $recipeInfo
     * @param array  $publicParams
     *
     * @return LogicResult
     */
    public static function FormatRecipeStructure(string $caseCode, array $recipeInfo, array $publicParams): LogicResult
    {
        if (empty($recipeInfo))
        {
            return self::Fail('处方信息错误', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('公共参数错误', 400);
        }

        // 处方关联的医院
        $hospitalIds    = array_unique(array_column($recipeInfo, 'hospital_id'));
        $getHospitalRes = HospitalLogic::GetHospitalsBaseInfo($hospitalIds, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        $getHospitalRes = $getHospitalRes->getData('hospitals', []);
        $getHospitalRes = array_column($getHospitalRes, null, 'id');

        // 处方关联的医生
        $doctorIds    = array_unique(array_column($recipeInfo, 'doctor_id'));
        $getDoctorRes = UsersModel::getUserByIds($doctorIds);

        // 处方跟诊
        $recipeIds             = array_unique(array_column($recipeInfo, 'id'));
        $getRecipeAssistantRes = RecipesAssistantModel::getRecipeAssistant($recipeIds);

        $returnRecipeTotalPrice = 0;
        $returnRecipeList       = [];
        foreach ($recipeInfo as $key => $curRecipeInfo)
        {
            // 处方关医院信息
            $tmpHospitalInfo = [];
            if (!empty($getHospitalRes[$curRecipeInfo['hospital_id']]))
            {
                $tmpHospitalInfo = [
                    'uid'  => $getHospitalRes[$curRecipeInfo['hospital_id']]['uid'],
                    'name' => $getHospitalRes[$curRecipeInfo['hospital_id']]['aliasName'],
                ];
            }

            // 处方医生信息
            $tmpDoctorInfo = [];
            if (!empty($getDoctorRes[$curRecipeInfo['doctor_id']]))
            {
                $tmpDoctorInfo = [
                    'uid'  => $getDoctorRes[$curRecipeInfo['doctor_id']]['uid'],
                    'name' => $getDoctorRes[$curRecipeInfo['doctor_id']]['name'],
                ];
            }

            // 处方来源
            $tmpSourceInfo = [
                'id'   => $curRecipeInfo['source_type'],
                'name' => CaseSourceTypeEnum::getDescription($curRecipeInfo['source_type']),
            ];

            // 处方跟诊信息
            $tmpAssistantInfo = [];
            foreach ($getRecipeAssistantRes as $assistantInfo)
            {
                if ($assistantInfo['recipe_id'] != $curRecipeInfo['id'])
                {
                    continue;
                }

                $tmpAssistantInfo[] = [
                    'uid'   => $assistantInfo['user_uid'],
                    'name'  => $assistantInfo['user_name'],
                    'level' => $assistantInfo['assistant_level'],
                ];
            }

            $tmpRecipeData = [
                'caseCode'     => $caseCode,
                'recipeCode'   => $curRecipeInfo['recipe_code'],
                'name'         => '处方' . ($key + 1),
                'hospital'     => $tmpHospitalInfo,
                'doctor'       => $tmpDoctorInfo,
                'source'       => $tmpSourceInfo,
                'createTime'   => formatDisplayDateTime($curRecipeInfo['created_at']),
                'price'        => formatDisplayNumber($curRecipeInfo['price']),
                'payStatus'    => [
                    'status' => $curRecipeInfo['is_paid'],
                    'name'   => RecipeEnum::RECIPE_IS_PAID[$curRecipeInfo['is_paid']] ?? '',
                ],
                'assistant'    => $tmpAssistantInfo,
                'assistStatus' => $curRecipeInfo['is_follow'],
            ];

            $returnRecipeTotalPrice = numberAdd([$returnRecipeTotalPrice, $curRecipeInfo['price']]);
            $returnRecipeList[]     = $tmpRecipeData;
        }

        $returnSummary = [
            'total'      => count($recipeInfo),
            'totalPrice' => formatDisplayNumber($returnRecipeTotalPrice),
        ];

        return self::Success(['summary' => $returnSummary, 'data' => $returnRecipeList]);
    }

    /**
     * 格式化处方内商品明细
     *
     * @param array $recipeItems
     * @param array $publicParams
     * @param bool  $withItemPrice
     * @param bool  $withItemStock
     * @param bool  $withGroupItem 是否分组处方内商品
     *
     * @return LogicResult
     */
    public static function FormatRecipeItemStructure(array $recipeItems, array $publicParams, bool $withItemPrice = false, bool $withItemStock = false, bool $withGroupItem = false): LogicResult
    {
        if (empty($recipeItems))
        {
            return self::Fail('格式化处方内明细不存在', 39008);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('医院缺少必选参数', 400);
        }

        // 处方内商品ID、组合ID
        $itemIds     = [];
        $itemSuitIds = [];
        array_walk($recipeItems, function ($item) use (&$itemIds, &$itemSuitIds) {
            if (empty($item['is_suit']) && $item['item_id'] > 0)
            {
                $itemIds[$item['item_id']] = $item['item_id'];
            }
            else
            {
                $itemSuitIds[$item['item_suit_id']] = $item['item_suit_id'];
            }
        });

        // 获取处方内商品
        $getItemInfoRes     = [];
        $getSuitItemInfoRes = [];
        if (!empty($itemIds))
        {
            $getItemInfoRes = ItemModel::getManyByIds($itemIds);
            $getItemInfoRes = $getItemInfoRes->isNotEmpty() ? $getItemInfoRes->keyBy('id')
                                                                             ->toArray() : [];
        }
        if (!empty($itemSuitIds))
        {
            $getSuitItemInfoRes = ItemSuitModel::getManyByIds($itemSuitIds);
            $getSuitItemInfoRes = $getSuitItemInfoRes->isNotEmpty() ? $getSuitItemInfoRes->keyBy('id')
                                                                                         ->toArray() : [];
        }
        if (empty($getItemInfoRes) && empty($getSuitItemInfoRes))
        {
            return self::Fail('处方内商品信息不存在', 39008);
        }

        // 获取商品项目类型
        $getItemSaleTypeRes = ItemSaleTypeModel::getAllSaleType([0, 1]);
        $getItemSaleTypeRes = $getItemSaleTypeRes->isNotEmpty() ? $getItemSaleTypeRes->keyBy('id')
                                                                                     ->toArray() : [];

        // 获取商品价格
        $getItemPriceRes     = [];
        $getSuitItemPriceRes = [];
        if ($withItemPrice)
        {
            // 获取医院信息
            $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
            if ($getHospitalRes->isFail())
            {
                return $getHospitalRes;
            }

            // 医院所属城市
            $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
            $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

            if (!empty($itemIds))
            {
                $getItemPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Sku->value,
                                                                        $itemIds,
                                                                        $orgId,
                                                                        $brandId,
                                                                        $hospitalProvinceId,
                                                                        $hospitalCityId,
                                                                        $hospitalId);
            }
            if (!empty($itemSuitIds))
            {
                $getSuitItemPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Spu->value,
                                                                            $itemSuitIds,
                                                                            $orgId,
                                                                            $brandId,
                                                                            $hospitalProvinceId,
                                                                            $hospitalCityId,
                                                                            $hospitalId);
            }
        }

        // TODO 获取商品库存

        // 预处理组合项子项映射（suit_unique_uid => [[子项列表], [子项列表]]）
        $suitChildrenMap = [];
        foreach ($recipeItems as $item)
        {
            if (empty($item['is_suit']) && !empty($item['suit_unique_uid']))
            {
                $suitChildrenMap[$item['suit_unique_uid']][] = $item;
            }
        }

        // 处方内商品分组
        $recipeItemGroups = [];

        // 当前方法内定义递归构建逻辑
        $buildItem = function ($recipeItem) use (&$buildItem, &$recipeItemGroups, $getItemInfoRes, $getSuitItemInfoRes, $getItemPriceRes, $getSuitItemPriceRes, $getItemSaleTypeRes, $suitChildrenMap, $withGroupItem) {
            // 商品信息、价格
            if (empty($recipeItem['is_suit']) && !empty($getItemInfoRes[$recipeItem['item_id']]))
            {
                $curItemInfo      = $getItemInfoRes[$recipeItem['item_id']];
                $curItemPriceInfo = $getItemPriceRes[$recipeItem['item_id']]['sale_price'] ?? [];
                $saleTypeFiledKey = 'first_sale_type_id';
            }
            else
            {
                $curItemInfo      = $getSuitItemInfoRes[$recipeItem['item_suit_id']];
                $curItemPriceInfo = $getSuitItemPriceRes[$recipeItem['item_suit_id']]['sale_price'] ?? [];
                $saleTypeFiledKey = 'sale_type_id';
            }

            // 商品出库单位规格
            $curItemUseUnit   = $curItemInfo['use_unit'] ?? '';
            $curItemUseRatio  = $curItemInfo['use_ratio'] ?? 0;
            $curItemBulkUnit  = $curItemInfo['bulk_unit'] ?? '';
            $curItemBulkRatio = $curItemInfo['bulk_ratio'] ?? 0;
            $curItemPackUnit  = $curItemInfo['pack_unit'] ?? '';

            // 如果没有整装单位、散装单位使，用计量单位
            if (empty($curItemBulkUnit))
            {
                $curItemBulkUnit = $curItemUseUnit;
            }
            if (empty($curItemPackUnit))
            {
                $curItemPackUnit = $curItemUseUnit;
            }

            // 商品出库单位，1ml/ml、1次、1瓶。（计量比 + 计量单位 + 散装单位 ）
            if ($curItemInfo[$saleTypeFiledKey] == ItemSaleTyeEnum::FirstDrug->value)
            {
                // 处方内使用单位-散装
                if ($recipeItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value)
                {
                    $curItemSpec = formatDisplayNumber($curItemUseRatio) . $curItemUseUnit . '/' . $curItemBulkUnit;
                }
                // 处方内使用单位-整装
                else
                {
                    $curItemSpec = formatDisplayNumber($curItemBulkRatio) . $curItemBulkUnit . '/' . $curItemPackUnit;
                }
            }
            else
            {
                $curItemSpec = 1 . $curItemUseUnit;
            }

            // 商品类型
            $curItemTypeInfo = [
                'id'   => $curItemInfo[$saleTypeFiledKey] ?? 0,
                'name' => $getItemSaleTypeRes[$curItemInfo[$saleTypeFiledKey]]['alias_name'] ?? '',
            ];

            // 处方内用法
            $curItemUsageInfo = [
                'id'   => $recipeItem['usage_id'],
                'name' => $recipeItem['usage_name'],
            ];

            $tmpItemData = [
                'uid'          => $recipeItem['uid'] ?? '',
                'itemUid'      => $curItemInfo['uid'] ?? '',
                'type'         => $curItemTypeInfo,
                'isSuit'       => $recipeItem['is_suit'],
                'suitUniqueId' => $recipeItem['suit_unique_uid'],
                'group'        => self::FormatGroupDisplay($recipeItem['group'], $recipeItem['suit_group']),
                'name'         => ItemHelper::ItemDisplayName($curItemInfo),
                'spec'         => $curItemSpec,
                'unit'         => $recipeItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value ? $curItemBulkUnit : $curItemPackUnit,
                'unitType'     => $recipeItem['unit_type'],
                'once'         => $recipeItem['once_use'],
                'times'        => $recipeItem['times'],
                'days'         => $recipeItem['days'],
                'quantity'     => formatDisplayNumber($recipeItem['quantity']),
                'usage'        => $curItemUsageInfo,
                'price'        => isset($recipeItem['price']) ? formatDisplayNumber($recipeItem['price']) : 0,
                'totalPrice'   => isset($recipeItem['price']) ? formatDisplayNumber(numberMul([$recipeItem['price'], $recipeItem['quantity']])) : 0,
                'remark'       => $recipeItem['remark'],
                'itemInfo'     => [
                    'uid'               => $curItemInfo['uid'] ?? '',
                    'type'              => $curItemTypeInfo,
                    'name'              => ItemHelper::ItemDisplayName($curItemInfo),
                    'useUnit'           => $curItemUseUnit,
                    'useRatio'          => $curItemUseRatio,
                    'spec'              => $curItemSpec,
                    'packUnit'          => $curItemPackUnit,
                    'packPrice'         => formatDisplayNumber($curItemPriceInfo['pack_sale_price'] ?? 0),
                    'bulkUnit'          => $curItemBulkUnit,
                    'bulkPrice'         => formatDisplayNumber($curItemPriceInfo['bulk_sale_price'] ?? 0),
                    'bulkRatio'         => $curItemBulkRatio,
                    'stock'             => [],
                    'isPreciseMetering' => $curItemInfo['is_precise_metering'] ?? false,
                    'isPackSaleAllow'   => $curItemInfo['is_pack_sale_allow'] ?? false,
                ],
                'isPrintItems' => null,
                'suitItems'    => [],
            ];

            // 如果是组合，返回是否打印子项
            if (!empty($recipeItem['is_suit']))
            {
                $tmpItemData['isPrintItems'] = $curItemInfo['is_print_items'];
            }

            // 处方内商品按照商品类型分组
            if ($withGroupItem)
            {
                // 分组内商品信息
                $tmpGroupItemData = $tmpItemData;

                $groupMeta = self::GetRecipeItemGroupKey($curItemInfo);
                $groupKey  = $groupMeta['group'] ?? 'unknown';
                $showName  = $groupMeta['showName'] ?? '处方笺';
                $orderBy   = $groupMeta['orderBy'] ?? 0;
                if (!isset($recipeItemGroups[$groupKey]))
                {
                    $recipeItemGroups[$groupKey] = [
                        'name'       => $showName,
                        'orderBy'    => $orderBy,
                        'totalPrice' => 0,
                        'items'      => [],
                    ];
                }

                // 扁平化数据中，如果是组合子项，并且支持打印子项的话，需要设置支持打印
                if (empty($recipeItem['is_suit']) && !empty($recipeItem['item_suit_id']))
                {
                    $tmpGroupItemData['isPrintItems'] = $getSuitItemInfoRes[$recipeItem['item_suit_id']]['is_print_items'];
                }

                $recipeItemGroups[$groupKey]['totalPrice'] = numberAdd([$recipeItemGroups[$groupKey]['totalPrice'], $tmpGroupItemData['price']]);
                $recipeItemGroups[$groupKey]['items'][]    = $tmpGroupItemData;
            }

            // 如果是组合，递归构建子项
            if (!empty($recipeItem['is_suit']) && !empty($recipeItem['suit_unique_uid']))
            {
                foreach ($suitChildrenMap[$recipeItem['suit_unique_uid']] ?? [] as $childItem)
                {
                    $tmpItemData['suitItems'][] = $buildItem($childItem); // 递归构建
                }
            }

            return $tmpItemData;
        };

        // 执行主构建逻辑（排除掉子项）
        $returnItemData = [];
        foreach ($recipeItems as $item)
        {
            // 处方内组合子项过滤，由主项递归构建
            if (empty($item['is_suit']) && !empty($item['suit_unique_uid']))
            {
                continue;
            }

            $returnItemData[] = $buildItem($item);
        }

        // 如果存在分组，对分组进行排序
        $returnItemGroupData = [];
        if (!empty($recipeItemGroups))
        {
            $returnItemGroupData = array_values($recipeItemGroups);
            usort($returnItemGroupData, fn($a, $b) => $a['orderBy'] <=> $b['orderBy']);

            $returnItemGroupData = array_map(function ($group) {
                unset($group['orderBy']);

                return $group;
            }, $returnItemGroupData);
        }

        return self::Success(['itemList' => $returnItemData, 'itemGroupList' => $returnItemGroupData]);
    }

    /**
     * 获取商品分组信息
     *
     * @param string $fieldKey
     * @param string $group
     *
     * @return int
     */
    private static function GetItemGroupInfo(string $fieldKey, string $group): int
    {
        // 判断是否为组合分组（如 G1 或 G-1）
        if (preg_match('/^G-?(\d+)$/i', $group, $matches))
        {
            return $fieldKey === 'suit_group' ? (int) $matches[1] : 0;
        }

        // 普通分组
        return $fieldKey === 'group' ? (int) $group : 0;
    }

    /**
     * 格式化分组展示
     *
     * @param int $group     单品分组，1、2
     * @param int $suitGroup 组合分组，G1、G2
     *
     * @return string|null
     */
    private static function FormatGroupDisplay(int $group = 0, int $suitGroup = 0): string|null
    {
        if (empty($group) && empty($suitGroup))
        {
            return null;
        }

        if ($suitGroup > 0)
        {
            return 'G' . $suitGroup;
        }

        return (string) $group;
    }

    /**
     * 获取处方内商品符合分组的条件
     *
     * @param array $itemInfo
     *
     * @return array
     */
    private static function GetRecipeItemGroupKey(array $itemInfo): array
    {
        // 兜底分组项
        $defaultGroup = last(self::RecipeItemGroupCondition());
        if (empty($itemInfo))
        {
            return $defaultGroup;
        }

        foreach (self::RecipeItemGroupCondition() as $rule)
        {
            $matched = true;
            foreach ($rule['conditions'] as $key => $val)
            {
                if (!isset($itemInfo[$key]) || (string) $itemInfo[$key] !== (string) $val)
                {
                    $matched = false;
                    break;
                }
            }
            if ($matched)
            {
                return $rule;
            }
        }

        return $defaultGroup;
    }

    /**
     * 处方内商品分组条件
     *
     * @return array[]
     */
    private static function RecipeItemGroupCondition(): array
    {
        return [
            // 01. 药品-普药-批号药品
            [
                'showName'   => '处方笺',
                'remark'     => '普药_批号药品',
                'group'      => '普药_批号药品',
                'groupKey'   => 'prescription_general_license',
                'orderBy'    => 1,
                'conditions' => [
                    'first_sale_type_id' => 1,
                    'drug_type_id'       => 1,
                    'is_licensed'        => 1,
                ],
            ],

            // 02. 药品-普药-非批号药品
            [
                'showName'   => '处方笺',
                'group'      => '普药_非批号药品',
                'groupKey'   => 'prescription_general_unlicensed',
                'orderBy'    => 2,
                'conditions' => [
                    'first_sale_type_id' => 1,
                    'drug_type_id'       => 1,
                    'is_licensed'        => 0,
                ],
            ],

            // 03. 药品-中药
            [
                'showName'   => '处方笺',
                'group'      => '中药',
                'groupKey'   => 'prescription_chinese',
                'orderBy'    => 3,
                'conditions' => [
                    'first_sale_type_id' => 1,
                    'drug_type_id'       => 2,
                ],
            ],

            // 04. 药品-毒麻药
            [
                'showName'   => '处方笺',
                'group'      => '毒麻药',
                'groupKey'   => 'prescription_controlled',
                'orderBy'    => 4,
                'conditions' => [
                    'first_sale_type_id' => 1,
                    'drug_type_id'       => 3,
                ],
            ],

            // 05. 药品-保健品
            [
                'showName'   => '处方笺',
                'group'      => '保健品',
                'groupKey'   => 'prescription_healthcare',
                'orderBy'    => 5,
                'conditions' => [
                    'first_sale_type_id' => 1,
                    'drug_type_id'       => 4,
                ],
            ],

            // 06. 药品-处方粮
            [
                'showName'   => '处方笺',
                'group'      => '处方粮',
                'groupKey'   => 'prescription_food',
                'orderBy'    => 6,
                'conditions' => [
                    'first_sale_type_id' => 1,
                    'drug_type_id'       => 5,
                ],
            ],

            // 07. 处置类
            [
                'showName'   => '处置单',
                'group'      => '处置类',
                'groupKey'   => 'treatment',
                'orderBy'    => 7,
                'conditions' => [
                    'first_sale_type_id' => 4,
                ],
            ],

            // 08. 化验类
            [
                'showName'   => '化验单',
                'group'      => '化验类',
                'groupKey'   => 'laboratory',
                'orderBy'    => 8,
                'conditions' => [
                    'first_sale_type_id' => 2,
                ],
            ],

            // 09. 影像类
            [
                'showName'   => '处置单',
                'group'      => '影像类',
                'groupKey'   => 'imaging',
                'orderBy'    => 9,
                'conditions' => [
                    'first_sale_type_id' => 3,
                ],
            ],

            // 10. 耗材类
            [
                'showName'   => '处置单',
                'group'      => '耗材类',
                'groupKey'   => 'material',
                'orderBy'    => 10,
                'conditions' => [
                    'is_receive_allow' => 1,
                ],
            ],

            // 11. 其他（组合主项，未知分组）
            [
                'showName'   => '处方笺',
                'group'      => '其他',
                'groupKey'   => 'other',
                'orderBy'    => 99,
                'conditions' => [],
            ]
        ];
    }
}
