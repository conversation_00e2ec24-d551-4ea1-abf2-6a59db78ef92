<?php

namespace App\Support\Concurrent;

use Closure;
use Throwable;
use Spatie\Fork\Fork;

/**
 * 并发任务
 * Class ConcurrentTask
 * @package App\Support\Concurrent
 */
class ConcurrentTask
{
    protected array $tasks = [];
    protected array $results = [];

    public static function new(): self
    {
        return new self();
    }

    public function addTask(string $label, Closure $task): static
    {
        $this->tasks[$label] = $task;

        return $this;
    }

    /**
     * @return array<string, ConcurrentResult>
     */
    public function run(): array
    {
        $closures = [];

        foreach ($this->tasks as $label => $task)
        {
            $closures[$label] = function () use ($task, $label) {
                try
                {
                    $res = call_user_func($task);

                    if ($res instanceof ConcurrentResult)
                    {
                        return $res;
                    }

                    return ConcurrentResult::success($res);
                } catch (Throwable $throwable)
                {
                    return ConcurrentResult::exception("[{$label}] 异常: " . $throwable->getMessage(), $throwable);
                }
            };
        }

        $rawResults = Fork::new()
                          ->run(...array_values($closures));
        $labels     = array_keys($closures);

        foreach ($labels as $i => $label)
        {
            $this->results[$label] = $rawResults[$i] ?? ConcurrentResult::exception("[$label] 未返回结果");
        }

        return $this->results;
    }

    public function getResults(): array
    {
        return $this->results;
    }

    public function getOneResult(string $label): ?ConcurrentResult
    {
        return $this->results[$label] ?? null;
    }
}
