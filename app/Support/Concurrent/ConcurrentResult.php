<?php

namespace App\Support\Concurrent;

use Throwable;

class ConcurrentResult
{
    protected bool $success;
    public bool $isException;
    protected string $message;
    protected mixed $data;
    protected ?Throwable $exception;

    private function __construct(bool $success, string $message, mixed $data = null, bool $isException = false, ?Throwable $exception = null)
    {
        $this->success     = $success;
        $this->message     = $message;
        $this->data        = $data;
        $this->isException = $isException;
        $this->exception   = $exception;
    }

    public static function success(mixed $data): self
    {
        return new self(true, 'ok', $data);
    }

    public static function fail(string $message, mixed $data = null): self
    {
        return new self(false, $message, $data);
    }

    public static function exception(string $message, ?Throwable $exception = null): self
    {
        return new self(false, $message, null, true, $exception);
    }

    public function isFail(): bool
    {
        return !$this->success;
    }

    public function isException(): bool
    {
        return $this->isException;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getData(): mixed
    {
        return $this->data;
    }

    public function getException(): ?Throwable
    {
        return $this->exception;
    }
}
