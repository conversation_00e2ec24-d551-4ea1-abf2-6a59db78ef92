<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        if (app()->environment(['local', 'testing']) && config('app.debug') && env('SQL_LOG_ENABLE', false))
        {
            SqlQueryLogger::enable();  // 自动启用监听
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //保证在最后
        app()->terminating(function () {
            if (\Illuminate\Support\Facades\DB::transactionLevel() > 0)
            {
                \Illuminate\Support\Facades\Log::critical('🚨 检测到事务未提交，当前事务层级: ' . \Illuminate\Support\Facades\DB::transactionLevel());
            }
        });
    }
}
