<?php

namespace App\Providers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SqlQueryLogger
{
    /**
     * 启动 SQL 监听器，并将日志写入指定通道
     *
     * @param string $channelName 日志通道名，默认 sqllog
     *
     * @return void
     */
    public static function enable(string $channelName = 'sqllog'): void
    {
        DB::listen(function ($query) use ($channelName) {
            $sql = $query->sql;
            foreach ($query->bindings as $binding)
            {
                if (is_string($binding)) {
                    $binding = "'" . addslashes($binding) . "'";
                } elseif (is_null($binding)) {
                    $binding = 'NULL';
                } elseif ($binding instanceof \DateTimeInterface) {
                    $binding = "'" . $binding->format('Y-m-d H:i:s') . "'";
                }
                $sql   = preg_replace('/\?/', $binding, $sql, 1);
            }

            $log = "[SQL] {$sql} | time: {$query->time}ms";
            Log::channel($channelName)
               ->debug($log);
        });
    }
}
