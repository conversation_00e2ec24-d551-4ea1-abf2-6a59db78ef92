<?php

namespace App\Services\GPrinter;

/**
 * @method array print(string $deviceId, string $msgDetail, array $options = [])
 * @method array voice(string $deviceId, string $voice, array $options = [])
 * @method array getDeviceList(?string $token = null)
 * @method array pageDevices(int $pageNum = 1, int $pageSize = 10, ?string $token = null)
 * @method array addDevice(string $deviceId, string $devName, ?int $grpID = null)
 * @method array getDevice(string $deviceId, ?string $token = null)
 * @method array getDeviceInfo(string $deviceId, ?string $token = null)
 * @method array editDevice(string $deviceId, string $devName, ?int $grpID = null)
 * @method array deleteDevice(string $deviceId)
 * @method array cancelPrint(string $deviceId, int $all = 0)
 * @method array getStatus(?string $deviceId = null, ?string $productCode = null, ?string $token = null)
 * @method array setLogo(string $deviceId, string $imgUrl)
 * @method array deleteLogo(string $deviceId)
 * @method array setVolume(string $deviceId, int $volume)
 * @method array setVoiceType(string $deviceId, int $voiceType)
 * @method array listTemplate()
 * @method array templatePrint(string $deviceId, string $templateID, string $tData, array $options = [])
 *
 * @see \App\Services\GPrinter\GPrinterService
 */
class GPrinterManager
{
    protected GPrinterService $gprinter;

    public function __construct(GPrinterService $gprinter)
    {
        $this->gprinter = $gprinter;
    }

    public function __call(string $method, array $parameters): mixed
    {
        return $this->gprinter->$method(...$parameters);
    }
}
