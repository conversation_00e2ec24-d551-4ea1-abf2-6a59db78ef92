<?php

namespace App\Services\GPrinter;

use Illuminate\Support\Facades\Facade;

/**
 * @method static array print(string $deviceId, string $msgDetail, array $options = [])
 * @method static array voice(string $deviceId, string $voice, array $options = [])
 * @method static array getDeviceList(?string $token = null)
 * @method static array pageDevices(int $pageNum = 1, int $pageSize = 10, ?string $token = null)
 * @method static array addDevice(string $deviceId, string $devName, ?int $grpID = null)
 * @method static array getDevice(string $deviceId, ?string $token = null)
 * @method static array getDeviceInfo(string $deviceId, ?string $token = null)
 * @method static array editDevice(string $deviceId, string $devName, ?int $grpID = null)
 * @method static array deleteDevice(string $deviceId)
 * @method static array cancelPrint(string $deviceId, int $all = 0)
 * @method static array getStatus(?string $deviceId = null, ?string $productCode = null, ?string $token = null)
 * @method static array setLogo(string $deviceId, string $imgUrl)
 * @method static array deleteLogo(string $deviceId)
 * @method static array setVolume(string $deviceId, int $volume)
 * @method static array setVoiceType(string $deviceId, int $voiceType)
 * @method static array listTemplate()
 * @method static array templatePrint(string $deviceId, string $templateID, string $tData, array $options = [])
 *
 * @see \App\Services\GPrinter\GPrinterService
 */
class GPrinter extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return 'gprinter';
    }
}
