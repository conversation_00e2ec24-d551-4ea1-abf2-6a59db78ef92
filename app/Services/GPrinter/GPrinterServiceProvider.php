<?php

namespace App\Services\GPrinter;

use Illuminate\Support\ServiceProvider;

class GPrinterServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // 合并配置文件
        $this->mergeConfigFrom(
            __DIR__ . '/config/gprinter.php',
            'gprinter'
        );

        // 绑定服务
        $this->app->singleton('gprinter', function ($app) {
            return new GPrinterService();
        });
    }

    public function boot(): void
    {
        // 发布配置文件
        $this->publishes([
                             __DIR__ . '/config/gprinter.php' => config_path('gprinter.php'),
                         ], 'gprinter-config');
    }
}
