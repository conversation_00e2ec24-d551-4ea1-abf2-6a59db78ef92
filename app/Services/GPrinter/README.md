# GPrinterService 佳博云打印服务

## 安装说明

1. 将整个 GPrinterService 目录复制到 `app/Services/` 目录下

2. 在 `config/app.php` 的 `providers` 数组中添加服务提供者：

```php
\App\Services\GPrinter\GPrinterServiceProvider::class,
```

3. 在 `config/app.php` 的 `aliases` 数组中添加门面：

```php
'GPrinter' => \App\Services\GPrinter\GPrinter::class,
```

4. 发布配置文件（可选）：
```bash
php artisan vendor:publish --tag=gprinter-config
```

5. 在 `.env` 文件中添加配置：
```
GPRINTER_API_BASE_URL=https://api.poscom.cn
GPRINTER_MEMBER_CODE=your_member_code
GPRINTER_API_KEY=your_api_key
```

## 使用方法

### 1. 依赖注入

```php
use App\Services\GPrinter\GPrinterService;

public function someMethod(GPrinterService $printerService)
{
    $result = $printerService->print('DEVICE_ID', 'Hello World!');
}
```

### 2. 服务容器获取
```php
$printerService = app('gprinter');
$result = $printerService->print('DEVICE_ID', 'Hello World!');
```

### 3. 门面调用

```php
use App\Services\GPrinter\GPrinter;

$result = GPrinter::print('DEVICE_ID', 'Hello World!');
```

## API 方法

### print(string $deviceId, string $msgDetail, array $options = [])
发送打印任务

### voice(string $deviceId, string $voice, array $options = [])
发送语音播报

### getDeviceList(?string $token = null)
查询打印设备列表

### pageDevices(int $pageNum = 1, int $pageSize = 10, ?string $token = null)
分页查询设备列表

### addDevice(string $deviceId, string $devName, ?int $grpID = null)
添加打印机

### getDevice(string $deviceId, ?string $token = null)
查询打印机信息

### getDeviceInfo(string $deviceId, ?string $token = null)
获取设备详细信息

### editDevice(string $deviceId, string $devName, ?int $grpID = null)
修改打印机信息

### deleteDevice(string $deviceId)
删除打印机

### cancelPrint(string $deviceId, int $all = 0)
取消打印任务

### getStatus(?string $deviceId = null, ?string $productCode = null, ?string $token = null)
获取打印机状态

### setLogo(string $deviceId, string $imgUrl)
设置Logo

### deleteLogo(string $deviceId)
删除Logo

### setVolume(string $deviceId, int $volume)
设置打印音量

### setVoiceType(string $deviceId, int $voiceType)
设置语音播报类型

### listTemplate()
查询模板列表

### templatePrint(string $deviceId, string $templateID, string $tData, array $options = [])
指定模板打印
