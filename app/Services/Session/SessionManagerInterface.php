<?php

namespace App\Services\Session;

/**
 * SESSION管理接口
 */
interface SessionManagerInterface
{
    /**
     * 开始会话
     *
     * @param string|int $uid
     * @param string     $token
     * @param array      $data
     * @param int|null   $lifetime
     *
     * @return string|bool
     */
    public function start(string|int $uid, string $token = '', array $data = [],
                          int|null   $lifetime = null): string|bool;

    /**
     * 销毁会话
     *
     * @param string $token
     *
     * @return bool
     */
    public function destroy(string $token): bool;

    /**
     * 判断会话是否有效
     * 有效则返回uid
     *
     * @param $token
     *
     * @return string|int|bool
     */
    public function exists($token): string|int|bool;

    /**
     * 更新会话过期时间
     *
     * @param          $token
     * @param int|null $lifetime
     *
     * @return bool
     */
    public function refresh($token, int|null $lifetime = null): bool;

    /**
     * 删除所有会话
     *
     * @return bool
     */
    public function flush(): bool;
}
