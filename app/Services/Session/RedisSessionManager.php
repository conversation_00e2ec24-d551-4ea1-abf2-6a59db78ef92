<?php

namespace App\Services\Session;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Redis\Connections\Connection;

/**
 * SESSION管理的REDIS实现
 */
class RedisSessionManager implements SessionManagerInterface
{
    /**
     * 会话中，uid的存储key
     *
     * @var string
     */
    private const string TOKEN_UID_KEY = 'uid';

    /**
     * 配置
     *
     * @var array
     */
    private array $config = [
        'redis_connection'       => 'default',
        'redis_token_key_prefix' => 'token:',
        'redis_login_key_prefix' => 'login:',
        'lifetime'               => 86400,
        'single_device'          => false,
    ];

    public function __construct()
    {
        $this->config = array_merge($this->config, config('setting.session', []));
    }

    /**
     * 获取redis连接实例
     *
     * @param string $connectionName
     *
     * @return Connection|\Redis
     */
    public function getRedis(string $connectionName = ''): Connection|\Redis
    {
        return \Illuminate\Support\Facades\Redis::connection($connectionName ?: $this->config['redis_connection']);
    }

    /**
     * 生成token字符串
     *
     * @return string
     */
    public function generateToken(): string
    {
        return hash('sha256', Str::uuid());
    }

    /**
     * 获取token存储key
     *
     * @param string $token
     *
     * @return string
     */
    public function getTokenKey(string $token): string
    {
        return $this->config['redis_token_key_prefix'] . $token;
    }

    /**
     * 获取保存uid映射token集合的key前缀
     *
     * @param string|int $uid
     *
     * @return string
     */
    public function getLoginKey(string|int $uid): string
    {
        return $this->config['redis_login_key_prefix'] . $uid;
    }

    /**
     * 开始会话
     *
     * @param string|int $uid
     * @param string     $token
     * @param array      $data
     * @param int|null   $lifetime
     *
     * @return string|bool
     */
    public function start(string|int $uid, string $token = '', array $data = [],
                          int|null   $lifetime = null): string|bool
    {
        $token        = $token ?: $this->generateToken();
        $tokenKey     = $this->getTokenKey($token);
        $loginKey     = $this->getLoginKey($uid);
        $singleDevice = $this->config['single_device'];

        $data[self::TOKEN_UID_KEY] = $uid;
        $lifetime                  = $lifetime ?: $this->config['lifetime'];

        try
        {
            //获取已有登录
            $existsTokens    = $this->getRedis()->sMembers($loginKey);
            $existsTokenKeys = [];
            if (is_array($existsTokens) && !empty($existsTokens))
            {
                $existsTokenKeys = array_map(fn($value) => $this->getTokenKey($value), $existsTokens);
            }

            // Redis 事务
            $this->getRedis()->transaction(function (\Redis $redis) use (
                $tokenKey, $loginKey, $token, $data, $lifetime, $existsTokenKeys, $singleDevice
            ) {
                if ($singleDevice && !empty($existsTokenKeys))
                {
                    $redis->del($existsTokenKeys);
                    $redis->del($loginKey);
                }
                $redis->hMSet($tokenKey, $data);
                $redis->expire($tokenKey, $lifetime);
                $redis->sAdd($loginKey, $token);
                $redis->expire($loginKey, $lifetime);
            });
        } catch (\Exception)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' redis transaction exec fail', $data);

            return false;
        }

        return $token;
    }

    /**
     * 销毁会话
     *
     * @param string $token
     *
     * @return bool
     */
    public function destroy(string $token): bool
    {
        $uid = false;

        try
        {
            $uid = $this->exists($token);

            if ($uid === false)
            {
                return true;
            }

            $tokenKey     = $this->getTokenKey($token);
            $loginKey     = $this->getLoginKey($uid);
            $singleDevice = $this->config['single_device'];

            // Redis 事务
            $this->getRedis()->transaction(function (\Redis $redis) use ($tokenKey, $loginKey, $token, $singleDevice) {
                $redis->del($tokenKey);

                if ($singleDevice)
                {
                    $redis->del($loginKey);
                }
                else
                {
                    $redis->sRem($loginKey, $token);
                }
            });
        } catch (\Exception)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' redis transaction exec fail',
                       ['token' => $token, self::TOKEN_UID_KEY => $uid]);

            return false;
        }

        return true;
    }

    /**
     * 判断会话是否有效
     * 有效则返回uid
     *
     * @param $token
     *
     * @return string|int|bool
     */
    public function exists($token): string|int|bool
    {
        $uid = $this->getRedis()->hGet($this->getTokenKey($token), self::TOKEN_UID_KEY);

        return $uid ?: false;
    }

    /**
     * 更新会话过期时间
     *
     * @param          $token
     * @param int|null $lifetime
     *
     * @return bool
     */
    public function refresh($token, int|null $lifetime = null): bool
    {
        $uid = false;

        try
        {
            $uid = $this->exists($token);

            if ($uid === false)
            {
                return false;
            }

            $tokenKey = $this->getTokenKey($token);
            $loginKey = $this->getLoginKey($uid);
            $lifetime = $lifetime ?: $this->config['lifetime'];

            // Redis 事务
            $this->getRedis()->transaction(function (\Redis $redis) use ($tokenKey, $loginKey, $lifetime) {
                $redis->expire($tokenKey, $lifetime);
                $redis->expire($loginKey, $lifetime);
            });
        } catch (\Exception)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' redis transaction exec fail',
                       ['token' => $token, self::TOKEN_UID_KEY => $uid]);

            return false;
        }

        return true;
    }

    /**
     * 清空整个会话DB，删除所有会话
     * 注意：会话库存储了其他信息的慎用
     *
     * @return bool
     */
    public function flush(): bool
    {
        return $this->getRedis()->flushDb();
    }


    /**
     * 存入多个键值对
     *
     * @param string                               $token
     * @param array<string, int|string|float|bool> $data
     *
     * @return bool
     */
    public function put(string $token, array $data): bool
    {
        return $this->getRedis()->hMSet($this->getTokenKey($token), $data);
    }

    /**
     * 存入键值对
     *
     * @param string                $token
     * @param string                $key
     * @param int|string|float|bool $value
     *
     * @return bool
     */
    public function push(string $token, string $key, int|string|float|bool $value): bool
    {
        $result = $this->getRedis()->hSet($this->getTokenKey($token), $key, $value);

        return !($result === false);
    }

    /**
     * 获取一个键
     *
     * @param string                              $token
     * @param string                              $key
     * @param int|string|float|bool|callable|null $default
     *
     * @return mixed
     */
    public function get(string $token, string $key, int|string|float|bool|callable|null $default = null): mixed
    {
        $result = $this->getRedis()->hGet($this->getTokenKey($token), $key);

        if (is_callable($default))
        {
            return $default($result);
        }

        if ($result === false && $default != null)
        {
            return $default;
        }

        return $result;
    }

    /**
     * 取出一个键，并删除
     *
     * @param string                     $token
     * @param string                     $key
     * @param int|string|float|bool|null $default
     *
     * @return int|string|float|bool|null
     */
    public function pull(string                     $token, string $key,
                         int|string|float|bool|null $default = null): int|string|float|bool|null
    {
        $result = $this->getRedis()->hGet($this->getTokenKey($token), $key);
        if ($result === false && $default != null)
        {
            return $default;
        }
        $this->forget($token, $key);

        return $result;
    }

    /**
     * 取出所有
     *
     * @param string $token
     *
     * @return array
     */
    public function all(string $token): array
    {
        return $this->getRedis()->hGetAll($this->getTokenKey($token));
    }

    /**
     * 是否存在某个键
     *
     * @param string $token
     * @param string $key
     *
     * @return bool
     */
    public function has(string $token, string $key): bool
    {
        return $this->getRedis()->hExists($this->getTokenKey($token), $key);
    }

    /**
     * 删除某个键
     *
     * @param string $token
     * @param string $key
     *
     * @return bool
     */
    public function forget(string $token, string $key): bool
    {
        $result = $this->getRedis()->hDel($this->getTokenKey($token), $key);

        return ($result > 0);
    }
}
