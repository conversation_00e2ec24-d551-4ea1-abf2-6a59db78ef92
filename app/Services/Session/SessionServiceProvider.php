<?php

namespace App\Services\Session;

use Illuminate\Support\ServiceProvider;

class SessionServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(SessionManagerInterface::class, RedisSessionManager::class);

        $this->app->alias(RedisSessionManager::class, 'redis.session');
    }

    public function boot(): void
    {
        //
    }
}

