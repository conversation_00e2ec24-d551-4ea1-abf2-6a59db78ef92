<?php

namespace App\Services\Search;

use App\Services\Search\Contracts\SearchServiceInterface;
use App\Services\Search\Traits\IndexNameTrait;
use Meilisearch\Client;

class MeilisearchService implements SearchServiceInterface
{
    use IndexNameTrait;

    private Client $client;
    private string $indexName = '';

    public function __construct(Client $client)
    {
        $this->client = $client;
    }

    public function setIndexName(string $indexName): self
    {
        $this->indexName = $indexName;

        return $this;
    }

    public function getIndexName(): string
    {
        return $this->indexName;
    }

    public function searchRecipeItems(string $keywords, int $purchaseHospitalId = 0, array $supportPetCategoryIds = [], array $firstSaleTypeId = [
        1,
        2,
        3,
        4
    ], int                                   $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $index = $this->client->index($this->buildRecipeItemsIndexName($orgId));
        }
        else
        {
            $index = $this->client->index($this->getIndexName());
        }

        // TODO 指定医院有采购记录
        $hospitalPurchaseFilter = '';
        if ($purchaseHospitalId > 0)
        {
            $hospitalPurchaseFilter = sprintf('purchase_hospital_ids IN [%d]', $purchaseHospitalId);
        }

        // 构建宠物类型筛选条件
        $supportPetCategoryFilter = empty($supportPetCategoryIds) ? '' : sprintf('item_support_pet_category IS EMPTY OR item_support_pet_category IN [%s]',
                                                                                 implode(',', $supportPetCategoryIds));

        // 构建一级项目类型筛选条件
        $firstSaleTypeFilter = empty($firstSaleTypeId) ? '' : sprintf('first_sale_type.id IN [%s]',
                                                                      implode(',', $firstSaleTypeId));

        $results = $index->search($keywords, [
            'filter'                => [
                $firstSaleTypeFilter,
                $supportPetCategoryFilter,
                'is_recipe_allow = true',
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ]);

        //var_dump($results);

        return $this->formatResults($results->getHits());
    }

    public function searchBeautyItems(string $keywords, array $supportPetCategoryIds = [], array $firstSaleTypeId = [5], int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $index = $this->client->index($this->buildBeautyItemsIndexName($orgId));
        }
        else
        {
            $index = $this->client->index($this->getIndexName());
        }

        // 构建宠物类型筛选条件
        $supportPetCategoryFilter = empty($supportPetCategoryIds) ? '' : sprintf('item_support_pet_category IS EMPTY OR item_support_pet_category IN [%s]',
                                                                                 implode(',', $supportPetCategoryIds));

        // 构建一级项目类型筛选条件
        $firstSaleTypeFilter = empty($firstSaleTypeId) ? '' : sprintf('first_sale_type.id IN [%s]',
                                                                      implode(',', $firstSaleTypeId));

        $results = $index->search($keywords, [
            'filter'                => [
                $firstSaleTypeFilter,
                $supportPetCategoryFilter,
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ]);

        //var_dump($results);

        return $this->formatResults($results->getHits());
    }

    public function searchRetailItems(string $keywords, array $firstSaleTypeId = [1], int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $index = $this->client->index($this->buildRetailItemsIndexName($orgId));
        }
        else
        {
            $index = $this->client->index($this->getIndexName());
        }

        // 构建一级项目类型筛选条件
        $firstSaleTypeFilter = empty($firstSaleTypeId) ? '' : sprintf('first_sale_type.id IN [%s]',
                                                                      implode(',', $firstSaleTypeId));

        if (isLikelyBarcode($keywords))
        {
            $barcodeFilter = "barcode = '$keywords'";
            $barcodeResult = $index->search('', [
                'filter'                => [
                    $firstSaleTypeFilter,
                    $barcodeFilter,
                    'is_retail_allow = true',
                    'status = 1',
                ],
                'limit'                 => $limit,
                'attributesToRetrieve'  => ['*'],
                'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
                'showRankingScore'      => true,
            ]);

            if ($barcodeResult->count() > 0)
            {
                return $this->formatResults($barcodeResult->getHits());
            }
        }

        $fullTextResult = $index->search($keywords, [
            'filter'                => [
                $firstSaleTypeFilter,
                'is_retail_allow = true',
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ]);

        //var_dump($results);

        return $this->formatResults($fullTextResult->getHits());
    }

    /**
     * 采购商品搜索
     *
     * @param string   $keywords           商品名称关键字
     * @param int      $purchaseHospitalId 指定医院ID存在采购记录（到货签收后认为存在采购记录）
     * @param array    $itemIds            指定商品ID
     * @param int      $brandId            指定商品品牌ID
     * @param int      $limit              指定获取条目
     * @param int|null $orgId              指定机构ID
     *
     * @return array
     */
    public function searchPurchaseItems(string $keywords, int $purchaseHospitalId = 0, array $itemIds = [], int $brandId = 0, int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $index = $this->client->index($this->buildPurchaseItemsIndexName($orgId));
        }
        else
        {
            $index = $this->client->index($this->getIndexName());
        }

        // 商品采购状态有效
        $isPurchasableFilter = 'is_purchasable = 1';

        // 采购单商品只可以增加一级分类：药品，
        $firstSaleTypeFilter = 'first_sale_type.id IN [1]';

        // 排除组合
        $isSuitFilter = 'is_suit = 0';

        // 指定医院有采购记录
        $hospitalPurchaseFilter = '';
        if ($purchaseHospitalId > 0)
        {
            $hospitalPurchaseFilter = sprintf('purchase_hospital_ids IN [%d]', $purchaseHospitalId);
        }

        // 指定商品ID
        $itemIdsFilter = '';
        if (!empty($itemIds))
        {
            $itemIdsFilter = sprintf('id IN [%s]', implode(',', $itemIds));
        }

        // 构建品牌筛选条件
        $brandFilter = '';
        if ($brandId > 0)
        {
            $brandFilter = sprintf('brand.id = %d', $brandId);
        }

        // 执行搜索
        $results = $index->search($keywords, [
            'filter'                => [
                $isPurchasableFilter,
                $itemIdsFilter,
                $firstSaleTypeFilter,
                $hospitalPurchaseFilter,
                $brandFilter,
                $isSuitFilter,
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ]);

        return $this->formatResults($results->getHits());
    }

    public function searchConsumablesItems(string $keywords, int $hospitalId, array $firstSaleTypeIds = [1], int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $index = $this->client->index($this->buildConsumablesItemsIndexName($orgId));
        }
        else
        {
            $index = $this->client->index($this->getIndexName());
        }

        // 一级项目类型：默认药品
        $firstSaleTypeFilter = '';
        if (!empty($firstSaleTypeIds))
        {
            $firstSaleTypeFilter = sprintf('first_sale_type.id IN [%s]', implode(',', $firstSaleTypeIds));
        }

        // 是耗材领用标记：默认是
        $isReceiveAllowFilter = 'is_receive_allow = 1';

        // 指定医院有采购记录
        $hospitalPurchaseFilter = sprintf('purchase_hospital_ids IN [%d]', $hospitalId);

        // 排除组合
        $isSuitFilter = 'is_suit = 0';

        $results = $index->search($keywords, [
            'filter'                => [
                $firstSaleTypeFilter,
                $isReceiveAllowFilter,
                //$hospitalPurchaseFilter, // TODO 指定医院有采购记录
                $isSuitFilter,
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ]);

        return $this->formatResults($results->getHits());
    }

    private function formatResults(array $hits): array
    {
        return array_map(function ($item) {
            return [
                ...$item,
                'highlight_name'       => $item['_formatted']['name'] ?? null,
                'highlight_alias_name' => $item['_formatted']['alias_name'] ?? null,
                'highlight_basis_name' => $item['_formatted']['basis_name'] ?? null,
                'ranking_score'        => $item['_rankingScore'] ?? null,
            ];
        }, $hits);
    }

}
