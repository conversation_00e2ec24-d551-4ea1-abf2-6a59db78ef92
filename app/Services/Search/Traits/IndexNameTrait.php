<?php

namespace App\Services\Search\Traits;

trait IndexNameTrait
{
    public function buildIndexName(string $searchType, array $prefixSearch = [], array $prefixReplace = []): string
    {
        $indexName = config(sprintf('search.search_type_to_index.%s', $searchType), '');
        if (!empty($prefixSearch) && !empty($prefixReplace))
        {
            return str_replace($prefixSearch, $prefixReplace, $indexName);
        }

        return $indexName;
    }

    public function buildRecipeItemsIndexName(int $orgId): string
    {
        return $this->buildIndexName('recipe_items', ['{orgId}'], [$orgId]);
    }

    public function buildBeautyItemsIndexName(int $orgId): string
    {
        return $this->buildIndexName('beauty_items', ['{orgId}'], [$orgId]);
    }

    public function buildRetailItemsIndexName(int $orgId): string
    {
        return $this->buildIndexName('retail_items', ['{orgId}'], [$orgId]);
    }

    public function buildPurchaseItemsIndexName(int $orgId): string
    {
        return $this->buildIndexName('purchase_items', ['{orgId}'], [$orgId]);
    }

    public function buildConsumablesItemsIndexName(int $orgId): string
    {
        return $this->buildIndexName('consumables_items', ['{orgId}'], [$orgId]);
    }
}
