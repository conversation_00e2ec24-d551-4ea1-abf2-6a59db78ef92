<?php

namespace App\Services\Search;

use App\Services\Search\Contracts\SearchServiceInterface;
use App\Services\Search\Traits\IndexNameTrait;
use Elastic\Elasticsearch\Client;

class ElasticsearchService implements SearchServiceInterface
{
    use IndexNameTrait;

    private Client $client;
    private string $indexName = '';

    public function __construct(Client $client)
    {
        $this->client = $client;
    }

    public function setIndexName(string $indexName): self
    {
        $this->indexName = $indexName;

        return $this;
    }

    public function getIndexName(): string
    {
        return $this->indexName;
    }

    public function searchRecipeItems(
        string   $keywords,
        array    $supportPetCategoryIds = [],
        int      $limit = 200,
        int|null $orgId = null,
    ): array
    {
        if (!empty($orgId))
        {
            $index = $this->buildRecipeItemsIndexName($orgId);
        }
        else
        {
            $index = $this->getIndexName();
        }

        $params = [
            'index' => $index,
            'body'  => [
                'query'     => [
                    'bool' => [
                        'must'   => [
                            'multi_match' => [
                                'query'  => $keywords,
                                'fields' => [
                                    'alias_name^10',
                                    'basis_name^8',
                                    'name^7',
                                    'english_name^5',
                                    'mem_code^3',
                                ],
                            ],
                        ],
                        'filter' => $this->buildFilters($supportPetCategoryIds),
                    ],
                ],
                'size'      => $limit,
                '_source'   => ['*'],
                'highlight' => [
                    'fields' => [
                        'name'       => new \stdClass(),
                        'alias_name' => new \stdClass(),
                        'basis_name' => new \stdClass(),
                    ],
                ],
            ],
        ];

        $response = $this->client->search($params);

        return $this->formatResults($response->asArray());
    }

    private function buildFilters(array $supportPetCategoryIds): array
    {
        $filters = [
            ['term' => ['is_recipe_allow' => true]],
            ['term' => ['status' => 1]],
        ];

        if (!empty($supportPetCategoryIds))
        {
            $filters[] = [
                'bool' => [
                    'should'               => [
                        ['terms' => ['item_support_pet_category' => $supportPetCategoryIds]],
                        [
                            'script' => [
                                'script' => "doc['item_support_pet_category'].size() == 0",
                            ],
                        ],
                    ],
                    'minimum_should_match' => 1,
                ],
            ];
        }

        return $filters;
    }

    private
    function formatResults(array $response): array
    {
        return array_map(function ($hit) {
            return [
                ...$hit['_source'],
                'highlight_name'       => $hit['highlight']['name'][0] ?? null,
                'highlight_alias_name' => $hit['highlight']['alias_name'][0] ?? null,
                'highlight_basis_name' => $hit['highlight']['basis_name'][0] ?? null,
            ];
        }, $response['hits']['hits']);
    }
}
