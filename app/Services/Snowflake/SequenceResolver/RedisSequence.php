<?php

namespace App\Services\Snowflake\SequenceResolver;

use App\Services\Snowflake\Configuration;
use Illuminate\Support\Facades\Redis;

readonly class RedisSequence implements SequenceResolver
{
    private string $redisConnection;
    private int $dataCenterId;
    private int $machineId;

    public function __construct(Configuration $config)
    {
        $this->redisConnection = $config->getRedisConnection();
        $this->dataCenterId    = $config->getDataCenterId();
        $this->machineId       = $config->getMachineId();
    }

    public function next(int $currentTime): int
    {
        $key = "snowflake:$this->dataCenterId:$this->machineId:$currentTime";

        $result = Redis::connection($this->redisConnection)->multi()
                       ->incr($key)
                       ->expire($key, 60)
                       ->exec();

        return (int) $result[0];
    }
}
