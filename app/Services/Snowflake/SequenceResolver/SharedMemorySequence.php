<?php

namespace App\Services\Snowflake\SequenceResolver;

use App\Services\Snowflake\Configuration;
use App\Services\Snowflake\Exceptions\SnowflakeException;

class SharedMemorySequence implements SequenceResolver
{
    private readonly int $machineId;
    private $shmId;
    private $semId;

    public function __construct(Configuration $config)
    {
        $this->machineId = $config->getMachineId();
        $this->initSharedMemory();
    }

    private function initSharedMemory(): void
    {
        // 生成共享内存唯一键
        $key = ftok(__FILE__, 's') + $this->machineId;

        // 打开或创建共享内存段
        $this->shmId = shmop_open($key, 'c', 0644, 16);  // 扩展为16个字节存储

        if (!$this->shmId)
        {
            throw new SnowflakeException("Failed to access shared memory");
        }

        // 获取信号量用于同步
        $this->semId = sem_get($key, 1, 0666, 1);  // 获取一个信号量

        if (!$this->semId)
        {
            throw new SnowflakeException("Failed to get semaphore");
        }
    }

    public function next(int $currentTime): int
    {
        // 获取信号量锁
        if (!sem_acquire($this->semId))
        {
            return - 1; // 信号量获取失败
        }

        try
        {
            // 读取共享内存中的数据
            $data   = shmop_read($this->shmId, 0, 16);  // 扩展为16个字节存储
            $stored = unpack('Jtime/Vseq', $data);  // 解包，包括最后更新时间戳

            if (!is_array($stored))
            {
                $stored = ['time' => $currentTime, 'seq' => 0];
            }
            elseif ($stored['time'] !== $currentTime)
            {
                // 如果时间戳不一致，或者数据过期，重置
                $stored = ['time' => $currentTime, 'seq' => 0];
            }
            else
            {
                // 如果时间戳一致，则序列号加1
                $stored['seq'] = $stored['seq'] + 1;
            }

            // 将数据打包并写回共享内存
            shmop_write($this->shmId, pack('JV', $stored['time'], $stored['seq']), 0);

            return $stored['seq'];  // 返回生成的序列号
        } finally
        {
            // 释放信号量锁
            sem_release($this->semId);
        }
    }

    public function __destruct()
    {
        // 注意：此处不删除共享内存和信号量
        // shmop_close($this->shmId);  // 不在进程析构时删除共享内存
        // sem_release($this->semId);  // 进程退出时应该释放信号量
    }
}

