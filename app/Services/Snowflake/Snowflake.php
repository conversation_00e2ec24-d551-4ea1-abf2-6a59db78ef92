<?php

namespace App\Services\Snowflake;

use App\Services\Snowflake\SequenceResolver\SequenceResolver;
use App\Services\Snowflake\Exceptions\SnowflakeException;

class Snowflake
{
    protected Configuration $config;
    protected int $datacenterId;
    protected int $machineId;
    protected ?SequenceResolver $sequenceResolver;
    protected int $sequence = 0;
    protected int $lastTimestamp = - 1;

    const int EPOCH = 1735660800000; // 定义雪花算法的起始时间（北京时间 2025年1月1日）
    const int DATACENTER_BITS = 3;   // 数据中心ID长度，最大支持8个数据中心
    const int MACHINE_BITS = 8;      // 机器ID长度，最大支持256个机器
    const int SEQUENCE_BITS = 8;     // 序列号长度，每个机器每毫秒最多生成256个ID

    const int MAX_DATACENTER_ID = - 1 ^ (- 1 << self::DATACENTER_BITS); // 最大数据中心ID
    const int MAX_MACHINE_ID = - 1 ^ (- 1 << self::MACHINE_BITS);       // 最大机器ID
    const int MAX_SEQUENCE = - 1 ^ (- 1 << self::SEQUENCE_BITS);        // 最大序列号

    protected int $datacenterShift = self::SEQUENCE_BITS + self::MACHINE_BITS;
    protected int $machineShift = self::SEQUENCE_BITS;
    protected int $timestampShift = self::SEQUENCE_BITS + self::MACHINE_BITS + self::DATACENTER_BITS;

    public function __construct(Configuration $config, ?SequenceResolver $sequenceResolver = null)
    {
        $datacenterId = $config->getDatacenterId();
        $machineId    = $config->getMachineId();

        if ($datacenterId > self::MAX_DATACENTER_ID || $datacenterId < 0)
        {
            throw new SnowflakeException("Datacenter ID is invalid");
        }

        if ($machineId > self::MAX_MACHINE_ID || $machineId < 0)
        {
            throw new SnowflakeException("Machine ID is invalid");
        }

        $this->config           = $config;
        $this->datacenterId     = $datacenterId;
        $this->machineId        = $machineId;
        $this->sequenceResolver = $sequenceResolver;
    }

    public function generateId(): string
    {
        $timestamp = $this->getCurrentMillisecond();

        // 检查时间是否回拨
        if ($timestamp < $this->lastTimestamp)
        {
            throw new SnowflakeException("Clock moved backwards. Refusing to generate ID");
        }

        $attempt = 0;
        while (($sequence = $this->nextSequence($timestamp)) < 0 || $sequence > self::MAX_SEQUENCE)
        {
            usleep(min(100 * pow(2, $attempt), 1000));  // 指数退避，最多等待 1000 微秒
            $timestamp = $this->getCurrentMillisecond();
            $attempt ++;
        }

        $this->lastTimestamp = $timestamp;

        // 生成雪花ID
        return (($timestamp - self::EPOCH) << $this->timestampShift)
               | ($this->datacenterId << $this->datacenterShift)
               | ($this->machineId << $this->machineShift)
               | $this->sequence;
    }

    public function parseId(string $snowflakeId): array|false
    {
        // 先确保是有效的数字并符合雪花算法的位宽要求
        if (!is_numeric($snowflakeId) || strlen(decbin($snowflakeId)) > 64)
        {
            return false;
        }

        // 提取各个部分
        $timestamp    = ($snowflakeId >> $this->timestampShift) + self::EPOCH;
        $datacenterId = ($snowflakeId >> $this->datacenterShift) & self::MAX_DATACENTER_ID;
        $machineId    = ($snowflakeId >> $this->machineShift) & self::MAX_MACHINE_ID;
        $sequence     = $snowflakeId & self::MAX_SEQUENCE;

        // 验证各个部分的合理性
        if ($datacenterId < 0 || $datacenterId > self::MAX_DATACENTER_ID)
        {
            return false;
        }

        if ($machineId < 0 || $machineId > self::MAX_MACHINE_ID)
        {
            return false;
        }

        if ($sequence < 0 || $sequence > self::MAX_SEQUENCE)
        {
            return false;
        }

        // 验证时间戳是否合理，确保其不早于纪元时间，并且不超过当前时间
        $currentTimestamp = $this->getCurrentMillisecond();  // 获取当前时间戳（毫秒）
        if ($timestamp < self::EPOCH || $timestamp > $currentTimestamp)
        {
            return false;
        }

        // 返回解析后的各个字段
        return [
            'timestamp'    => $timestamp,
            'datacenterId' => $datacenterId,
            'machineId'    => $machineId,
            'sequence'     => $sequence,
        ];
    }


    public function getConfiguration(): array
    {
        return $this->config->getAll();
    }

    private function nextSequence(int $currentTime): int
    {
        if ($this->sequenceResolver)
        {
            $this->sequence = $this->sequenceResolver->next($currentTime);
        }
        else
        {
            if ($currentTime == $this->lastTimestamp)
            {
                $this->sequence ++;
            }
            else
            {
                $this->sequence = 0;
            }
        }


        return $this->sequence;
    }

    // 获取当前时间戳
    private function getCurrentMillisecond(): int
    {
        return (int) (microtime(true) * 1000);
    }
}
