<?php

namespace App\Services\Snowflake;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;

class Configuration
{
    private string $machineIdConfig;
    private int $maxMachineId = 256; //最大设备ID，需要与算法核心的常量统一
    private string $hostnameRegex;
    private int $dataCenterId;
    private int $machineId;
    private ?string $sequenceResolver;
    private string $redisConnection;

    public function __construct()
    {
        $this->initialize();
    }

    private function initialize(): void
    {
        // 从配置获取
        $this->machineIdConfig  = Config::get('snowflake.machine_id_config', 'default');
        $this->hostnameRegex    = Config::get('snowflake.hostname_regex', '/server-(\d+)/');
        $this->dataCenterId     = Config::get('snowflake.datacenter_id', 0);
        $this->machineId        = match ($this->machineIdConfig)
        {
            'config'   => Config::get('snowflake.machine_id', 0),
            'hostname' => App::make(MachineIdResolver::class)
                             ->resolveByHostname($this->hostnameRegex, $this->getMaxMachineId()),
            'auto'     => App::make(MachineIdResolver::class)->resolveByMacAddress($this->getMaxMachineId()),
            default    => 0
        };
        $this->sequenceResolver = Config::get('snowflake.sequence_resolver');
        $this->redisConnection  = Config::get('snowflake.redis_connection', 'default');
    }

    public function getAll(): array
    {
        return [
            'max_machine_id'    => $this->maxMachineId,
            'datacenter_id'     => $this->dataCenterId,
            'machine_id'        => $this->machineId,
            'machine_id_config' => $this->machineIdConfig,
            'hostname_regex'    => $this->hostnameRegex,
            'sequence_resolver' => $this->sequenceResolver,
            'redis_connection'  => $this->redisConnection,
        ];
    }

    public function getDataCenterId(): int
    {
        return $this->dataCenterId;
    }

    public function getMachineId(): int
    {
        return $this->machineId;
    }

    public function getMaxMachineId(): int
    {
        return $this->maxMachineId;
    }

    public function getMachineIdConfig(): string
    {
        return $this->machineIdConfig;
    }

    public function getHostnameRegex(): string
    {
        return $this->hostnameRegex;
    }

    public function getSequenceResolver(): ?string
    {
        return $this->sequenceResolver;
    }

    public function getRedisConnection(): string
    {
        return $this->redisConnection;
    }
}
