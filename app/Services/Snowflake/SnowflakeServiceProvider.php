<?php

namespace App\Services\Snowflake;

use Illuminate\Support\ServiceProvider;
use App\Services\Snowflake\SequenceResolver\SequenceResolver;
use App\Services\Snowflake\SequenceResolver\RedisSequence;
use App\Services\Snowflake\SequenceResolver\SharedMemorySequence;

class SnowflakeServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(Configuration::class);
        $this->app->singleton(Snowflake::class, function ($app) {
            $config   = $app->make(Configuration::class);
            $resolver = $this->resolveSequence($config);

            return new Snowflake($config, $resolver);
        });
    }

    private function resolveSequence(Configuration $config): ?SequenceResolver
    {
        return match ($config->getSequenceResolver())
        {
            'shared_memory' => new SharedMemorySequence(app(Configuration::class)),
            'redis'         => new RedisSequence(app(Configuration::class)),
            default         => null,
        };
    }
}
