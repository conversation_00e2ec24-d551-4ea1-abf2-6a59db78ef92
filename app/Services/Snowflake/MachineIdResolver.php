<?php

namespace App\Services\Snowflake;

class MachineIdResolver
{
    public function resolveByHostname(string $pattern, int $maxMachineId): int
    {
        // 尝试从hostname获取
        $hostname = gethostname();
        if (preg_match($pattern, $hostname, $matches))
        {
            return intval($matches[1]) % $maxMachineId;
        }

        return 0;
    }

    public function resolveByMacAddress(int $maxMachineId): int
    {
        $mac = $this->getMacAddress();

        return abs(crc32($mac)) % $maxMachineId;
    }

    private function getMacAddress(): string
    {
        $mac = shell_exec('cat /sys/class/net/eth0/address 2>/dev/null');
        if (!$mac)
        {
            $mac = shell_exec("ifconfig | grep ether | head -n 1 | awk '{print $2}'");
        }

        return trim($mac) ?: uniqid('mac_fallback_', true);
    }
}
