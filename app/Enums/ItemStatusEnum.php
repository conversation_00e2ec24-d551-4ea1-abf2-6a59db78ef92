<?php

namespace App\Enums;

/**
 * 商品状态
 * item.status
 */
enum ItemStatusEnum: int
{
    use EnumToolTrait;

    case Deleted = - 1;

    case Waiting = 0;

    case Online = 1;

    case Offline = 2;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        - 1 => '已删除',
        0   => '待上线',
        1   => '已上线',
        2   => '已下线',
    ];
}
