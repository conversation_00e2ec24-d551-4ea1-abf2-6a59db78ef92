<?php

namespace App\Enums;

/**
 * 化验、影像状态
 * his_tests.status、his_images.status
 *
 * Class TestOrImageStatusEnum
 * @package App\Enums
 */
enum TestOrImageStatusEnum: int
{

    use EnumToolTrait;

    case Waiting = 0;

    case Testing = 1;
    case Tested = 2;
    case Invalid = 3;

    public const array DESCRIPTIONS = [
        self::Waiting->value => '待检测',
        self::Testing->value => '检测中',
        self::Tested->value  => '已检测',
        self::Invalid->value => '已作废',
    ];

    /**
     * 所有可选项（用于前端筛选）
     */
    public static function options(): array
    {
        return array_map(fn($item) => [
            'id'   => $item->value,
            'name' => $item->getDescription($item->value),
        ], self::cases());
    }

    /**
     * 根据状态获取对应的查询条件
     *
     * @param int    $status
     * @param string $tableName
     *
     * @return array|array[]
     */
    public static function getConditions(int $status, string $tableName = ''): array
    {
        $tablePrefix = '';
        if (!empty($tableName))
        {
            $tablePrefix = $tableName . '.';
        }

        return match (self::tryFrom($status))
        {
            self::Waiting => [
                [$tablePrefix . 'status', '=', 1],
                [$tablePrefix . 'start_status', '=', 0],
            ],
            self::Testing => [
                [$tablePrefix . 'start_status', '=', 1],
                [$tablePrefix . 'result_status', '=', 0],
            ],
            self::Tested => [
                [$tablePrefix . 'start_status', '=', 1],
                [$tablePrefix . 'result_status', '=', 1],
            ],
            self::Invalid => [
                [$tablePrefix . 'status', '=', 0],
            ],
            default => [],
        };
    }

    /**
     * 根据记录信息返回当前统一状态（前端识别用）
     */
    public static function FormatTestOrImageStatus(array $taskInfo): array
    {
        if (empty($taskInfo))
        {
            return [];
        }

        // 已作废
        if (empty($taskInfo['status']))
        {
            return [
                'id'   => self::Invalid->value,
                'name' => self::DESCRIPTIONS[self::Invalid->value],
            ];
        }

        // 待检测
        if ($taskInfo['start_status'] == TestOrImageStatusEnum::Waiting->value)
        {
            return [
                'id'   => self::Waiting->value,
                'name' => self::DESCRIPTIONS[self::Waiting->value],
            ];
        }

        // 检测中
        if ($taskInfo['start_status'] == TestOrImageStatusEnum::Testing->value && empty($taskInfo['result_status']))
        {
            return [
                'id'   => self::Testing->value,
                'name' => self::DESCRIPTIONS[self::Testing->value],
            ];
        }

        // 已检测
        if ($taskInfo['start_status'] == TestOrImageStatusEnum::Testing->value && !empty($taskInfo['result_status']))
        {
            return [
                'id'   => self::Tested->value,
                'name' => self::DESCRIPTIONS[self::Tested->value],
            ];
        }

        return [
            'id'   => '',
            'name' => '',
        ];
    }
}
