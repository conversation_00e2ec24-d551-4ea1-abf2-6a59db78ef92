<?php

namespace App\Enums;

/**
 * 门诊状态
 */
enum OutpatientStatusEnum: int
{
    use EnumToolTrait;

    case Waiting = 0;

    case InTreatment = 1;

    case Stop = 2;

    case Completed = 3;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        0 => '候诊中',
        1 => '就诊中',
        2 => '暂停中',
        3 => '结束',
    ];

    /**
     * 有效门诊状态
     */
    public const array VALID_STATUS = [
        self::Waiting->value,
        self::InTreatment->value,
        self::Stop->value
    ];
}
