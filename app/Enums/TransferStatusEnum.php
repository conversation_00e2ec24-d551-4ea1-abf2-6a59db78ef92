<?php

namespace App\Enums;

/**
 * 转诊状态
 */
enum TransferStatusEnum: int
{
    use EnumToolTrait;

    case Waiting = 0;

    case Success = 1;

    case Rejected = 2;

    case Invalid = - 1;

    public const array DESCRIPTIONS = [
        self::Waiting->value  => '等待办理',
        self::Success->value  => '办理成功',
        self::Rejected->value => '已拒绝',
        self::Invalid->value  => '已作废',
    ];

    public static function options(): array
    {
        return array_map(fn($item) => [
            'id'   => $item->value,
            'name' => $item->getDescription($item->value),
        ], self::cases());
    }
}
