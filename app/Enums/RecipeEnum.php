<?php

namespace App\Enums;

/**
 * 处方
 *
 */
enum RecipeEnum: int
{
    use EnumToolTrait;

    // 开具处方中使用的单位类型
    public const array RECIPE_UNIT_TYPE = [
        ItemUnitTypeEnum::UNIT_TYPE_BULK->value,
        ItemUnitTypeEnum::UNIT_TYPE_PACK->value,
    ];

    // 开具处方中使用的用药频次
    public const array RECIPE_DRUG_TIMES = [
        1 => '1次/天',
        2 => '2次/天',
        3 => '3次/天',
        4 => '4次/天',
        5 => '5次/天',
        6 => '6次/天',
    ];

    // 处方支付状态
    public const array RECIPE_IS_PAID = [
        0 => '未付款',
        1 => '已付款',
        2 => '已下单',
    ];
}
