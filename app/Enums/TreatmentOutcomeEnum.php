<?php

namespace App\Enums;

use App\Logics\V1\TransferLogic;

/**
 * 就诊结果
 * 关联表：his_treatment_outcome，此处定义为了方便程序中特殊使用
 */
enum TreatmentOutcomeEnum: int
{
    use EnumToolTrait;

    // 治愈中，办理住院使用
    case Inpatient = 2;

    // 转院
    case TransferToAnotherHospital = 3;

    // 院内转诊
    case TransferWithinHospital = 8;


    // 诊断结果对应的回调方法
    public const array TREATMENT_OUTCOME_CALLBACK = [
        self::TransferToAnotherHospital->value => [TransferLogic::class, 'TransferToAnotherHospital'],
        self::TransferWithinHospital->value    => [TransferLogic::class, 'TransferWithinHospital'],
    ];
}
