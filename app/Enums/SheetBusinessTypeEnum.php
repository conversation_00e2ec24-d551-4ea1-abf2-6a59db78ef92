<?php

namespace App\Enums;

/**
 * 支付单总的业务类型
 */
enum SheetBusinessTypeEnum: string
{
    use EnumToolTrait;

    //医疗 洗美 零售
    case Registration = 'registration';
    case Recipe = 'recipe';
    case Retail = 'retail';
    case Beauty = 'beauty';

    public const array DESCRIPTIONS = [
        self::Registration->value => '挂号',
        self::Recipe->value       => '处方',
        self::Retail->value       => '零售',
        self::Beauty->value       => '洗美',
    ];

    /**
     * 是否允许非业务单发起者的编辑
     *
     * @param string $businessType
     *
     * @return bool
     */
    public static function EditAble(string $businessType): bool
    {
        return in_array($businessType, [
            self::Retail->value,
            self::Beauty->value,
        ]);
    }

    /**
     * 是否允许非业务单发起者的删除
     *
     * @param string $businessType
     *
     * @return bool
     */
    public static function DeleteAble(string $businessType): bool
    {
        return in_array($businessType, [
            self::Retail->value,
            self::Beauty->value,
        ]);
    }

}
