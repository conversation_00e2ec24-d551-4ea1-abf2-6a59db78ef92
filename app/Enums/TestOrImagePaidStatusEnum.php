<?php

namespace App\Enums;

/**
 * 化验、影像支付状态
 * his_tests.is_paid、his_images.is_paid
 *
 * Class TestOrImagePaidStatusEnum
 * @package App\Enums
 */
enum TestOrImagePaidStatusEnum: int
{
    use EnumToolTrait;

    case Unpaid = 0;

    case Paid = 1;

    public const array DESCRIPTIONS = [
        0 => '未支付',
        1 => '已支付',
        2 => '未支付',
    ];

    /**
     * 所有可选项（用于前端筛选）
     *
     * @return array
     */
    public static function options(): array
    {
        return array_map(fn($item) => [
            'id'   => $item->value,
            'name' => $item->getDescription($item->value),
        ], self::cases());
    }
}
