<?php

namespace App\Enums;

use App\Models\ItemModel;
use App\Models\ItemBeautyModel;

/**
 * 商品中心:商品大类型
 * 对应item_type表
 */
enum ItemTypeEnum: int
{
    use EnumToolTrait;

    // 单品
    case Sku = 1;

    // 组合商品
    case Spu = 2;

    // 挂号商品
    case Registration = 3;

    // 洗美商品
    case Beauty = 4;

    // 服务套餐
    case Package = 5;

    // TODO
    public const array SUIT_ITEM_TYPE_RELATION_MODEL = [
        self::Sku->value    => ItemModel::class,
        self::Beauty->value => ItemBeautyModel::class,
    ];
}
