<?php

namespace App\Enums;

/**
 * redis存储KEY枚举
 */
enum RedisKeyEnum: string
{

    use EnumToolTrait;

    case UserVerifyCodeError = 'user_sms_verify_error_times:';
    case UserPasswordError = 'user_password_error_times:';

    public static function GetUserVerifyCodeErrorKey(string $phone): string
    {
        return self::UserVerifyCodeError->value . $phone;
    }

    public static function GetUserPasswordErrorKey(string $phone): string
    {
        return self::UserPasswordError->value . $phone;
    }
}
