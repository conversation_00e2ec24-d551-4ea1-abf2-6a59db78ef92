<?php

namespace App\Enums;

/**
 * 用户画像类型枚举
 */
enum MemberProfileTypeEnum: string
{
    use EnumToolTrait;

    case Consume = 'consume_level';

    case Personality = 'personality_level';

    /**
     * 画像类型的中文描述
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        'consume_level'     => '消费画像',
        'personality_level' => '性格画像',
    ];

    /**
     * 根据画像类型名称获取对应的字段名
     *
     * @param string $caseName 画像类型名称（如 'Consume'）或值（如 'consume_level'）
     *
     * @return string 对应的字段名
     */
    public static function getCaseNameRelationFieldName(string $caseName): string
    {
        // 尝试将输入视为枚举名称（如 'Consume'）
        foreach (self::cases() as $case)
        {
            if ($case->name === $caseName)
            {
                return $case->value;
            }
        }

        // 如果既不是有效的名称，则返回空字符串
        return '';
    }
}

