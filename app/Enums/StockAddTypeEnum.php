<?php

namespace App\Enums;

/**
 * 仓储-库存动态-增加类型
 * 对应stock_add_type表，is_sub = 0
 */
enum StockAddTypeEnum: int
{
    use EnumToolTrait;

    case Purchase = 1;

    case InventoryProfit = 2;

    case OutpatientReturn = 3;

    case RetailReturn = 4;

    case WholeToScattered = 5;

    case OtherReturn = 6;

    case BeautyReturn = 7;

    case TransferShelf = 8;

    case Other = 9;

    case Transfer = 10;

    case Oversold = 11;

    case InventoryProfitFromInventory = 21;

    case TransferShelfFromInventory = 81;

    public const array DESCRIPTIONS = [
        1  => '采购入库上架',
        2  => '盘赢上架',
        3  => '门诊/住院退货上架',
        4  => '零售退货上架',
        5  => '整装拆散装上架',
        6  => '退货其他上架',
        7  => '美容洗澡退货上架',
        8  => '转移货架上架',
        9  => '其他上架',
        10 => '调拨上架',
        11 => '超卖入库',
        21 => '盘点单盘盈上架',
        81 => '盘点单转移货架上架',
    ];

    /**
     * 入库类型是否必须填写子类型
     *
     * @param int $type
     *
     * @return bool
     */
    public static function typeIsMustSubType(int $type): bool
    {
        return in_array($type, [self::InventoryProfit->value, self::TransferShelf->value]);
    }

    /**
     * 入库类型是否必须填写备注
     *
     * @param int $type
     *
     * @return bool
     */
    public static function typeIsMustRemark(int $type): bool
    {
        return in_array($type, self::values());
    }

    /**
     * 入库类型需要计算加权成本价
     *
     * @return array
     */
    public static function getNeedWeightedPrice(): array
    {
        return [self::Purchase->value, self::Transfer->value];
    }
}
