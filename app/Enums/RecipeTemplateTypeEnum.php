<?php

namespace App\Enums;

/**
 * 处方模版类型
 * Class RecipeTemplateEnum
 * @package App\Enums
 */
enum RecipeTemplateTypeEnum: int
{

    use EnumToolTrait;

    case Personal = 1;

    case Hospital = 2;

    case Group = 3;

    public const array DESCRIPTIONS = [
        self::Personal->value => '个人模版',
        self::Hospital->value => '本院模版',
        self::Group->value    => '集团模版',
    ];

    public static function options(): array
    {
        return array_map(fn($item) => [
            'id'   => $item->value,
            'name' => $item->getDescription($item->value),
        ], self::cases());
    }
}
