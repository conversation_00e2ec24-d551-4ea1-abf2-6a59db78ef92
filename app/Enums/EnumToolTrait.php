<?php

namespace App\Enums;

use UnitEnum;

trait EnumToolTrait
{

    /**
     * 获取所有枚举名称
     *
     * @return array
     */
    public static function names(): array
    {
        return array_column(self::cases(), 'name');
    }

    /**
     * 获取所有枚举值
     *
     * @return array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * 枚举值到枚举选项
     *
     * @param mixed $value
     *
     * @return mixed
     */
    public static function valueToCase(mixed $value): mixed
    {
        if (self::isEnum($value))
        {
            return $value;
        }

        return array_find(self::cases(), fn($case) => $value == $case->value);
    }

    /**
     * 转枚举项目为选项
     *
     * @param string $key
     * @param string $value
     *
     * @return array
     */
    public static function caseToOptions(string $key = 'id', string $value = 'name'): array
    {
        return array_map(fn($item) => [
            $key   => $item->value,
            $value => $item->getDescription($item->value),
        ], self::cases());
    }

    /**
     * 检查某个值是否存在于枚举中
     *
     * @param mixed $value
     *
     * @return bool
     */
    public static function exists(mixed $value): bool
    {
        return in_array($value, array_column(self::cases(), 'value'));
    }

    /**
     * 检查某个值是否不存在于枚举中
     *
     * @param mixed $value
     *
     * @return bool
     */
    public static function notExists(mixed $value): bool
    {
        return !in_array($value, array_column(self::cases(), 'value'));
    }

    /**
     * 检查变量是否为Enum
     *
     * @see https://www.php.net/manual/zh/class.unitenum.php
     *
     * @param mixed $variable
     *
     * @return bool
     */
    private static function isEnum(mixed $variable): bool
    {
        return $variable instanceof UnitEnum;
    }

    /**
     * 获取枚举值的描述
     *
     * @param mixed $value 枚举值或枚举实例
     *
     * @return string 描述文本
     */
    public static function getDescription(mixed $value): string
    {
        // 如果是枚举实例，获取其值
        $actualValue = self::isEnum($value) ? $value->value : $value;

        // 检查是否定义了DESCRIPTIONS常量
        if (defined('static::DESCRIPTIONS'))
        {
            $descriptions = constant('static::DESCRIPTIONS');

            return $descriptions[$actualValue] ?? '';
        }

        // 返回空字符串作为默认值
        return '';
    }
}
