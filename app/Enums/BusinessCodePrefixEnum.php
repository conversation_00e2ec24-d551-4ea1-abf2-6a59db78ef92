<?php

namespace App\Enums;

/**
 * 业务编码前缀
 */
enum BusinessCodePrefixEnum: string
{
    // 宠物病历号前缀
    case PET_NUMBER = 'PN';

    // 门诊号挂号前缀
    case MZGH = '10';

    // 门诊前缀
    case MZJL = '11';

    // 门诊病历
    case MZBL = '12';

    // 门诊处方
    case MZCF = '13';

    // 门诊化验
    case MZHY = '14';

    // 门诊影像
    case MZYX = '15';

    // 门诊处置
    case MZCZ = '16';

    // 院内转诊
    case YNZZ = '17';

    // 住院手续
    case ZYSX = '18';

    // 住院病历
    case ZYBL = '19';

    // 内部转院
    case NBZY = '20';

    // 采购单号
    case CGDH = 'CG';

    // 到货单号
    case DHDH = 'DH';

    // 耗材领用单
    case HCLY = 'HC';

    // 充值购买单
    case CZGMD = 'CZ';

    // 洗美购买单
    case XMGMD = 'XM';

    // 零售购买单
    case LSGMD = 'LS';

}

