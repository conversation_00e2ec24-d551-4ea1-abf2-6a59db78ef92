<?php

namespace App\Enums;

/**
 * 仓储-待出库单-出库状态
 */
enum StockPendingOrderStatusEnum: int
{
    use EnumToolTrait;

    // 自动出库中（待出库）
    case PendingDispatch = 0;

    case  CompletedDispatch = 1;

    case Refunded = 2;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        0 => '自动出库中',
        1 => '已自动出库',
        2 => '已退款',
    ];
}
