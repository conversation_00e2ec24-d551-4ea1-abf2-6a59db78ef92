<?php

namespace App\Enums;

/**
 * 商品单位类型
 *
 * 整、散
 *
 */
enum ItemUnitTypeEnum: int
{
    use EnumToolTrait;

    // 散
    case UNIT_TYPE_BULK = 1;

    // 整
    case UNIT_TYPE_PACK = 2;

    // 整装、散装单位对应价格不同字段
    public const array UNIT_TYPE_PRICE_FIELD = [
        self::UNIT_TYPE_BULK->value => 'bulk_sale_price',
        self::UNIT_TYPE_PACK->value => 'pack_sale_price',
    ];

    // 零售可包含的单位
    public const array RETAIL_UNIT_TYPE = [
        self::UNIT_TYPE_BULK->value,
        self::UNIT_TYPE_PACK->value,
    ];

    // 洗美商品默认单位
    public static function getBeautyDefaultUnit(): int
    {
        return self::UNIT_TYPE_BULK->value;
    }

    // 零售默认使用单位
    public static function getRetailDefaultUnit(): int
    {
        return self::UNIT_TYPE_BULK->value;
    }
}
