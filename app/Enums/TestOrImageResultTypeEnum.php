<?php

namespace App\Enums;

/**
 * 化验、影像支持的结果类型
 *
 * Class TestOrImageStatusEnum
 * @package App\Enums
 */
enum TestOrImageResultTypeEnum: string
{

    use EnumToolTrait;

    // Text
    case Text = 'text';

    // Image
    case Image = 'image';

    // File
    case File = 'file';

    // Template
    case TemplateList = 'templateList';

    // TemplateDetail
    case TemplateDetail = 'templateDetail';

    public const array DESCRIPTIONS = [
        self::Text->value           => '文字结果',
        self::Image->value          => '图片结果',
        self::File->value           => '文件结果',
        self::TemplateList->value   => '模版列表',
        self::TemplateDetail->value => '模版详情',
    ];
}
