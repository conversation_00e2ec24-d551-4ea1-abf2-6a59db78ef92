<?php

namespace App\Enums;

/**
 * 医院角色枚举
 * Class HospitalRoleEnum
 * @package App\Enums
 */
class HospitalRoleEnum
{
    /*
     * 院长角色
     */
    const int HOSPITAL_MANAGE_ROLE_ID = 1;

    /*
     * 医生角色
     */
    const int HOSPITAL_DOCTOR_ROLE_ID = 2;

    /*
     * 美容师角色
     */
    const int HOSPITAL_BEAUTY_ROLE_ID = 3;

    /*
     * 前台收银角色
     */
    const int HOSPITAL_CASHIER_ROLE_ID = 4;

    /*
     * 医生助理角色
     */
    const int HOSPITAL_DOCTOR_HELPER_ROLE_ID = 5;

    /*
     * 收银助手角色
     */
    const int HOSPITAL_CASHIER_HELPER_ROLE_ID = 6;

    /*
     * 总部运营角色
     */
    const int HEAD_OFFICE_OPERATE_ROLE_ID = 11;

    /*
     * 总部采购角色
     */
    const int HEAD_OFFICE_PROCUREMENT_ROLE_ID = 12;

    /*
     * 总部技术角色
     */
    const int HEAD_OFFICE_DEVELOP_ROLE_ID = 13;

    /*
     * 大区经理角色
     */
    const int AREA_MANAGER_ROLE_ID = 14;

    // 医院展示人员相关，过滤拥有以下角色人
    const array HEAD_OFFICE_ROLE_GROUP_IDS = [
        self::HEAD_OFFICE_OPERATE_ROLE_ID,
        self::HEAD_OFFICE_PROCUREMENT_ROLE_ID,
        self::HEAD_OFFICE_DEVELOP_ROLE_ID,
        self::AREA_MANAGER_ROLE_ID,
    ];
}
