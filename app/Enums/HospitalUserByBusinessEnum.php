<?php

namespace App\Enums;

/**
 * 医院不同业务下可见用户
 * Class HospitalUserByBusinessEnum
 * @package App\Enums
 */
enum HospitalUserByBusinessEnum: string
{
    use EnumToolTrait;

    // 类型：医生
    case Doctor = 'doctor';

    // 类型：检测员
    case Tester = 'tester';

    // 类型：销售员
    case Sales = 'sales';

    // 类型：处方跟诊
    case Assistant = 'assistant';

    // 类型：美容师
    case Beautician = 'beautician';

    // 业务类型
    public const array HOSPITAL_BUSINESS_ROLE_LISTS = [
        // 医生：医生角色
        self::Doctor->value    => [
            'role_ids'         => [
                HospitalRoleEnum::HOSPITAL_DOCTOR_ROLE_ID,
            ],
            'exclude_role_ids' => HospitalRoleEnum::HEAD_OFFICE_ROLE_GROUP_IDS,
            'default_option'   => [],
        ],

        // 检测员：医生、助理角色
        self::Tester->value    => [
            'role_ids'         => [
                HospitalRoleEnum::HOSPITAL_DOCTOR_ROLE_ID,
                HospitalRoleEnum::HOSPITAL_DOCTOR_HELPER_ROLE_ID
            ],
            'exclude_role_ids' => HospitalRoleEnum::HEAD_OFFICE_ROLE_GROUP_IDS,
            'default_option'   => [],
        ],

        // 销售员
        self::Sales->value     => [
            'role_ids'         => [],
            'exclude_role_ids' => HospitalRoleEnum::HEAD_OFFICE_ROLE_GROUP_IDS,
            'default_option'   => [],
        ],

        // 处方跟诊
        self::Assistant->value => [
            'role_ids'         => [
                HospitalRoleEnum::HOSPITAL_DOCTOR_ROLE_ID,
                HospitalRoleEnum::HOSPITAL_DOCTOR_HELPER_ROLE_ID
            ],
            'exclude_role_ids' => HospitalRoleEnum::HEAD_OFFICE_ROLE_GROUP_IDS,
            'default_option'   => [],
        ],

        // 美容师
        self::Beautician->value    => [
            'role_ids'         => [
                HospitalRoleEnum::HOSPITAL_BEAUTY_ROLE_ID,
            ],
            'exclude_role_ids' => HospitalRoleEnum::HEAD_OFFICE_ROLE_GROUP_IDS,
            'default_option'   => [],
        ],
    ];

    /**
     * 获取业务类型对应的角色列表
     *
     * @param string $businessType
     *
     * @return array
     */
    public static function getBusinessRoleList(string $businessType): array
    {
        return self::HOSPITAL_BUSINESS_ROLE_LISTS[$businessType] ?? [];
    }
}

