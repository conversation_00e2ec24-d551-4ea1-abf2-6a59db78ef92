<?php

namespace App\Enums;

/**
 * 项目类型
 * 对应item_sale_type表
 */
enum ItemSaleTyeEnum: int
{
    use EnumToolTrait;

    // 一级项目类型
    // 普通商品、药品
    case FirstDrug = 1;

    // 化验检查项目
    case FirstTest = 2;

    // 影像检查项目
    case FirstImage = 3;

    // 医疗处置项目
    case FirstNurses = 4;

    // 美容洗护项目
    case FistBeauty = 5;

    // 二级项目类型
    // 商品
    case SecondItem = 10;

    // 处方中可开具的一级项目类型
    public const array RECIPE_FIRST_SALE_TYPE = [
        self::FirstDrug->value,
        self::FirstTest->value,
        self::FirstImage->value,
        self::FirstNurses->value,
    ];

    // 洗美可开具的一级项目类型
    public const array BEAUTY_SALE_TYPE = [
        self::FistBeauty->value,
    ];

    // 零售可开的一级项目类型
    public const array RETAIL_SALE_TYPE = [
        self::FirstDrug->value,
    ];
}
