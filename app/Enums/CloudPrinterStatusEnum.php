<?php

namespace App\Enums;

/**
 * 佳博云打印机状态枚举
 */
enum CloudPrinterStatusEnum: int
{
    use EnumToolTrait;

    case Online = 0;
    case Normal = 1;
    case OutOfPaper = 2;
    case OtherError = 3;
    case OverHeat = 4;
    case Opened = 5;
    case Paused = 8;
    case Printing = 9;

    public const array DESCRIPTIONS = [
        self::Online->value     => '上线',
        self::Normal->value     => '正常',
        self::OutOfPaper->value => '缺纸',
        self::OtherError->value => '其他异常',
        self::OverHeat->value   => '过热',
        self::Opened->value     => '开盖',
        self::Paused->value     => '暂停',
        self::Printing->value   => '打印中',
    ];
}
