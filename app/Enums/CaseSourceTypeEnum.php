<?php

namespace App\Enums;

/**
 * 病历来源
 */
enum CaseSourceTypeEnum: int
{
    use EnumToolTrait;

    case Outpatient = 1;

    case Inpatient = 2;

    public const array DESCRIPTIONS = [
        1 => '门诊',
        2 => '住院',
    ];

    /**
     * 根据来源类型判断是否为门诊病历
     *
     * @param int $sourceType
     *
     * @return bool
     * @noinspection PhpUnused
     */
    public static function getCaseIsOutpatientBySourceType(int $sourceType): bool
    {
        return self::Outpatient->value == $sourceType;
    }

    /**
     * 根据来源类型判断是否为住院病历
     *
     * @param int $sourceType
     *
     * @return bool
     * @noinspection PhpUnused
     */
    public static function getCaseIsInpatientBySourceType(int $sourceType): bool
    {
        return self::Inpatient->value == $sourceType;
    }
}
