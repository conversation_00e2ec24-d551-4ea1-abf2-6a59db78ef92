<?php

namespace App\Http\Requests\Form;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Enums\MemberGenderEnum;

class MemberRequest extends FormRequest
{
    /**
     * 禁用自动验证
     *
     * @var bool
     */
    protected $stopOnFirstFailure = false;

    /**
     * 禁用自动验证
     *
     * @return void
     */
    public function validateResolved()
    {
        // 不执行任何验证，留给控制器手动处理
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 手动执行验证并返回验证器实例
     *
     * @return Validator
     */
    public function getValidator(): Validator
    {
        return $this->getValidatorInstance();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'phone'        => ['required', 'integer', 'digits:11'],
            'name'         => ['required', 'string', 'between:2,20'],
            'gender'       => ['required', 'integer', Rule::in(MemberGenderEnum::values())],
            'birthday'     => ['sometimes', 'nullable', 'date'],
            'avatar'       => ['sometimes', 'string'],
            'wechatNumber' => ['sometimes', 'string'],
            'fromId'       => ['required', 'integer'],
            'email'        => ['sometimes', 'email'],
            'otherMobile1' => ['sometimes', 'integer', 'digits:11'],
            'otherMobile2' => ['sometimes', 'integer', 'digits:11'],
            'telephone'    => ['sometimes', 'string'],
            'provinceId'   => ['sometimes', 'integer'],
            'cityId'       => ['sometimes', 'integer'],
            'areaId'       => ['sometimes', 'integer'],
            'streetId'     => ['sometimes', 'integer'],
            'address'      => ['sometimes', 'string'],
            'remark'       => ['sometimes', 'string'],
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'phone.required'       => '手机号为必填',
            'phone.integer'        => '手机号无效',
            'phone.digits'         => '手机号无效',
            'name.required'        => '姓名为必填',
            'name.string'          => '姓名无效',
            'name.between'         => '姓名长度错误',
            'gender.required'      => '性别为必填',
            'gender.integer'       => '性别无效',
            'gender.in'            => '性别无效',
            'birthday.date'        => '生日无效',
            'avatar.string'        => '头像无效',
            'wechatNumber.string'  => '微信无效',
            'fromId.required'      => '渠道来源为必填',
            'fromId.integer'       => '渠道来源无效',
            'email.email'          => '邮箱无效',
            'otherMobile1.integer' => '其他手机号1无效',
            'otherMobile1.digits'  => '其他手机号1无效',
            'otherMobile2.integer' => '其他手机号2无效',
            'otherMobile2.digits'  => '其他手机号2无效',
            'telephone.string'     => '座机无效',
            'provinceId.integer'   => '省份无效',
            'cityId.integer'       => '城市无效',
            'areaId.integer'       => '区县无效',
            'streetId.integer'     => '街道无效',
            'address.string'       => '地址无效',
            'remark.string'        => '备注无效',
            'remark.nullable'      => '备注无效',
        ];
    }
}
