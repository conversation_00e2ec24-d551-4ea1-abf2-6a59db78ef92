<?php

namespace App\Http\Requests\Form;

use App\Enums\PetGenderEnum;
use App\Enums\PetPreventStatusEnum;
use App\Enums\PetSterileStatusEnum;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PetRequest extends FormRequest
{
    /**
     * 禁用自动验证
     *
     * @var bool
     */
    protected $stopOnFirstFailure = false;

    /**
     * 禁用自动验证
     *
     * @return void
     */
    public function validateResolved()
    {
        // 不执行任何验证，留给控制器手动处理
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 手动执行验证并返回验证器实例
     *
     * @return Validator
     */
    public function getValidator(): Validator
    {
        return $this->getValidatorInstance();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'petName'           => ['required_without:petUid', 'between:1,20'], // 编辑下非必填
            'petGender'         => ['required_without:petUid', 'integer', Rule::in(PetGenderEnum::values())], // 编辑下非必填
            'petCategoryId'     => ['required_without:petUid', 'integer', 'min:1'], // 编辑下非必填
            'petIcon'           => ['sometimes', 'nullable', 'string'],
            'petBirthday'       => ['sometimes', 'nullable'],
            'petBreedId'        => ['sometimes', 'nullable', 'integer'],
            'petBreedDesc'      => ['sometimes', 'nullable', 'string', 'between:0,50'],
            'petWeight'         => ['sometimes', 'nullable', 'numeric', 'between:0,100'],
            'petBloodId'        => ['sometimes', 'nullable', 'integer'],
            'petSterileStatus'  => ['sometimes', 'nullable', 'integer', Rule::in(PetSterileStatusEnum::values())],
            'petSterileDate'    => ['sometimes', 'nullable', 'date'],
            'petPreventStatus'  => ['sometimes', 'nullable', 'integer', Rule::in(PetPreventStatusEnum::values())],
            'petColorId'        => ['required_without:petUid', 'integer', 'min:1'], // 编辑下非必填
            'petColorDesc'      => ['sometimes', 'nullable', 'string', 'between:0,50'],
            'petRecordNumber'   => ['sometimes', 'nullable', 'string'],
            'petLiveStatus'     => ['required_without:petUid', 'integer', 'min:1'], // 编辑下非必填
            'petIrritateRemark' => ['sometimes', 'nullable', 'string', 'between:0,100'],
            'remark'            => ['sometimes', 'nullable', 'string', 'between:0,100'],
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'petName.required'          => '宠物名称必选参数错误',
            'petName.between'           => '宠物名称长度错误',
            'petGender.required'        => '宠物性别参数错误',
            'petGender.integer'         => '宠物性别参数类型错误',
            'petGender.in'              => '宠物性别参数值错误',
            'petCategoryId.required'    => '宠物分类参数错误',
            'petCategoryId.integer'     => '宠物分类参数类型错误',
            'petCategoryId.min'         => '宠物分类参数值错误',
            'petBirthday.date'          => '宠物生日参数错误',
            'petId.required'            => '宠物ID必选参数错误',
            'petId.integer'             => '宠物ID参数类型错误',
            'petId.min'                 => '宠物ID参数值错误',
            'petLiveStatus.required'    => '宠物生存状态参数错误',
            'petLiveStatus.integer'     => '宠物生存状态参数类型错误',
            'petLiveStatus.min'         => '宠物生存状态参数值错误',
            'remark.between'            => '宠物备注参数长度错误',
            'petRecordNumber.string'    => '宠物病历号参数类型错误',
            'petWeight.numeric'         => '宠物体重参数类型错误',
            'petWeight.between'         => '宠物体重参数值错误',
            'petBloodId.integer'        => '宠物血型参数类型错误',
            'petBloodId.min'            => '宠物血型参数值错误',
            'petSterileStatus.integer'  => '宠物绝育参数类型错误',
            'petSterileStatus.in'       => '宠物绝育参数值错误',
            'petSterileDate.date'       => '宠物绝育时间参数错误',
            'petPreventStatus.integer'  => '宠物免疫参数类型错误',
            'petPreventStatus.in'       => '宠物免疫参数值错误',
            'petColorId.required'       => '宠物颜色参数错误',
            'petColorId.integer'        => '宠物颜色参数类型错误',
            'petColorId.min'            => '宠物颜色参数值错误',
            'petColorDesc.between'      => '宠物颜色描述参数长度错误',
            'petColorDesc.string'       => '宠物颜色描述参数类型错误',
            'petColorDesc.nullable'     => '宠物颜色描述参数错误',
            'petBreedId.integer'        => '宠物品种参数类型错误',
            'petBreedId.required'       => '宠物品种参数错误',
            'petBreedId.min'            => '宠物品种参数值错误',
            'petIrritateRemark.between' => '宠物过敏备注参数长度错误',
            'petIrritateRemark.string'  => '宠物过敏备注参数类型错误',
            'remark.string'             => '宠物备注参数类型错误',
            'remark.nullable'           => '宠物备注参数错误',
        ];
    }
}
