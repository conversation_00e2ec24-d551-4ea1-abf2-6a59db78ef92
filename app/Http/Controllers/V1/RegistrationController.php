<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Logics\V1\PetLogic;
use App\Logics\V1\MemberLogic;
use App\Logics\V1\RegistrationTypeLogic;
use App\Logics\V1\RegistrationReasonLogic;
use App\Logics\V1\RegistrationLogic;

/**
 * 挂号相关
 * Class BasicEnumDataController
 * @package      App\Http\Controllers\V1
 *
 * @noinspection PhpUnused
 */
class RegistrationController extends Controller
{

    /**
     * 获取挂号原因
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetRegistrationReasonsOptions(): JsonResponse
    {
        $getRegistrationReasonRes = RegistrationReasonLogic::GetRegistrationReasonOptions();
        if ($getRegistrationReasonRes->isFail())
        {
            return outputJsonError($getRegistrationReasonRes->getCode(),
                                   $getRegistrationReasonRes->getMessage(),
                                   ['error' => $getRegistrationReasonRes->getMessage()]);
        }

        return outputJsonResult($getRegistrationReasonRes->getData());
    }

    /**
     * 获取挂号类型
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetRegistrationTypeOptions(): JsonResponse
    {
        $getRegistrationTypeRes = RegistrationTypeLogic::GetRegistrationTypeOptions();
        if ($getRegistrationTypeRes->isFail())
        {
            return outputJsonError($getRegistrationTypeRes->getCode(),
                                   $getRegistrationTypeRes->getMessage(),
                                   ['error' => $getRegistrationTypeRes->getMessage()]);
        }

        return outputJsonResult($getRegistrationTypeRes->getData());
    }

    /**
     * 获取医院下医生
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetHospitalDoctorOptions(): JsonResponse
    {
        // TODO 获取医生角色ID
        $getHospitalDoctorRes = RegistrationLogic::GetHospitalRegistrationUsers(getPublicParamsHospitalId());

        if ($getHospitalDoctorRes->isFail())
        {
            return outputJsonError($getHospitalDoctorRes->getCode(),
                                   $getHospitalDoctorRes->getMessage(),
                                   ['error' => $getHospitalDoctorRes->getMessage()]);
        }

        return outputJsonResult($getHospitalDoctorRes->getData());
    }

    /**
     * 获取医院下美容师
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetHospitalBeautyOptions(): JsonResponse
    {
        // TODO 获取美容师角色ID
        $getHospitalDoctorRes = RegistrationLogic::GetHospitalRegistrationUsers(getPublicParamsHospitalId());

        if ($getHospitalDoctorRes->isFail())
        {
            return outputJsonError($getHospitalDoctorRes->getCode(),
                                   $getHospitalDoctorRes->getMessage(),
                                   ['error' => $getHospitalDoctorRes->getMessage()]);
        }

        return outputJsonResult($getHospitalDoctorRes->getData());
    }

    /**
     * 获取会员挂号宠物列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Exception
     * @noinspection PhpUnused
     */
    public function GetRegistrationPetLists(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make($request->post(),
                                     ['memberUid' => ['required', 'string', 'between:8,64']],
                                     [
                                         'memberUid.required' => '用户UID，必选参数错误',
                                         'memberUid.string'   => '用户UID，参数类型错误',
                                         'memberUid.between'  => '用户UID，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取有效会员信息
        $memberUid    = $validator->validated()['memberUid'];
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
        if ($getMemberRes->isFail())
        {
            return outputJsonError($getMemberRes->getCode(),
                                   $getMemberRes->getMessage(),
                                   ['error' => $getMemberRes->getMessage()]);
        }

        $memberId = $getMemberRes->getData('id', 0);
        if (empty($memberId))
        {
            return outputJsonError(30002, '会员不存在', ['error' => '会员不存在']);
        }

        $getRegistrationPetListRes = RegistrationLogic::GetRegistrationPetList($memberId, getPublicParamsHospitalId());
        if ($getRegistrationPetListRes->isFail())
        {
            return outputJsonError($getRegistrationPetListRes->getCode(),
                                   $getRegistrationPetListRes->getMessage(),
                                   ['error' => $getRegistrationPetListRes->getMessage()]);
        }

        return outputJsonResult($getRegistrationPetListRes->getData());
    }

    /**
     * 获取挂号优惠
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetRegistrationDiscount(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make($request->post(),
                                     [
                                         'memberUid'            => ['required', 'string', 'between:8,64'],
                                         'petUid'               => ['required', 'string', 'between:8,64'],
                                         'registrationReasonId' => ['required', 'int', 'min:1'],
                                         'registrationTypeId'   => ['required', 'int', 'min:1'],
                                     ],
                                     [
                                         'memberUid.required'            => '用户UID，必选参数错误',
                                         'memberUid.string'              => '用户UID，参数类型错误',
                                         'memberUid.between'             => '用户UID，参数长度错误',
                                         'petUid.required'               => '宠物UID，必选参数错误',
                                         'petUid.string'                 => '宠物UID，参数类型错误',
                                         'petUid.between'                => '宠物UID，参数长度错误',
                                         'registrationReasonId.required' => '挂号原因ID，必选参数错误',
                                         'registrationReasonId.int'      => '挂号原因ID，参数类型错误',
                                         'registrationReasonId.min'      => '挂号原因ID，参数值错误',
                                         'registrationTypeId.required'   => '挂号类型ID，必选参数错误',
                                         'registrationTypeId.int'        => '挂号类型ID，参数类型错误',
                                         'registrationTypeId.min'        => '挂号类型ID，参数值错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 参数
        $params = $validator->validated();

        // 获取会员信息
        $memberUid    = $params['memberUid'];
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
        if ($getMemberRes->isFail())
        {
            return outputJsonError($getMemberRes->getCode(),
                                   $getMemberRes->getMessage(),
                                   ['error' => $getMemberRes->getMessage()]);
        }

        // 获取宠物信息
        $petUid    = $params['petUid'];
        $getPetRes = PetLogic::GetValidPetByIdOrUid(petUid: $petUid);
        if ($getPetRes->isFail())
        {
            return outputJsonError($getPetRes->getCode(),
                                   $getPetRes->getMessage(),
                                   ['error' => $getPetRes->getMessage()]);
        }

        $publicParams               = getRequestReservedParameters();
        $memberId                   = $getMemberRes->getData('id', 0);
        $petId                      = $getPetRes->getData('id', 0);
        $getRegistrationDiscountRes = RegistrationLogic::GetRegistrationDiscount($memberId,
                                                                                 $petId,
                                                                                 $params['registrationReasonId'],
                                                                                 $params['registrationTypeId'],
                                                                                 $publicParams);
        if ($getRegistrationDiscountRes->isFail())
        {
            return outputJsonError($getRegistrationDiscountRes->getCode(),
                                   $getRegistrationDiscountRes->getMessage(),
                                   ['error' => $getRegistrationDiscountRes->getMessage()]);
        }

        return outputJsonResult($getRegistrationDiscountRes->getData());
    }

    /**
     * 挂号
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function AddPetRegistration(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make($request->post(),
                                     [
                                         'memberUid'             => ['required', 'string', 'between:8,64'],
                                         'petUid'                => ['required', 'string', 'between:8,64'],
                                         'registrationReasonId'  => ['required', 'int', 'min:1'],
                                         'registrationTypeId'    => ['required', 'int', 'min:1'],
                                         'registrationDoctorUid' => ['sometimes', 'nullable', 'string', 'between:8,64'],
                                         'petWeight'             => ['sometimes', 'nullable', 'decimal:2'],
                                         'beautyDoctorUid'       => ['sometimes', 'nullable', 'string', 'between:8,64'],
                                         'registrationMode'      => ['required', 'int', 'min:1'],
                                         'transferCode'          => ['sometimes', 'nullable', 'string', 'between:8,64'],
                                     ],
                                     [
                                         'memberUid.required'            => '用户UID，必选参数错误',
                                         'memberUid.string'              => '用户UID，参数类型错误',
                                         'memberUid.between'             => '用户UID，参数长度错误',
                                         'petUid.required'               => '宠物UID，必选参数错误',
                                         'petUid.string'                 => '宠物UID，参数类型错误',
                                         'petUid.between'                => '宠物UID，参数长度错误',
                                         'registrationReasonId.required' => '挂号原因ID，必选参数错误',
                                         'registrationReasonId.int'      => '挂号原因ID，参数类型错误',
                                         'registrationReasonId.min'      => '挂号原因ID，参数值错误',
                                         'registrationTypeId.required'   => '挂号类型ID，必选参数错误',
                                         'registrationTypeId.int'        => '挂号类型ID，参数类型错误',
                                         'registrationTypeId.min'        => '挂号类型ID，参数值错误',
                                         'registrationDoctorUid.string'  => '挂号医生UID，参数类型错误',
                                         'registrationDoctorUid.between' => '挂号医生UID，参数长度错误',
                                         'petWeight.decimal'             => '宠物体重，参数类型错误',
                                         'beautyDoctorUid.string'        => '美容医生UID，参数类型错误',
                                         'beautyDoctorUid.between'       => '美容医生UID，参数长度错误',
                                         'registrationMode.required'     => '挂号模式，必选参数错误',
                                         'registrationMode.int'          => '挂号模式，参数类型错误',
                                         'registrationMode.min'          => '挂号模式，参数值错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 参数
        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 获取会员信息
        $memberUid    = $params['memberUid'];
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
        if ($getMemberRes->isFail())
        {
            return outputJsonError($getMemberRes->getCode(),
                                   $getMemberRes->getMessage(),
                                   ['error' => $getMemberRes->getMessage()]);
        }

        // 获取宠物信息
        $petUid    = $params['petUid'];
        $getPetRes = PetLogic::GetValidPetByIdOrUid(petUid: $petUid);
        if ($getPetRes->isFail())
        {
            return outputJsonError($getPetRes->getCode(),
                                   $getPetRes->getMessage(),
                                   ['error' => $getPetRes->getMessage()]);
        }

        // 添加挂号
        $memberId           = $getMemberRes->getData('id', 0);
        $petId              = $getPetRes->getData('id', 0);
        $getRegistrationRes = RegistrationLogic::AddRegistration($memberId,
                                                                 $petId,
                                                                 $params,
                                                                 $publicParams);
        if ($getRegistrationRes->isFail())
        {
            return outputJsonError($getRegistrationRes->getCode(),
                                   $getRegistrationRes->getMessage(),
                                   ['error' => $getRegistrationRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
