<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use App\Logics\V1\HospitalLogic;
use Illuminate\Http\JsonResponse;

/**
 * 医院相关
 * Class HospitalController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class HospitalController extends Controller
{
    /**
     * 获取支持转院的医院列表
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetTransferHospitalList(): JsonResponse
    {
        $getTransferHospitalListRes = HospitalLogic::getTransferHospitalList(getRequestReservedParameters());
        if ($getTransferHospitalListRes->isFail())
        {
            return outputJsonError($getTransferHospitalListRes->getCode(),
                                   $getTransferHospitalListRes->getMessage(),
                                   ['error' => $getTransferHospitalListRes->getMessage()]);
        }

        return outputJsonResult($getTransferHospitalListRes->getData());
    }
}
