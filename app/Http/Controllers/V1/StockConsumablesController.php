<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Enums\StockConsumablesReceiveOrderOutboundStatusEnum;
use App\Logics\V1\StockConsumablesLogic;
use App\Models\StockConsumablesReceiveOrderModel;

/**
 * 仓储-耗材领用单
 * Class StockConsumablesController
 * @package App\Http\Controllers\V1
 */
class StockConsumablesController extends Controller
{
    /**
     * 耗材领用单列表筛选项
     *
     * @return JsonResponse
     */
    public function GetConsumablesFilterOptions()
    {
        $getCreateUserOptionsRes = StockConsumablesLogic::GetCreateConsumablesUserOptions(getRequestReservedParameters());

        return outputJsonResult(['userOptions' => $getCreateUserOptionsRes->getData()]);
    }

    /**
     * 搜索耗材商品
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function SearchConsumablesItems(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords' => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'keywords' => '搜索关键词',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getSearchItemsRes = StockConsumablesLogic::GetSearchItems($searchParams, $publicParams);
        if ($getSearchItemsRes->isFail())
        {
            return outputJsonError($getSearchItemsRes->getCode(),
                                   $getSearchItemsRes->getMessage(),
                                   ['error' => $getSearchItemsRes->getMessage()]);
        }

        return outputJsonResult($getSearchItemsRes->getData());
    }

    /**
     * 耗材领用单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'       => ['sometimes', 'nullable', 'string'],
                                         'startDate'      => ['sometimes', 'nullable', 'date'],
                                         'endDate'        => ['sometimes', 'nullable', 'date'],
                                         'status'         => ['sometimes', 'nullable', 'integer', Rule::in([0, 1])],
                                         'outboundStatus' => ['sometimes', 'nullable', 'integer', Rule::in(StockConsumablesReceiveOrderOutboundStatusEnum::values())],
                                         'createUserUid'  => ['sometimes', 'nullable', 'string'],
                                         'page'           => ['sometimes', 'nullable', 'integer'],
                                         'count'          => ['sometimes', 'nullable', 'integer'],
                                     ],
                                     [],
                                     [
                                         'keywords'       => '搜索关键词',
                                         'startDate'      => '开始日期',
                                         'endDate'        => '结束日期',
                                         'status'         => '领用单状态',
                                         'outboundStatus' => '出库状态',
                                         'createUserUid'  => '创建人',
                                         'page'           => '页码',
                                         'count'          => '每页条数',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getPurchaseListRes = StockCOnsumablesLogic::GetConsumablesReceiveLists($searchParams, $publicParams);
        if ($getPurchaseListRes->isFail())
        {
            return outputJsonError($getPurchaseListRes->getCode(),
                                   $getPurchaseListRes->getMessage(),
                                   ['error' => $getPurchaseListRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseListRes->getData());
    }

    /**
     * 添加耗材领用单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Add(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'remark' => ['sometimes', 'nullable', 'string'],
                                         'items'  => ['required', 'array'],
                                     ],
                                     [],
                                     [
                                         'remark' => '领用备注',
                                         'items'  => '耗材商品',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $addPurchaseParams = $validator->validated();
        $publicParams      = getRequestReservedParameters();

        $getAddPurchaseRes = StockConsumablesLogic::AddConsumablesReceiveOrder($addPurchaseParams, $publicParams);
        if ($getAddPurchaseRes->isFail())
        {
            return outputJsonError($getAddPurchaseRes->getCode(),
                                   $getAddPurchaseRes->getMessage(),
                                   ['error' => $getAddPurchaseRes->getMessage()]);
        }

        return outputJsonResult($getAddPurchaseRes->getData());
    }

    /**
     * 编辑耗材领用单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Edit(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'receiveCode' => ['required', 'string'],
                                         'remark'      => ['sometimes', 'nullable', 'string'],
                                         'items'       => ['required', 'array'],
                                     ],
                                     [],
                                     [
                                         'receiveCode' => '领用单编码',
                                         'remark'      => '领用备注',
                                         'items'       => '耗材商品',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取参数
        $receiveCode = $validator->validated()['receiveCode'];

        // 获取领用单信息
        $getStockConsumablesRes = StockConsumablesReceiveOrderModel::getData(where: ['receive_code' => $receiveCode]);
        $getStockConsumablesRes = $getStockConsumablesRes ? current($getStockConsumablesRes) : [];
        if (empty($getStockConsumablesRes))
        {
            return outputJsonError(42010, '采购单不存在', ['error' => '采购单不存在']);
        }

        $editConsumablesParams = $validator->validated();
        $publicParams          = getRequestReservedParameters();
        $getEditConsumablesRes = StockConsumablesLogic::EditConsumablesReceiveOrder($getStockConsumablesRes['id'], $editConsumablesParams, $publicParams);
        if ($getEditConsumablesRes->isFail())
        {
            return outputJsonError($getEditConsumablesRes->getCode(), $getEditConsumablesRes->getMessage(), ['error' => $getEditConsumablesRes->getMessage()]);
        }

        return outputJsonResult($getEditConsumablesRes->getData());
    }

    /**
     * 耗材领用单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function Detail(Request $request): JsonResponse
    {
        $receiveCode = $request->post('receiveCode');
        if (empty($receiveCode))
        {
            return outputJsonError(400, '耗材领用单号不能为空', ['error' => '耗材领用单号不能为空']);
        }

        // 获取耗材领用单信息
        $getStockConsumablesRes = StockConsumablesReceiveOrderModel::getData(where: ['receive_code' => $receiveCode]);
        $getStockConsumablesRes = $getStockConsumablesRes ? current($getStockConsumablesRes) : [];
        if (empty($getStockConsumablesRes))
        {
            return outputJsonError(42010, '耗材领用单不存在', ['error' => '耗材领用单不存在']);
        }

        $getConsumablesDetailRes = StockConsumablesLogic::GetConsumablesReceiveOrderDetail($getStockConsumablesRes['id'], getRequestReservedParameters());
        if ($getConsumablesDetailRes->isFail())
        {
            return outputJsonError($getConsumablesDetailRes->getCode(), $getConsumablesDetailRes->getMessage(), ['error' => $getConsumablesDetailRes->getMessage()]);
        }

        return outputJsonResult($getConsumablesDetailRes->getData());
    }
}
