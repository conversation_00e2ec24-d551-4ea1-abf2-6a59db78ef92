<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use App\Enums\SheetStatusEnum;
use App\Enums\BeautyExecuteStatusEnum;
use App\Logics\V1\UserLogic;
use App\Logics\V1\MemberLogic;
use App\Logics\V1\PetLogic;
use App\Support\Beauty\BeautySheetHelper;
use App\Logics\V1\BeautyServiceLogic;
use App\Logics\V1\BuySheet\BeautyServiceSheetLogic;

class BeautyServiceController extends Controller
{
    /**
     * 搜索洗美服务商品
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function SearchBeautyServiceItems(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keyword' => ['required', 'string'],
                                         'petUid'  => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'keyword' => '搜索关键词',
                                         'petUid'  => '宠物UID',
                                     ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 获取宠物信息
        $petUid    = $searchParams['petUid'];
        $getPetRes = PetLogic::GetValidPetByIdOrUid(petUid: $petUid);
        if ($getPetRes->isFail())
        {
            return outputJsonError($getPetRes->getCode(),
                                   $getPetRes->getMessage(),
                                   ['error' => $getPetRes->getMessage()]);
        }
        $searchParams['petId'] = $getPetRes->getData('id');

        $getSearchItemsRes = BeautyServiceLogic::GetSearchItems($searchParams, $publicParams);
        if ($getSearchItemsRes->isFail())
        {
            return outputJsonError($getSearchItemsRes->getCode(),
                                   $getSearchItemsRes->getMessage(),
                                   ['error' => $getSearchItemsRes->getMessage()]);
        }

        return outputJsonResult($getSearchItemsRes->getData());
    }

    /**
     * 创建美容服务单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function CreateSheet(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'petUid'     => 'required|string|between:8,64',
                'items'      => 'required|array',
                'totalPrice' => 'required|numeric|min:0',
            ],
            [],
            [
                'petUid'     => '宠物UID',
                'items'      => '洗美服务明细',
                'totalPrice' => '总金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $petUid    = $params['petUid'];
        $getPetRes = PetLogic::GetValidPetByIdOrUid(petUid: $petUid);
        if ($getPetRes->isFail())
        {
            return outputJsonError($getPetRes->getCode(), '', ['error' => $getPetRes->getMessage()]);
        }

        $params['petId'] = $getPetRes->getData('id');

        $createResult = BeautyServiceSheetLogic::CreateSheet($params, $publicParams);
        if ($createResult->isFail())
        {
            return outputJsonError($createResult->getCode(),
                                   $createResult->getMessage(),
                                   ['error' => $createResult->getMessage()]);
        }

        return outputJsonResult($createResult->getData());
    }

    /**
     * 编辑美容服务单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function EditSheet(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'sheetCode'  => 'required|string|between:8,32',
                'items'      => 'required|array',
                'totalPrice' => 'required|numeric|min:0',
            ],
            [],
            [
                'sheetCode'  => '洗美服务单编码',
                'items'      => '洗美服务明细',
                'totalPrice' => '总金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $sheetCode    = trim($params['sheetCode']);

        $sheetRes = BeautySheetHelper::GetValidSheet($sheetCode);
        if ($sheetRes->isFail())
        {
            return outputJsonError($sheetRes->getCode(), '', ['error' => $sheetRes->getMessage()]);
        }
        $params['sheetId'] = $sheetRes->getData('id');

        $createResult = BeautyServiceSheetLogic::EditSheet($params, $publicParams);
        if ($createResult->isFail())
        {
            return outputJsonError($createResult->getCode(),
                                   $createResult->getMessage(),
                                   ['error' => $createResult->getMessage()]);
        }

        return outputJsonResult($createResult->getData());
    }

    /**
     * 删除洗美服务单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function DeleteSheet(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'sheetCode' => 'required|string|between:8,32',
            ],
            [],
            [
                'sheetCode' => '洗美服务单编码',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $sheetCode    = trim($params['sheetCode']);

        $sheetRes = BeautySheetHelper::GetValidSheet($sheetCode);
        if ($sheetRes->isFail())
        {
            return outputJsonError($sheetRes->getCode(), '', ['error' => $sheetRes->getMessage()]);
        }
        $params['sheetId'] = $sheetRes->getData('id');

        $createResult = BeautyServiceSheetLogic::DeleteSheet($params, $publicParams);
        if ($createResult->isFail())
        {
            return outputJsonError($createResult->getCode(),
                                   $createResult->getMessage(),
                                   ['error' => $createResult->getMessage()]);
        }

        return outputJsonResult($createResult->getData());
    }

    /**
     * 美容服务单筛选项
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function UnpaidSheetFilterOptions(Request $request): JsonResponse
    {
        $publicParams = getRequestReservedParameters();

        $membersRes = BeautyServiceSheetLogic::GetSheetMemberOptions(SheetStatusEnum::Unpaid->value, $publicParams);
        if ($membersRes->isFail())
        {
            return outputJsonError($membersRes->getCode(), '', ['error' => $membersRes->getMessage()]);
        }

        $usersRes = BeautyServiceSheetLogic::GetCreateUsersOptions(SheetStatusEnum::Unpaid->value, $publicParams);
        if ($usersRes->isFail())
        {
            return outputJsonError($usersRes->getCode(), '', ['error' => $usersRes->getMessage()]);
        }

        return outputJsonResult([
                                    'memberOptions'      => $membersRes->getData(),
                                    'createUsersOptions' => $usersRes->getData(),
                                ]);
    }

    /**
     * 美容服务单待支付列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function UnpaidSheetList(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'page'          => 'sometimes|nullable|integer',
                'count'         => 'sometimes|nullable|integer',
                'memberUid'     => 'sometimes|nullable|string|between:8,64',
                'createUserUid' => 'sometimes|nullable|string|between:8,64',
                'startDate'     => 'sometimes|nullable|date',
                'endDate'       => 'sometimes|nullable|date',
            ],
            [],
            [
                'page'          => '页码',
                'count'         => '每页条数',
                'memberUid'     => '会员UID',
                'createUserUid' => '开单人UID',
                'startDate'     => '最后修改开始日期',
                'endDate'       => '最后修改结束日期',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $memberUid     = trim($params['memberUid'] ?? '');
        $createUserUid = trim($params['createUserUid'] ?? '');
        if (!empty($memberUid))
        {
            $getMemberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
            if ($getMemberRes->isFail())
            {
                return outputJsonError($getMemberRes->getCode(), '', ['error' => $getMemberRes->getMessage()]);
            }
            $params['memberId'] = $getMemberRes->getData('id');
        }
        if (!empty($createUserUid))
        {
            $getUserRes = UserLogic::GetValidUserByIdOrUid(userUid: $createUserUid);
            if ($getUserRes->isFail())
            {
                return outputJsonError($getUserRes->getCode(), '', ['error' => $getUserRes->getMessage()]);
            }
            $params['createUserId'] = $getUserRes->getData('id');
        }

        $unpaidListResult = BeautyServiceSheetLogic::GetUnpaidSheetList($params, $publicParams);
        if ($unpaidListResult->isFail())
        {
            return outputJsonError($unpaidListResult->getCode(),
                                   $unpaidListResult->getMessage(),
                                   ['error' => $unpaidListResult->getMessage()]);
        }

        return outputJsonResult($unpaidListResult->getData());
    }

    /**
     * 洗美服务详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function SheetDetail(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'sheetCode' => 'required|string|between:8,32',
            ],
            [],
            [
                'sheetCode' => '洗美服务单编码',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $detailResult = BeautyServiceSheetLogic::GetSheetDetail($params, $publicParams);
        if ($detailResult->isFail())
        {
            return outputJsonError($detailResult->getCode(),
                                   $detailResult->getMessage(),
                                   ['error' => $detailResult->getMessage()]);
        }

        return outputJsonResult($detailResult->getData());
    }

    /**
     * 美容服务筛选项[已支付]
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function ServiceFilterOptions(Request $request): JsonResponse
    {
        $publicParams = getRequestReservedParameters();

        $usersRes = BeautyServiceSheetLogic::GetCreateUsersOptions(SheetStatusEnum::Paid->value, $publicParams);
        if ($usersRes->isFail())
        {
            return outputJsonError($usersRes->getCode(), '', ['error' => $usersRes->getMessage()]);
        }

        return outputJsonResult([
                                    'createUsersOptions' => $usersRes->getData(),
                                ]);
    }

    /**
     * 洗美服务列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function ServiceList(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'page'          => 'sometimes|nullable|integer',
                'count'         => 'sometimes|nullable|integer',
                'keywords'      => 'sometimes|nullable|string',
                'createUserUid' => 'sometimes|nullable|string|between:8,64',
                'executeStatus' => ['sometimes', 'nullable', new Enum(BeautyExecuteStatusEnum::class)],
                'startDate'     => 'sometimes|nullable|date',
                'endDate'       => 'sometimes|nullable|date',
            ],
            [],
            [
                'page'          => '页码',
                'count'         => '每页条数',
                'createUserUid' => '开单人',
                'executeStatus' => '执行状态',
                'startDate'     => '开单开始日期',
                'endDate'       => '开单结束日期',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $createUserUid = trim($params['createUserUid'] ?? '');
        if (!empty($createUserUid))
        {
            $getUserRes = UserLogic::GetValidUserByIdOrUid(userUid: $createUserUid);
            if ($getUserRes->isFail())
            {
                return outputJsonError($getUserRes->getCode(), '', ['error' => $getUserRes->getMessage()]);
            }
            $params['createUserId'] = $getUserRes->getData('id');
        }

        $serviceListResult = BeautyServiceLogic::GetServiceList($params, $publicParams);
        if ($serviceListResult->isFail())
        {
            return outputJsonError($serviceListResult->getCode(),
                                   $serviceListResult->getMessage(),
                                   ['error' => $serviceListResult->getMessage()]);
        }

        return outputJsonResult($serviceListResult->getData());
    }

    /**
     * 洗美服务执行
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function FollowServiceExecutor(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'sheetCode' => 'required|string|between:8,32',
                                         'executor1' => 'sometimes|nullable|string',
                                         'executor2' => 'sometimes|nullable|string',
                                     ],
                                     [],
                                     [
                                         'sheetCode' => '洗美服务单编码',
                                         'executor1' => '执行人1',
                                         'executor2' => '执行人2',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $executor1Uid = trim($params['executor1'] ?? '');
        $executor2    = trim($params['executor2'] ?? '');
        if (!empty($executor1))
        {
            $userRes = UserLogic::GetValidUserByIdOrUid(userUid: $executor1Uid);
            if ($userRes->isFail())
            {
                return outputJsonError($userRes->getCode(), '', ['error' => $userRes->getMessage()]);
            }
            $params['executor1Id'] = $userRes->getData('id');
        }
        if (!empty($executor2))
        {
            $userRes = UserLogic::GetValidUserByIdOrUid(userUid: $executor2);
            if ($userRes->isFail())
            {
                return outputJsonError($userRes->getCode(), '', ['error' => $userRes->getMessage()]);
            }
            $params['executor2Id'] = $userRes->getData('id');
        }

        $createResult = BeautyServiceLogic::FollowServiceExecutor($params, $publicParams);
        if ($createResult->isFail())
        {
            return outputJsonError($createResult->getCode(),
                                   $createResult->getMessage(),
                                   ['error' => $createResult->getMessage()]);
        }

        return outputJsonResult($createResult->getData());
    }
}
