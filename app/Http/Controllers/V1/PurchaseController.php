<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\PurchaseTypeEnum;
use App\Enums\PurchaseOrderStatusEnum;
use App\Logics\V1\PurchaseLogic;
use App\Models\PurchaseOrderModel;

/**
 * Class PurchaseController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class PurchaseController extends Controller
{

    /**
     * 获取采购单筛选项
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetPurchaseFilterOptions(): JsonResponse
    {
        $getPurchaseUserOptionsRes = PurchaseLogic::GetCreatePurchaseUserOptions(getRequestReservedParameters());

        $getCPurchaseSupplierOptionsRes = PurchaseLogic::GetCreatePurchaseSupplierOptions(getRequestReservedParameters());

        $getPurchaseAllotHospitalOptionsRes = PurchaseLogic::GetPurchaseAllotHospitalOptions(getRequestReservedParameters());

        return outputJsonResult([
                                    'userOptions'          => $getPurchaseUserOptionsRes->getData(),
                                    'supplierOptions'      => $getCPurchaseSupplierOptionsRes->getData(),
                                    'allotHospitalOptions' => $getPurchaseAllotHospitalOptionsRes->getData()
                                ]);
    }

    /**
     * 获取采购类型
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetPurchaseTypeOptions(): JsonResponse
    {
        $getPurchaseTypeOptions = PurchaseTypeEnum::options();

        return outputJsonResult([
                                    'purchaseTypeOptions' => $getPurchaseTypeOptions,
                                ]);
    }

    /**
     * 获取采购供应商
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetPurchaseSupplier(Request $request): JsonResponse
    {
        $purchaseType = $request->post('purchaseType', 0);
        if (empty($purchaseType) || !in_array($purchaseType, PurchaseTypeEnum::values()))
        {
            return outputJsonError(400, '采购选择类型错误', ['error' => '采购选择类型错误']);
        }

        $getPurchaseSupplierRes = PurchaseLogic::GetPurchaseSupplier($purchaseType, getRequestReservedParameters());
        if ($getPurchaseSupplierRes->isFail())
        {
            return outputJsonError($getPurchaseSupplierRes->getCode(),
                                   $getPurchaseSupplierRes->getMessage(),
                                   ['error' => $getPurchaseSupplierRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseSupplierRes->getData());
    }

    /**
     * 搜索采购商品
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function SearchPurchaseItems(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'        => ['required', 'string'],
                                         'purchaseType'    => [
                                             'required',
                                             'integer',
                                             Rule::in(PurchaseTypeEnum::values())
                                         ],
                                         'supplierUid'     => ['sometimes', 'nullable', 'string'],
                                         'fromHospitalUid' => ['sometimes', 'nullable', 'string'],
                                         'isPurchased'     => ['sometimes', 'nullable', 'integer', Rule::in([0, 1])],
                                         'brandUid'        => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [],
                                     [
                                         'keywords'        => '搜索关键词',
                                         'purchaseType'    => '采购类型',
                                         'supplierUid'     => '供应商UID',
                                         'fromHospitalUid' => '调拨源医院UID',
                                         'isPurchased'     => '是否已采购',
                                         'brandUid'        => '品牌UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getSearchItemsRes = PurchaseLogic::GetSearchItems($searchParams, $publicParams);
        if ($getSearchItemsRes->isFail())
        {
            return outputJsonError($getSearchItemsRes->getCode(),
                                   $getSearchItemsRes->getMessage(),
                                   ['error' => $getSearchItemsRes->getMessage()]);
        }

        return outputJsonResult($getSearchItemsRes->getData());
    }

    /**
     * 获取采购单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'         => ['sometimes', 'nullable', 'string'],
                                         'startDate'        => ['sometimes', 'nullable', 'date'],
                                         'endDate'          => ['sometimes', 'nullable', 'date'],
                                         'purchaseStatus'   => ['sometimes', 'nullable', 'integer', Rule::in(PurchaseOrderStatusEnum::values())],
                                         'createUserUid'    => ['sometimes', 'nullable', 'string'],
                                         'purchaseType'     => ['sometimes', 'nullable', 'integer', Rule::in(PurchaseTypeEnum::values())],
                                         'supplierUid'      => ['sometimes', 'nullable', 'string'],
                                         'allotHospitalUid' => ['sometimes', 'nullable', 'string'],
                                         'page'             => ['sometimes', 'nullable', 'integer'],
                                         'count'            => ['sometimes', 'nullable', 'integer'],
                                     ],
                                     [],
                                     [
                                         'keywords'         => '搜索关键词',
                                         'startDate'        => '开始日期',
                                         'endDate'          => '结束日期',
                                         'purchaseStatus'   => '采购单状态',
                                         'createUserUid'    => '创建人',
                                         'purchaseType'     => '采购类型',
                                         'supplierUid'      => '供应商',
                                         'allotHospitalUid' => '调拨源医院',
                                         'page'             => '页码',
                                         'count'            => '每页条数',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getPurchaseListRes = PurchaseLogic::GetPurchaseLists($searchParams, $publicParams);
        if ($getPurchaseListRes->isFail())
        {
            return outputJsonError($getPurchaseListRes->getCode(),
                                   $getPurchaseListRes->getMessage(),
                                   ['error' => $getPurchaseListRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseListRes->getData());
    }

    /**
     * 新增采购单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     */
    public function Add(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'purchaseType'    => [
                                             'required',
                                             'integer',
                                             Rule::in(PurchaseTypeEnum::values())
                                         ],
                                         'supplierUid'     => ['sometimes', 'nullable', 'string'],
                                         'fromHospitalUid' => ['sometimes', 'nullable', 'string'],
                                         'remark'          => ['sometimes', 'nullable', 'string'],
                                         'submitType'      => ['required', 'integer', Rule::in([0, 1])],
                                         'totalPrice'      => ['required', 'numeric', 'min:0'],
                                         'items'           => ['required', 'array'],
                                     ],
                                     [],
                                     [
                                         'purchaseType'    => '采购类型',
                                         'supplierUid'     => '供应商UID',
                                         'fromHospitalUid' => '调拨源医院UID',
                                         'remark'          => '备注',
                                         'submitType'      => '提交类型',
                                         'totalPrice'      => '总价',
                                         'items'           => '采购商品',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $addPurchaseParams = $validator->validated();
        $publicParams      = getRequestReservedParameters();

        $getAddPurchaseRes = PurchaseLogic::AddPurchaseOrder($addPurchaseParams, $publicParams);
        if ($getAddPurchaseRes->isFail())
        {
            return outputJsonError($getAddPurchaseRes->getCode(),
                                   $getAddPurchaseRes->getMessage(),
                                   ['error' => $getAddPurchaseRes->getMessage()]);
        }

        return outputJsonResult($getAddPurchaseRes->getData());
    }

    /**
     * 编辑采购单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Edit(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'purchaseCode'    => ['required', 'string', 'between:8,64'],
                                         'purchaseType'    => [
                                             'required',
                                             'integer',
                                             Rule::in(PurchaseTypeEnum::values())
                                         ],
                                         'supplierUid'     => ['sometimes', 'nullable', 'string'],
                                         'fromHospitalUid' => ['sometimes', 'nullable', 'string'],
                                         'remark'          => ['sometimes', 'nullable', 'string'],
                                         'submitType'      => ['required', 'integer', Rule::in([0, 1])],
                                         'totalPrice'      => ['required', 'numeric', 'min:0'],
                                         'items'           => ['required', 'array'],
                                     ],
                                     [],
                                     [
                                         'purchaseCode'    => '采购单编码',
                                         'purchaseType'    => '采购类型',
                                         'supplierUid'     => '供应商UID',
                                         'fromHospitalUid' => '调拨源医院UID',
                                         'remark'          => '备注',
                                         'submitType'      => '提交类型',
                                         'totalPrice'      => '总价',
                                         'items'           => '采购商品',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取参数
        $purchaseCode = $validator->validated()['purchaseCode'];

        // 获取采购单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['purchase_code' => $purchaseCode]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return outputJsonError(42010, '采购单不存在', ['error' => '采购单不存在']);
        }

        $editPurchaseParams = $validator->validated();
        $publicParams       = getRequestReservedParameters();

        $getEditPurchaseRes = PurchaseLogic::EditPurchaseOrder($getPurchaseOrderRes['id'], $editPurchaseParams, $publicParams);
        if ($getEditPurchaseRes->isFail())
        {
            return outputJsonError($getEditPurchaseRes->getCode(),
                                   $getEditPurchaseRes->getMessage(),
                                   ['error' => $getEditPurchaseRes->getMessage()]);
        }

        return outputJsonResult($getEditPurchaseRes->getData());
    }

    /**
     * 获取采购单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        $purchaseCode = $request->post('purchaseCode');
        if (empty($purchaseCode))
        {
            return outputJsonError(400, '采购单编码不能为空', ['error' => '采购单编码不能为空']);
        }

        // 获取采购单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['purchase_code' => $purchaseCode]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return outputJsonError(42010, '采购单不存在', ['error' => '采购单不存在']);
        }

        $getPurchaseDetailRes = PurchaseLogic::GetPurchaseOrderDetail($getPurchaseOrderRes['id'], getRequestReservedParameters());
        if ($getPurchaseDetailRes->isFail())
        {
            return outputJsonError($getPurchaseDetailRes->getCode(),
                                   $getPurchaseDetailRes->getMessage(),
                                   ['error' => $getPurchaseDetailRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseDetailRes->getData());
    }

    /**
     * 删除采购单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Delete(Request $request): JsonResponse
    {
        $purchaseCode = $request->post('purchaseCode');
        if (empty($purchaseCode))
        {
            return outputJsonError(400, '采购单编码不能为空', ['error' => '采购单编码不能为空']);
        }

        // 获取采购单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['purchase_code' => $purchaseCode]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return outputJsonError(42010, '采购单不存在', ['error' => '采购单不存在']);
        }

        $deletePurchaseRes = PurchaseLogic::DeletePurchaseOrder($getPurchaseOrderRes['id'], getRequestReservedParameters());
        if ($deletePurchaseRes->isFail())
        {
            return outputJsonError($deletePurchaseRes->getCode(),
                                   $deletePurchaseRes->getMessage(),
                                   ['error' => $deletePurchaseRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
