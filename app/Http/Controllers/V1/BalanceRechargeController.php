<?php

namespace App\Http\Controllers\V1;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Logics\V1\MemberLogic;
use App\Logics\V1\UserLogic;
use App\Logics\V1\BalanceRechargeActivityLogic;
use App\Logics\V1\BuySheet\BalanceRechargeSheetLogic;

/**
 * Class BalanceRechargeController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class BalanceRechargeController extends Controller
{
    /**
     * 医院充值活动
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function RechargeActivities(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'memberUid' => 'required|string|between:8,64',
            ],
            [],
            [
                'memberUid' => '用户UID',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $rechargeActivitiesRes = BalanceRechargeActivityLogic::GetRechargeActivities($params, $publicParams);
        if ($rechargeActivitiesRes->isFail())
        {
            return outputJsonError($rechargeActivitiesRes->getCode(),
                                   '',
                                   ['error' => $rechargeActivitiesRes->getMessage()]);
        }

        return outputJsonResult($rechargeActivitiesRes->getData());
    }

    /**
     * 创建会员余额充值购买单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function CreateRechargeSheet(Request $request): JsonResponse
    {

        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'memberUid'               => 'required|string|between:8,64',
                'sellerUid'               => 'required|string|between:8,64',
                'items'                   => 'required|array',
                'items.*.activityUid'     => 'required|string|between:8,64',
                'items.*.quantity'        => 'required|integer|min:1',
                'items.*.balanceRecharge' => 'required|numeric|min:0',
                'items.*.balanceGift'     => 'required|numeric|min:0',
                'totalPrice'              => 'required|numeric|min:0',
            ],
            [],
            [
                'memberUid'               => '用户UID',
                'sellerUid'               => '销售员UID',
                'items'                   => '充值明细',
                'items.*.activityUid'     => '活动UID',
                'items.*.quantity'        => '数量',
                'items.*.balanceRecharge' => '充值金额',
                'items.*.balanceGift'     => '赠送金额',
                'totalPrice'              => '总金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $memberUid = $params['memberUid'];
        $sellerUid = $params['sellerUid'];
        $memberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
        if ($memberRes->isFail())
        {
            return outputJsonError($memberRes->getCode(), '', ['error' => $memberRes->getMessage()]);
        }
        $userRes = UserLogic::GetValidUserByIdOrUid(userUid: $sellerUid);
        if ($userRes->isFail())
        {
            return outputJsonError($userRes->getCode(), '', ['error' => $userRes->getMessage()]);
        }

        $params['memberId'] = $memberRes->getData('id');
        $params['sellerId'] = $userRes->getData('id');

        $createResult = BalanceRechargeSheetLogic::CreateSheet($params, $publicParams);
        if ($createResult->isFail())
        {
            return outputJsonError($createResult->getCode(), '', ['error' => $createResult->getMessage()]);
        }

        return outputJsonResult($createResult->getData());
    }
}
