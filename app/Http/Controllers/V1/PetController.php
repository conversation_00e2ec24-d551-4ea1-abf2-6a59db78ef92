<?php

namespace App\Http\Controllers\V1;

use Exception;
use Throwable;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Form\PetRequest;
use App\Logics\V1\PetLogic;
use App\Models\MemberModel;
use App\Models\MemberPetsModel;

/**
 * 会员宠物相关
 * Class PetController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class PetController extends Controller
{
    /**
     * 会员宠物列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     * @noinspection PhpUnused
     */
    public function Lists(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(),
                                    [
                                        'memberUid' => ['required', 'string', 'between:8,64'],
                                        'page'      => ['sometimes', 'nullable', 'integer'],
                                        'count'     => ['sometimes', 'nullable', 'integer'],
                                    ],
                                    [
                                        'memberUid.required' => '用户UID，必选参数错误',
                                        'memberUid.string'   => '用户UID，参数类型错误',
                                        'memberUid.between'  => '用户UID，参数长度错误',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        // 获取会员信息
        $memberUid    = $validate->validated()['memberUid'];
        $getMemberRes = MemberModel::getOneByUid($memberUid);
        if (empty($getMemberRes))
        {
            return outputJsonError(30002, '会员不存在', ['error' => '会员不存在']);
        }

        $searchParams        = $validate->validated();
        $getMemberPetListRes = PetLogic::SearchMemberPetList($getMemberRes['id'], $searchParams);
        if ($getMemberPetListRes->isFail())
        {
            return outputJsonError($getMemberPetListRes->getCode(),
                                   $getMemberPetListRes->getMessage(),
                                   ['error' => $getMemberPetListRes->getMessage()]);
        }

        return outputJsonResult($getMemberPetListRes->getData());
    }

    /**
     * 新增宠物
     *
     * @param PetRequest $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Add(PetRequest $request): JsonResponse
    {
        // 验证参数有效性
        $memberUid = $request->post('memberUid', '');
        if (empty($memberUid))
        {
            return outputJsonError(400, '用户UID，必选参数错误', ['error' => '用户UID，必选参数错误']);
        }

        // 用户是否存在
        $getMemberRes = MemberModel::getOneByUid($memberUid);
        if (empty($getMemberRes))
        {
            return outputJsonError(30002, '用户不存在', ['error' => '用户UID错误']);
        }

        // 验证参数有效性
        $validator = $request->getValidator();
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $publicParams = getRequestReservedParameters();
        $addPetParams = $validator->validated();
        $getAddPetRes = PetLogic::AddPet($getMemberRes['id'], $addPetParams, $publicParams);
        if ($getAddPetRes->isFail())
        {
            return outputJsonError($getAddPetRes->getCode(),
                                   $getAddPetRes->getMessage(),
                                   ['error' => $getAddPetRes->getMessage()]);
        }

        return outputJsonResult($getAddPetRes->getData());
    }

    /**
     * 编辑宠物
     *
     * @param PetRequest $request
     *
     * @return JsonResponse
     * @throws ValidationException|Throwable
     * @noinspection PhpUnused
     */
    public function Edit(PetRequest $request): JsonResponse
    {
        // 宠物ID参数
        $petUid = $request->post('petUid', 0);
        if (empty($petUid))
        {
            return outputJsonError(400, '宠物UID，必选参数错误', ['error' => '宠物UID，必选参数错误']);
        }

        // 验证公共参数有效性
        $validator = $request->getValidator();
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 宠物是否存在
        $getPetInfo = MemberPetsModel::getOneByUid($petUid);
        if (empty($getPetInfo))
        {
            return outputJsonError(32000, '编辑的宠物不存在', ['error' => '编辑的宠物不存在']);
        }

        $addPetParams = $validator->validated();
        $getAddPetRes = PetLogic::EditPet($getPetInfo['id'], $addPetParams, getRequestReservedParameters());
        if ($getAddPetRes->isFail())
        {
            return outputJsonError($getAddPetRes->getCode(),
                                   $getAddPetRes->getMessage(),
                                   ['error' => $getAddPetRes->getMessage()]);
        }

        return outputJsonResult($getAddPetRes->getData());
    }

    /**
     * 宠物详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make($request->post(),
                                     [
                                         'petUid' => ['required', 'string', 'between:8,64']
                                     ],
                                     [
                                         'petUid.required' => '宠物UID，必选参数错误',
                                         'petUid.string'   => '宠物UID，参数类型错误',
                                         'petUid.between'  => '宠物UID，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 宠物是否存在
        $petUid     = $validator->validated()['petUid'];
        $getPetInfo = MemberPetsModel::getOneByUid($petUid);
        if (empty($getPetInfo))
        {
            return outputJsonError(32000, '查看的宠物不存在', ['error' => '查看的宠物不存在']);
        }

        $getPetFullInfoRes = PetLogic::GetPetDetailFullInfo($getPetInfo['id']);
        if ($getPetFullInfoRes->isFail())
        {
            return outputJsonError($getPetFullInfoRes->getCode(),
                                   $getPetFullInfoRes->getMessage(),
                                   ['error' => $getPetFullInfoRes->getMessage()]);
        }

        return outputJsonResult($getPetFullInfoRes->getData());
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException|Throwable
     * @noinspection PhpUnused
     */
    public function AddPrevent(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'petUid'        => ['required', 'string', 'between:8,64'],
                                         'preventDate'   => ['sometimes', 'nullable', 'date'],
                                         'preventRemark' => [
                                             'sometimes',
                                             'nullable',
                                             'string',
                                             'between:0,100'
                                         ],
                                     ],
                                     [
                                         'petUid.required'       => '宠物UID，必选参数错误',
                                         'petUid.string'         => '宠物UID，参数类型错误',
                                         'petUid.between'        => '宠物UID，参数长度错误',
                                         'preventDate.date'      => '免疫日期，参数类型错误',
                                         'preventRemark.string'  => '免疫备注，参数类型错误',
                                         'preventRemark.between' => '免疫备注，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(32003,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $preventParams = $validator->validated();
        $petUid        = $preventParams['petUid'];
        $petInfo       = MemberPetsModel::getOneByUid($petUid);
        if (empty($petInfo))
        {
            return outputJsonError(32000, '宠物不存在', ['error' => '宠物不存在']);
        }

        // 添加宠物免疫记录
        $getAddPreventRes = PetLogic::AddPetPrevent($petInfo['id'], $preventParams, getRequestReservedParameters());
        if ($getAddPreventRes->isFail())
        {
            return outputJsonError($getAddPreventRes->getCode(),
                                   $getAddPreventRes->getMessage(),
                                   ['error' => $getAddPreventRes->getMessage()]);
        }

        return outputJsonResult([]);
    }

    /**
     * 添加宠物驱虫记录
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function AddDeworming(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'petUid'          => ['required', 'string', 'between:8,64'],
                                         'dewormingDate'   => ['sometimes', 'nullable', 'date'],
                                         'dewormingRemark' => [
                                             'sometimes',
                                             'nullable',
                                             'string',
                                             'between:0,100'
                                         ],
                                     ],
                                     [
                                         'petUid.required'         => '宠物UID，必选参数错误',
                                         'petUid.string'           => '宠物UID，参数类型错误',
                                         'petUid.between'          => '宠物UID，参数长度错误',
                                         'dewormingDate.date'      => '驱虫日期，参数类型错误',
                                         'dewormingRemark.string'  => '驱虫备注，参数类型错误',
                                         'dewormingRemark.between' => '驱虫备注，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(32004,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $dewormingParams = $validator->validated();
        $petUid          = $dewormingParams['petUid'];
        $petInfo         = MemberPetsModel::getOneByUid($petUid);
        if (empty($petInfo))
        {
            return outputJsonError(32000, '宠物不存在', ['error' => '宠物不存在']);
        }

        // 添加宠物驱虫记录
        $getAddDewormingRes = PetLogic::AddPetDeworming($petInfo['id'],
                                                        $dewormingParams,
                                                        getRequestReservedParameters());
        if ($getAddDewormingRes->isFail())
        {
            return outputJsonError($getAddDewormingRes->getCode(),
                                   $getAddDewormingRes->getMessage(),
                                   ['error' => $getAddDewormingRes->getMessage()]);
        }

        return outputJsonResult([]);
    }

    /**
     * 获取宠物免疫驱虫记录
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetPetPreventList(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'petUid' => ['required', 'string', 'between:8,64'],
                                     ],
                                     [
                                         'petUid.required' => '宠物UID，必选参数错误',
                                         'petUid.string'   => '宠物UID，参数类型错误',
                                         'petUid.between'  => '宠物UID，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 宠物是否存在
        $petUid     = $validator->validated()['petUid'];
        $getPetInfo = MemberPetsModel::getOneByUid($petUid);
        if (empty($getPetInfo))
        {
            return outputJsonError(32000, '查看的宠物不存在', ['error' => '查看的宠物不存在']);
        }

        $getPetPreventListRes = PetLogic::GetPetPreventList($getPetInfo['id']);
        if ($getPetPreventListRes->isFail())
        {
            return outputJsonError($getPetPreventListRes->getCode(),
                                   $getPetPreventListRes->getMessage(),
                                   ['error' => $getPetPreventListRes->getMessage()]);
        }

        return outputJsonResult($getPetPreventListRes->getData());
    }

    /**
     * 获取宠物驱虫记录
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetPetDewormingList(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'petUid' => ['required', 'string', 'between:8,64'],
                                     ],
                                     [
                                         'petUid.required' => '宠物UID，必选参数错误',
                                         'petUid.string'   => '宠物UID，参数类型错误',
                                         'petUid.between'  => '宠物UID，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 宠物是否存在
        $petUid     = $validator->validated()['petUid'];
        $getPetInfo = MemberPetsModel::getOneByUid($petUid);
        if (empty($getPetInfo))
        {
            return outputJsonError(32000, '查看的宠物不存在', ['error' => '查看的宠物不存在']);
        }

        $getPetPreventListRes = PetLogic::GetPetDewormingList($getPetInfo['id']);
        if ($getPetPreventListRes->isFail())
        {
            return outputJsonError($getPetPreventListRes->getCode(),
                                   $getPetPreventListRes->getMessage(),
                                   ['error' => $getPetPreventListRes->getMessage()]);
        }

        return outputJsonResult($getPetPreventListRes->getData());
    }

    /**
     * 获取宠物体重记录
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetPetWeightList(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'petUid' => ['required', 'string', 'between:8,64'],
                                     ],
                                     [
                                         'petUid.required' => '宠物UID，必选参数错误',
                                         'petUid.string'   => '宠物UID，参数类型错误',
                                         'petUid.between'  => '宠物UID，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 宠物是否存在
        $petUid     = $validator->validated()['petUid'];
        $getPetInfo = MemberPetsModel::getOneByUid($petUid);
        if (empty($getPetInfo))
        {
            return outputJsonError(32000, '查看的宠物不存在', ['error' => '查看的宠物不存在']);
        }

        $getPetWeightListRes = PetLogic::GetPetWeightList($getPetInfo['id'], getRequestReservedParameters());
        if ($getPetWeightListRes->isFail())
        {
            return outputJsonError($getPetWeightListRes->getCode(),
                                   $getPetWeightListRes->getMessage(),
                                   ['error' => $getPetWeightListRes->getMessage()]);
        }

        return outputJsonResult($getPetWeightListRes->getData());
    }
}
