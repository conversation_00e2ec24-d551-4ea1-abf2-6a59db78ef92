<?php

namespace App\Http\Controllers\V1;

use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Logics\V1\BedsLogic;
use App\Logics\V1\RoomLogic;
use App\Logics\V1\AreaLogic;
use App\Logics\V1\CaseLogic;
use App\Logics\V1\PetBreedLogic;
use App\Logics\V1\PetColorLogic;
use App\Logics\V1\ItemBrandLogic;
use App\Logics\V1\ItemUsageLogic;
use App\Logics\V1\MemberFromLogic;
use App\Logics\V1\PetCategoryLogic;
use App\Logics\V1\NursingLevelLogic;
use App\Logics\V1\PetLiveStatusLogic;

/**
 * 获取基础枚举数据
 * Class BasicEnumDataController
 * @package      App\Http\Controllers\V1
 *
 * @noinspection PhpUnused
 */
class BasicEnumDataController extends Controller
{

    /**
     * 获取会员注册来源渠道
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetMemberFromOptions(): JsonResponse
    {
        $getMemberFromRes = MemberFromLogic::GetMemberFromList();
        if ($getMemberFromRes->isFail())
        {
            return outputJsonError($getMemberFromRes->getCode(),
                                   $getMemberFromRes->getMessage(),
                                   ['error' => $getMemberFromRes->getMessage()]);
        }

        return outputJsonResult($getMemberFromRes->getData());
    }

    /**
     * 获取省市区县各级
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetProvinceOptions(): JsonResponse
    {
        $getAreaOptionsRes = AreaLogic::GetProvinceOptions();
        if ($getAreaOptionsRes->isFail())
        {
            return outputJsonError($getAreaOptionsRes->getCode(),
                                   $getAreaOptionsRes->getMessage(),
                                   ['error' => $getAreaOptionsRes->getMessage()]);
        }

        return outputJsonResult($getAreaOptionsRes->getData());
    }

    /**
     * 获取宠物分类
     *
     * @return JsonResponse
     *
     * @noinspection  PhpUnused
     */
    public function GetPetCategoryOptions(): JsonResponse
    {
        $getPetCategoryRes = PetCategoryLogic::GetPetCategoryOptions();
        if ($getPetCategoryRes->isFail())
        {
            return outputJsonError($getPetCategoryRes->getCode(),
                                   $getPetCategoryRes->getMessage(),
                                   ['error' => $getPetCategoryRes->getMessage()]);
        }

        return outputJsonResult($getPetCategoryRes->getData());
    }

    /**
     * 获取宠物品种
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetPetBreedOptions(): JsonResponse
    {
        $getPetBreedRes = PetBreedLogic::GetPetBreedOptions();
        if ($getPetBreedRes->isFail())
        {
            return outputJsonError($getPetBreedRes->getCode(),
                                   $getPetBreedRes->getMessage(),
                                   ['error' => $getPetBreedRes->getMessage()]);
        }

        return outputJsonResult($getPetBreedRes->getData());
    }

    /**
     * 获取宠物颜色
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetPetColorOptions(): JsonResponse
    {
        $getPetColorRes = PetColorLogic::GetPetColorOptions();
        if ($getPetColorRes->isFail())
        {
            return outputJsonError($getPetColorRes->getCode(),
                                   $getPetColorRes->getMessage(),
                                   ['error' => $getPetColorRes->getMessage()]);
        }

        return outputJsonResult($getPetColorRes->getData());
    }

    /**
     * 获取宠物生存状态
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetPetLiveStatusOptions(): JsonResponse
    {
        $getPetLiveStatusRes = PetLiveStatusLogic::GetPetLiveStatusOptions();
        if ($getPetLiveStatusRes->isFail())
        {
            return outputJsonError($getPetLiveStatusRes->getCode(),
                                   $getPetLiveStatusRes->getMessage(),
                                   ['error' => $getPetLiveStatusRes->getMessage()]);
        }

        return outputJsonResult($getPetLiveStatusRes->getData());
    }

    /**
     * 门诊体况-体温测量部位
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetTemperatureTypeOptions(): JsonResponse
    {
        $getTemperatureTypeRes = CaseLogic::GetTemperatureTypeOptions();

        return outputJsonResult($getTemperatureTypeRes->getData());
    }

    /**
     * 门诊体况-血压测量部位
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetBloodPressureTypeOptions(): JsonResponse
    {
        $getBloodTypeRes = CaseLogic::GetBloodPressureTypeOptions();

        return outputJsonResult($getBloodTypeRes->getData());
    }

    /**
     * 住院-获取护理等级
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetNursingLevelOptions(): JsonResponse
    {
        $getNursingLevelRes = NursingLevelLogic::GetNursingLevelOptions();

        return outputJsonResult($getNursingLevelRes->getData());
    }

    /**
     * 住院-获取病房住院部信息
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetRoomsOptions(): JsonResponse
    {
        $getRoomsRes = RoomLogic::GetRoomsOptions(getPublicParamsHospitalId());

        return outputJsonResult($getRoomsRes->getData());
    }

    /**
     * 住院-获取病房床位信息
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetRoomBedsOptions(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'roomId' => ['required', 'int'],
                                    ],
                                    [
                                        'roomId.required' => '病房ID，必选参数错误',
                                        'roomId.int'      => '病房ID，参数类型错误',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $roomId     = $validate->validated()['roomId'];
        $getBedsRes = BedsLogic::GetRoomBedsOptions($roomId, getRequestReservedParameters());

        return outputJsonResult($getBedsRes->getData());
    }

    /**
     * 获取项目用药方式
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetRecipeItemUsageOptions(): JsonResponse
    {
        $getRecipeItemUsageRes = ItemUsageLogic::GetRecipeItemUsageOptions();
        if ($getRecipeItemUsageRes->isFail())
        {
            return outputJsonError($getRecipeItemUsageRes->getCode(),
                                   $getRecipeItemUsageRes->getMessage(),
                                   ['error' => $getRecipeItemUsageRes->getMessage()]);
        }

        return outputJsonResult($getRecipeItemUsageRes->getData());
    }

    /**
     * 获取商品品牌
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetItemBrandOptions(): JsonResponse
    {
        $getBrandListRes = ItemBrandLogic::GetBrandList();
        if ($getBrandListRes->isFail())
        {
            return outputJsonError($getBrandListRes->getCode(),
                                   $getBrandListRes->getMessage(),
                                   ['error' => $getBrandListRes->getMessage()]);
        }

        return outputJsonResult($getBrandListRes->getData());
    }
}
