<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;
use App\Logics\V1\RetailLogic;
use App\Logics\V1\BuySheet\RetailSheetLogic;
use App\Logics\V1\MemberLogic;
use App\Logics\V1\UserLogic;

class RetailController extends Controller
{
    /**
     * 搜索零售商品
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function SearchRetailItems(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keyword' => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'keyword' => '搜索关键词',
                                     ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getSearchItemsRes = RetailLogic::GetSearchItems($searchParams, $publicParams);
        if ($getSearchItemsRes->isFail())
        {
            return outputJsonError($getSearchItemsRes->getCode(),
                                   $getSearchItemsRes->getMessage(),
                                   ['error' => $getSearchItemsRes->getMessage()]);
        }

        return outputJsonResult($getSearchItemsRes->getData());
    }

    /**
     * 创建零售商品购买单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function CreateSheet(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'memberUid'  => 'sometimes|nullable|string|between:8,64',
                'sellerUid'  => 'sometimes|nullable|string|between:8,64',
                'items'      => 'required|array',
                'totalPrice' => 'required|numeric|min:0',
            ],
            [],
            [
                'memberUid'  => '会员UID',
                'sellerUid'  => '销售员UID',
                'items'      => '零售商品明细',
                'totalPrice' => '总金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $memberUid = $params['memberUid'];
        $sellerUid = $params['sellerUid'];
        // 兼容匿名购买
        if (!empty($memberUid))
        {
            $getMemberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
            if ($getMemberRes->isFail())
            {
                return outputJsonError($getMemberRes->getCode(), '', ['error' => $getMemberRes->getMessage()]);
            }

            $params['memberId'] = $getMemberRes->getData('id');
        }

        $userRes = UserLogic::GetValidUserByIdOrUid(userUid: $sellerUid);
        if ($userRes->isFail())
        {
            return outputJsonError($userRes->getCode(), '', ['error' => $userRes->getMessage()]);
        }
        $params['sellerId'] = $userRes->getData('id');

        $createResult = RetailSheetLogic::CreateSheet($params, $publicParams);
        if ($createResult->isFail())
        {
            return outputJsonError($createResult->getCode(),
                                   $createResult->getMessage(),
                                   ['error' => $createResult->getMessage()]);
        }

        return outputJsonResult($createResult->getData());
    }
}
