<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Support\Facades\App;
use App\Enums\LoginTypeEnum;
use App\Enums\UserSmsSendTypeEnum;
use App\Enums\UserSmsTypeEnum;
use App\Logics\V1\UserVerifySmsLogic;
use App\Logics\V1\LoginLogic;

class LoginController extends Controller
{
    /**
     * 获取登录验证码
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function SendLoginSms(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'phone'    => 'required|integer|digits:11',
                'sendType' => ['sometimes', 'nullable', 'integer', new Enum(UserSmsSendTypeEnum::class)],
            ],
        );

        if ($validator->fails())
        {
            return outputJsonError(400, $validator->errors()->first(),
                                   ['validatorError' => $validator->errors()->all()]);
        }

        $params       = array_merge(
            $validator->validated(),
            [
                'token' => '',
                'ip'    => $request->ip(),
                'type'  => UserSmsTypeEnum::Login->value,
            ]
        );
        $publicParams = getRequestReservedParameters();

        $sendResult = UserVerifySmsLogic::SendVerifyCode($params, $publicParams);
        if ($sendResult->isFail())
        {
            return outputJsonError($sendResult->getCode(), '', ['error' => $sendResult->getMessage()]);
        }

        //TODO:非正式环境，返回发送结果
        return outputJsonResult(App::isProduction() ? null : $sendResult->getData());
    }

    /**
     * 验证码和密码登录
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Login(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'loginType'  => ['required', 'integer', Rule::in(LoginTypeEnum::DirectTypeValues())],
                'phone'      => [
                    Rule::requiredIf(fn() => in_array($request->post('loginType'), LoginTypeEnum::DirectTypeValues())),
                    'integer',
                    'digits:11',
                ],
                'verifyCode' => [
                    Rule::requiredIf(fn() => $request->post('loginType') == LoginTypeEnum::Code->value),
                    'string',
                    'between:6,8',
                ],
                'password'   => [
                    Rule::requiredIf(fn() => $request->post('loginType') == LoginTypeEnum::Password->value),
                    'string',
                    'between:8,32',
                ],
            ],
        );

        if ($validator->fails())
        {
            return outputJsonError(400, $validator->errors()->first(),
                                   ['validatorError' => $validator->errors()->all()]);
        }

        $params       = array_merge(
            $validator->validated(),
            [
                'token' => '',
                'ip'    => $request->ip(),
            ]
        );
        $publicParams = getRequestReservedParameters();

        if ($request->post('loginType') == LoginTypeEnum::Code->value)
        {
            $loginResult = LoginLogic::LoginFromCode($params, $publicParams);
        }
        else
        {
            $loginResult = LoginLogic::LoginFromPassword($params, $publicParams);
        }

        if ($loginResult->isFail())
        {
            return outputJsonError($loginResult->getCode(), '', ['error' => $loginResult->getMessage()]);
        }

        return outputJsonResult($loginResult->getData());
    }

    /**
     * 登录后选择医院
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function ChooseHospital(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'hospitalUid' => 'required|string|between:8,64',
            ],
            [
                'hospitalUid.required' => '登录医院为必选',
                'hospitalUid.string'   => '登录医院选择无效',
                'hospitalUid.between'  => '登录医院选择无效',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400, $validator->errors()->first(),
                                   ['validatorError' => $validator->errors()->all()]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $chooseHospitalResult = LoginLogic::LoginChooseHospital($params, $publicParams);
        if ($chooseHospitalResult->isFail())
        {
            return outputJsonError($chooseHospitalResult->getCode(), '',
                                   ['error' => $chooseHospitalResult->getMessage()]);
        }

        return outputJsonResult($chooseHospitalResult->getData());
    }

    /**
     * 获取登录状态信息
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function LoginStatus(Request $request): JsonResponse
    {
        $params       = [];
        $publicParams = getRequestReservedParameters();

        $statusResult = LoginLogic::LoginStatus($params, $publicParams);
        if ($statusResult->isFail())
        {
            return outputJsonError($statusResult->getCode(), '', ['error' => $statusResult->getMessage()]);
        }

        return outputJsonResult($statusResult->getData());
    }


    /**
     * 退出登录
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function Logout(Request $request): JsonResponse
    {
        $params       = [];
        $publicParams = getRequestReservedParameters();

        LoginLogic::Logout($params, $publicParams);

        //退出登录，永远不报错
        return outputJsonResult();
    }
}
