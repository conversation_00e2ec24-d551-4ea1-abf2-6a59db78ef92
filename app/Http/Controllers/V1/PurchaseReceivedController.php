<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\PurchaseTypeEnum;
use App\Enums\PurchaseOrderReceivedStatusEnum;
use App\Logics\V1\PurchaseReceivedLogic;
use App\Models\PurchaseOrderModel;
use App\Models\PurchaseReceivedModel;

/**
 * Class PurchaseReceivedController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class PurchaseReceivedController extends Controller
{

    /**
     * 获取可到货签收列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'         => ['sometimes', 'nullable', 'string'],
                                         'startDate'        => ['sometimes', 'nullable', 'date'],
                                         'endDate'          => ['sometimes', 'nullable', 'date'],
                                         'receivedStatus'   => ['sometimes', 'nullable', 'integer', Rule::in(PurchaseOrderReceivedStatusEnum::values())],
                                         'createUserUid'    => ['sometimes', 'nullable', 'string'],
                                         'purchaseType'     => ['sometimes', 'nullable', 'integer', Rule::in(PurchaseTypeEnum::values())],
                                         'supplierUid'      => ['sometimes', 'nullable', 'string'],
                                         'allotHospitalUid' => ['sometimes', 'nullable', 'string'],
                                         'page'             => ['sometimes', 'nullable', 'integer'],
                                         'count'            => ['sometimes', 'nullable', 'integer'],
                                     ],
                                     [],
                                     [
                                         'keywords'         => '搜索关键词',
                                         'startDate'        => '开始日期',
                                         'endDate'          => '结束日期',
                                         'receivedStatus'   => '签收状态',
                                         'createUserUid'    => '创建人',
                                         'purchaseType'     => '采购类型',
                                         'supplierUid'      => '供应商',
                                         'allotHospitalUid' => '调拨源医院',
                                         'page'             => '页码',
                                         'count'            => '每页条数',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getPurchaseListRes = PurchaseReceivedLogic::GetReceivedLists($searchParams, $publicParams);
        if ($getPurchaseListRes->isFail())
        {
            return outputJsonError($getPurchaseListRes->getCode(),
                                   $getPurchaseListRes->getMessage(),
                                   ['error' => $getPurchaseListRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseListRes->getData());
    }

    /**
     * 获取到货单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        $receivedCode = $request->post('receivedCode');
        if (empty($receivedCode))
        {
            return outputJsonError(400, '到货单编码不能为空', ['error' => '到货单编码不能为空']);
        }

        // 获取采购单信息
        $getReceivedRes = PurchaseReceivedModel::getData(where: ['received_code' => $receivedCode]);
        $getReceivedRes = $getReceivedRes ? current($getReceivedRes) : [];
        if (empty($getReceivedRes))
        {
            return outputJsonError(42016, '到货单不存在', ['error' => '到货单不存在']);
        }

        $getReceivedDetailRes = PurchaseReceivedLogic::GetReceivedDetail($getReceivedRes['id'], getRequestReservedParameters());
        if ($getReceivedDetailRes->isFail())
        {
            return outputJsonError($getReceivedDetailRes->getCode(),
                                   $getReceivedDetailRes->getMessage(),
                                   ['error' => $getReceivedDetailRes->getMessage()]);
        }

        return outputJsonResult($getReceivedDetailRes->getData());
    }

    /**
     * 到货签收
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     */
    public function Add(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'purchaseCode' => ['required', 'string', 'between:8,64'],
                                         'remark'       => ['required', 'nullable', 'string'],
                                         'pictures'     => ['required', 'array'],
                                         'items'        => ['required', 'array'],
                                     ],
                                     [],
                                     [
                                         'purchaseCode' => '采购单编码',
                                         'remark'       => '签收备注',
                                         'pictures'     => '签收凭证',
                                         'items'        => '签收商品',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取参数
        $purchaseCode      = $validator->validated()['purchaseCode'];
        $addReceivedParams = $validator->validated();
        $publicParams      = getRequestReservedParameters();

        // 获取采购单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['purchase_code' => $purchaseCode]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return outputJsonError(42010, '采购单不存在', ['error' => '采购单不存在']);
        }

        // 添加签收
        $getAddReceivedRes = PurchaseReceivedLogic::AddReceived($getPurchaseOrderRes['id'], $addReceivedParams, $publicParams);
        if ($getAddReceivedRes->isFail())
        {
            return outputJsonError($getAddReceivedRes->getCode(),
                                   $getAddReceivedRes->getMessage(),
                                   ['error' => $getAddReceivedRes->getMessage()]);
        }

        return outputJsonResult($getAddReceivedRes->getData());
    }
}
