<?php

namespace App\Http\Controllers\V1;

use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Logics\V1\WarehouseLogic;
use App\Models\StockWarehouseModel;

/**
 * 仓库相关
 * Class WarehouseController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class WarehouseController extends Controller
{
    /**
     * 获取仓库药房列表
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function Lists(): JsonResponse
    {
        $getWarehouseListRes = WarehouseLogic::GetWarehouseList(getPublicParamsHospitalId());
        if ($getWarehouseListRes->isFail())
        {
            return outputJsonError($getWarehouseListRes->getCode(),
                                   $getWarehouseListRes->getMessage(),
                                   ['error' => $getWarehouseListRes->getMessage()]);
        }

        return outputJsonResult($getWarehouseListRes->getData());
    }

    /**
     * 添加药房
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Add(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'name' => ['required', 'string'],
                                         'desc' => ['sometimes', 'nullable', 'string']
                                     ],
                                     [
                                         'name.required' => '仓库名称，必选参数错误',
                                         'name.string'   => '仓库名称，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $addWarehouseParams = $validator->validated();
        $addWarehouseRes    = WarehouseLogic::AddWarehouse($addWarehouseParams, getRequestReservedParameters());
        if ($addWarehouseRes->isFail())
        {
            return outputJsonError($addWarehouseRes->getCode(),
                                   $addWarehouseRes->getMessage(),
                                   ['error' => $addWarehouseRes->getMessage()]);
        }

        return outputJsonResult($addWarehouseRes->getData());
    }

    /**
     * 编辑仓库
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Edit(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'warehouseUid' => ['required', 'string', 'between:8,64'],
                                         'name'         => ['required', 'string'],
                                         'desc'         => ['sometimes', 'nullable', 'string']
                                     ],
                                     [
                                         'warehouseUid.required' => '仓库UID，必选参数错误',
                                         'warehouseUid.string'   => '仓库UID，参数类型错误',
                                         'warehouseUid.min'      => '仓库UID，参数值错误',
                                         'name.required'         => '仓库名称，必选参数错误',
                                         'name.string'           => '仓库名称，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取仓库信息
        $warehouseUid    = $validator->validated()['warehouseUid'];
        $getWarehouseRes = StockWarehouseModel::getOneByUid($warehouseUid);
        if (empty($getWarehouseRes))
        {
            return outputJsonError(41000, '仓库不存在', ['error' => '仓库不存在']);
        }

        // 编辑仓库
        $editWarehouseParams = $validator->validated();
        $editWarehouseRes    = WarehouseLogic::EditWarehouse($getWarehouseRes['id'],
                                                             $editWarehouseParams,
                                                             getRequestReservedParameters());
        if ($editWarehouseRes->isFail())
        {
            return outputJsonError($editWarehouseRes->getCode(),
                                   $editWarehouseRes->getMessage(),
                                   ['error' => $editWarehouseRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 删除仓库
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Delete(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'warehouseUid' => ['required', 'string', 'between:8,64'],
                                     ],
                                     [
                                         'warehouseUid.required' => '仓库UID，必选参数错误',
                                         'warehouseUid.string'   => '仓库UID，参数类型错误',
                                         'warehouseUid.min'      => '仓库UID，参数值错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取仓库信息
        $warehouseUid    = $validator->validated()['warehouseUid'];
        $getWarehouseRes = StockWarehouseModel::getOneByUid($warehouseUid);
        if (empty($getWarehouseRes))
        {
            return outputJsonResult();
        }

        // 删除仓库
        $deleteWarehouseRes = WarehouseLogic::DeleteWarehouse($getWarehouseRes['id'],
                                                              getRequestReservedParameters());
        if ($deleteWarehouseRes->isFail())
        {
            return outputJsonError($deleteWarehouseRes->getCode(),
                                   $deleteWarehouseRes->getMessage(),
                                   ['error' => $deleteWarehouseRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取仓库货架列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetShelvesList(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'warehouseUid' => ['required', 'required'],
                                    ],
                                    [
                                        'warehouseUid.required' => '仓库UID，必选参数错误',
                                        'warehouseUid.string'   => '仓库UID，参数类型错误',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        // 获取仓库信息
        $warehouseUid    = $validate->validated()['warehouseUid'];
        $getWarehouseRes = StockWarehouseModel::getOneByUid($warehouseUid);
        if (empty($getWarehouseRes))
        {
            return outputJsonError(41000, '仓库不存在', ['error' => '仓库不存在']);
        }

        $getShelfListRes = WarehouseLogic::GetWarehouseShelvesList($getWarehouseRes['id'],
                                                                   getRequestReservedParameters());
        if ($getShelfListRes->isFail())
        {
            return outputJsonError($getShelfListRes->getCode(),
                                   $getShelfListRes->getMessage(),
                                   ['error' => $getShelfListRes->getMessage()]);
        }

        return outputJsonResult($getShelfListRes->getData());
    }

    /**
     * 获取货架货位列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetShelfSlotList(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'warehouseUid' => ['required', 'string'],
                                        'shelfNumber'  => ['required', 'string'],
                                    ],
                                    [
                                        'warehouseUid.required' => '仓库UID，必选参数错误',
                                        'warehouseUid.string'   => '仓库UID，参数类型错误',
                                        'shelfNumber.required'  => '货架编号，必选参数错误',
                                        'shelfNumber.string'    => '货架编号，参数类型错误',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        // 获取参数
        $warehouseUid = $validate->validated()['warehouseUid'];
        $shelfNumber  = $validate->validated()['shelfNumber'];

        // 获取仓库信息
        $getWarehouseRes = StockWarehouseModel::getOneByUid($warehouseUid);
        if (empty($getWarehouseRes))
        {
            return outputJsonError(41000, '仓库不存在', ['error' => '仓库不存在']);
        }

        $getShelfSlotListRes = WarehouseLogic::GetWarehouseShelfSlotList($getWarehouseRes['id'],
                                                                         $shelfNumber,
                                                                         getRequestReservedParameters());
        if ($getShelfSlotListRes->isFail())
        {
            return outputJsonError($getShelfSlotListRes->getCode(),
                                   $getShelfSlotListRes->getMessage(),
                                   ['error' => $getShelfSlotListRes->getMessage()]);
        }

        return outputJsonResult($getShelfSlotListRes->getData());
    }

    /**
     * 新增货架
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function AddShelf(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'warehouseUid' => ['required', 'string'],
                                     ],
                                     [
                                         'warehouseUid.required' => '仓库UID，必选参数错误',
                                         'warehouseUid.string'   => '仓库UID，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取参数
        $warehouseUid = $validator->validated()['warehouseUid'];

        // 获取仓库信息
        $getWarehouseRes = StockWarehouseModel::getOneByUid($warehouseUid);
        if (empty($getWarehouseRes))
        {
            return outputJsonError(41000, '仓库不存在', ['error' => '仓库不存在']);
        }

        $getAddShelfRes = WarehouseLogic::AddShelf($getWarehouseRes['id'], getRequestReservedParameters());
        if ($getAddShelfRes->isFail())
        {
            return outputJsonError($getAddShelfRes->getCode(),
                                   $getAddShelfRes->getMessage(),
                                   ['error' => $getAddShelfRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 新增货位
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function AddShelfSlot(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'warehouseUid'   => ['required', 'string'],
                                         'shelfNumber'    => ['required', 'string'],
                                         'rowQuantity'    => ['sometimes', 'nullable', 'integer', 'min:0'],
                                         'columnQuantity' => ['sometimes', 'nullable', 'integer', 'min:0'],
                                     ],
                                     [
                                         'warehouseUid.required'  => '仓库UID，必选参数错误',
                                         'warehouseUid.string'    => '仓库UID，参数类型错误',
                                         'shelfNumber.required'   => '货架编号，必选参数错误',
                                         'shelfNumber.string'     => '货架编号，参数类型错误',
                                         'rowQuantity.integer'    => '行数，参数类型错误',
                                         'rowQuantity.min'        => '行数，参数值错误',
                                         'columnQuantity.integer' => '列数，参数类型错误',
                                         'columnQuantity.min'     => '列数，参数值错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取参数
        $warehouseUid   = $validator->validated()['warehouseUid'];
        $rowQuantity    = 0;
        $columnQuantity = 0;
        if ($request->has('rowQuantity'))
        {
            $rowQuantity = intval($validator->validated()['rowQuantity']);
        }
        if ($request->has('columnQuantity'))
        {
            $columnQuantity = intval($validator->validated()['columnQuantity']);
        }

        // 验证行、列数量，不可都为0
        if ($rowQuantity <= 0 && $columnQuantity <= 0)
        {
            return outputJsonError(41002, '行数和列数至少需要一个大于0', ['error' => '行数和列数至少需要一个大于0']);
        }

        // 获取仓库信息
        $getWarehouseRes = StockWarehouseModel::getOneByUid($warehouseUid);
        if (empty($getWarehouseRes))
        {
            return outputJsonError(41000, '仓库不存在', ['error' => '仓库不存在']);
        }

        $getAddShelfRes = WarehouseLogic::AddShelfSlot($getWarehouseRes['id'],
                                                       $validator->validated(),
                                                       getRequestReservedParameters());
        if ($getAddShelfRes->isFail())
        {
            return outputJsonError($getAddShelfRes->getCode(),
                                   $getAddShelfRes->getMessage(),
                                   ['error' => $getAddShelfRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
