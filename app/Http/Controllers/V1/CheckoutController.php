<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\ValidationException;
use App\Enums\SheetBusinessTypeEnum;
use App\Logics\V1\MemberLogic;
use App\Logics\V1\UserLogic;
use App\Logics\V1\BuySheet\SheetUnionLogic;

class CheckoutController extends Controller
{
    /**
     * 待支付购买单筛选项
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function UnpaidFilterOptions(Request $request): JsonResponse
    {
        $publicParams = getRequestReservedParameters();

        $typeOptions = SheetBusinessTypeEnum::caseToOptions(key: 'uid');

        $membersRes = SheetUnionLogic::GetSheetMemberOptions($publicParams);
        if ($membersRes->isFail())
        {
            return outputJsonError($membersRes->getCode(), '', ['error' => $membersRes->getMessage()]);
        }

        $usersRes = SheetUnionLogic::GetCreateUsersOptions($publicParams);
        if ($usersRes->isFail())
        {
            return outputJsonError($usersRes->getCode(), '', ['error' => $usersRes->getMessage()]);
        }

        return outputJsonResult([
                                    'typeOptions'        => $typeOptions,
                                    'memberOptions'      => $membersRes->getData(),
                                    'createUsersOptions' => $usersRes->getData(),
                                ]);
    }

    /**
     * 待支付购买单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function UnpaidList(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'memberUid'     => 'sometimes|nullable|string|between:8,64',
                'createUserUid' => 'sometimes|nullable|string|between:8,64',
                'type'          => ['sometimes', 'nullable', 'string', new Enum(SheetBusinessTypeEnum::class)],
                'startDate'     => 'sometimes|nullable|date',
                'endDate'       => 'sometimes|nullable|date',
            ],
            [],
            [
                'memberUid'     => '会员UID',
                'createUserUid' => '开单人UID',
                'type'          => '业务类型',
                'startDate'     => '开单开始日期',
                'endDate'       => '开单结束日期',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $memberUid     = trim($params['memberUid'] ?? '');
        $createUserUid = trim($params['createUserUid'] ?? '');
        if (!empty($memberUid))
        {
            $getMemberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
            if ($getMemberRes->isFail())
            {
                return outputJsonError($getMemberRes->getCode(), '', ['error' => $getMemberRes->getMessage()]);
            }
            $params['memberId'] = $getMemberRes->getData('id');
        }
        if (!empty($createUserUid))
        {
            $getUserRes = UserLogic::GetValidUserByIdOrUid(userUid: $createUserUid);
            if ($getUserRes->isFail())
            {
                return outputJsonError($getUserRes->getCode(), '', ['error' => $getUserRes->getMessage()]);
            }
            $params['createUserId'] = $getUserRes->getData('id');
        }

        $unpaidListResult = SheetUnionLogic::GetUnpaidSheetList($params, $publicParams);
        if ($unpaidListResult->isFail())
        {
            return outputJsonError($unpaidListResult->getCode(),
                                   $unpaidListResult->getMessage(),
                                   ['error' => $unpaidListResult->getMessage()]);
        }

        return outputJsonResult($unpaidListResult->getData());
    }
}
