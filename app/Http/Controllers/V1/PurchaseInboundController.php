<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\PurchaseTypeEnum;
use App\Enums\PurchaseOrderInboundStatusEnum;
use App\Logics\V1\PurchaseInboundLogic;
use App\Models\PurchaseReceivedModel;
use App\Models\PurchaseReceivedDetailModel;

/**
 * Class PurchaseReceivedController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class PurchaseInboundController extends Controller
{

    /**
     * 获取采购单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'         => ['sometimes', 'nullable', 'string'],
                                         'startDate'        => ['sometimes', 'nullable', 'date'],
                                         'endDate'          => ['sometimes', 'nullable', 'date'],
                                         'inboundStatus'    => ['sometimes', 'nullable', 'integer', Rule::in(PurchaseOrderInboundStatusEnum::values())],
                                         'createUserUid'    => ['sometimes', 'nullable', 'string'],
                                         'purchaseType'     => ['sometimes', 'nullable', 'integer', Rule::in(PurchaseTypeEnum::values())],
                                         'supplierUid'      => ['sometimes', 'nullable', 'string'],
                                         'allotHospitalUid' => ['sometimes', 'nullable', 'string'],
                                         'page'             => ['sometimes', 'nullable', 'integer'],
                                         'count'            => ['sometimes', 'nullable', 'integer'],
                                     ],
                                     [],
                                     [
                                         'keywords'         => '搜索关键词',
                                         'startDate'        => '开始日期',
                                         'endDate'          => '结束日期',
                                         'inboundStatus'    => '入库状态',
                                         'createUserUid'    => '创建人',
                                         'purchaseType'     => '采购类型',
                                         'supplierUid'      => '供应商',
                                         'allotHospitalUid' => '调拨源医院',
                                         'page'             => '页码',
                                         'count'            => '每页条数',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getPurchaseListRes = PurchaseInboundLogic::GetInboundLists($searchParams, $publicParams);
        if ($getPurchaseListRes->isFail())
        {
            return outputJsonError($getPurchaseListRes->getCode(),
                                   $getPurchaseListRes->getMessage(),
                                   ['error' => $getPurchaseListRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseListRes->getData());
    }

    /**
     * 到货入库
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function StockInbound(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'receivedCode'      => ['required', 'string', 'between:8,64'],
                                         'receivedDetailUid' => ['required', 'string'],
                                         'itemUid'           => ['required', 'string'],
                                         'itemBarcode'       => ['required', 'string'],
                                         'shelfCode'         => ['required', 'string'],
                                         'packQuantity'      => ['required', 'nullable', 'integer'],
                                         'bulkQuantity'      => ['required', 'nullable', 'integer'],
                                         'productionDate'    => ['sometimes', 'nullable', 'date'],
                                         'expiredDate'       => ['sometimes', 'nullable', 'date'],
                                     ],
                                     [],
                                     [
                                         'receivedCode'      => '到货单编码',
                                         'receivedDetailUid' => '到货单明细UID',
                                         'itemUid'           => '商品UID',
                                         'itemBarcode'       => '商品条码',
                                         'shelfCode'         => '货架编码',
                                         'packQuantity'      => '整装数量',
                                         'bulkQuantity'      => '散装数量',
                                         'productionDate'    => '生产日期',
                                         'expireDate'        => '过期日期',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取参数
        $receivedParams    = $validator->validated();
        $receivedCode      = $receivedParams['receivedCode'];
        $receivedDetailUid = $receivedParams['receivedDetailUid'];
        $publicParams      = getRequestReservedParameters();

        // 获取到货单信息
        $getReceivedRes = PurchaseReceivedModel::getData(where: ['received_code' => $receivedCode]);
        $getReceivedRes = $getReceivedRes ? current($getReceivedRes) : [];
        if (empty($getReceivedRes))
        {
            return outputJsonError(42010, '到货单不存在', ['error' => '到货单不存在']);
        }

        // 获取到货单明细信息
        $getReceivedDetailRes = PurchaseReceivedDetailModel::getData(where: ['received_id' => $getReceivedRes['id'], 'uid' => $receivedDetailUid]);
        $getReceivedDetailRes = $getReceivedDetailRes ? current($getReceivedDetailRes) : [];
        if (empty($getReceivedDetailRes))
        {
            return outputJsonError(42010, '到货单明细不存在', ['error' => '到货单明细不存在']);
        }

        // 添加入库
        $getStockInboundRes = PurchaseInboundLogic::StockItemInbound($getReceivedRes['id'], $getReceivedDetailRes['id'], $receivedParams, $publicParams);
        if ($getStockInboundRes->isFail())
        {
            return outputJsonError($getStockInboundRes->getCode(),
                                   $getStockInboundRes->getMessage(),
                                   ['error' => $getStockInboundRes->getMessage()]);
        }

        return outputJsonResult($getStockInboundRes->getData());
    }
}
