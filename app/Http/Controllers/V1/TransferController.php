<?php

namespace App\Http\Controllers\V1;

use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\TransferStatusEnum;
use App\Logics\V1\TransferLogic;
use App\Models\HospitalModel;
use App\Models\TransfersModel;

/**
 * 影像相关
 * Class ImageController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class TransferController extends Controller
{
    /**
     * 转诊列表筛选项
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetTransferFilterOptions(Request $request): JsonResponse
    {
        $listType = $request->post('listType', 0);
        if (empty($listType) || !in_array($listType, [1, 2]))
        {
            return outputJsonResult();
        }

        // 获取转诊相关医院列表，本院转出：返回转入到的医院筛选列表；本院转入：返回其它医院转出的医院筛选列表
        $getFilterOptionsRes = TransferLogic::GetTransferRelationHospitalList($listType,
                                                                              getRequestReservedParameters());

        // 转诊单状态
        $transferStatus = TransferStatusEnum::options();


        $returnData = [
            'hospitalOptions' => $getFilterOptionsRes->getData(),
            'transferStatus'  => $transferStatus,
        ];

        return outputJsonResult($returnData);
    }

    /**
     * 转诊列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Lists(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(),
                                    [
                                        'listType'    => ['required', 'integer', Rule::in(1, 2)],
                                        'page'        => ['sometimes', 'nullable', 'integer'],
                                        'count'       => ['sometimes', 'nullable', 'integer'],
                                        'keyword'     => ['sometimes', 'nullable', 'string'],
                                        'startDate'   => ['sometimes', 'nullable', 'date'],
                                        'endDate'     => ['sometimes', 'nullable', 'date'],
                                        'hospitalUid' => ['sometimes', 'nullable', 'string'],
                                        'status'      => [
                                            'sometimes',
                                            'nullable',
                                            'integer',
                                            Rule::in(TransferStatusEnum::values())
                                        ],
                                    ],
                                    [],
                                    [
                                        'listType' => '列表类型',
                                        'status'   => '转诊单状态'
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $searchParams = $validate->validated();
        if (!empty($searchParams['hospitalUid']))
        {
            $getHospitalRes = HospitalModel::getOneByUid($searchParams['hospitalUid']);
            if (empty($getHospitalRes))
            {
                return outputJsonError(40311, '选择查询的医院不存在', ['error' => '选择查询的医院不存在']);
            }

            $searchParams['hospitalId'] = $getHospitalRes['id'];
        }

        $listType = (int) $validate->validated()['listType'];
        match ($listType)
        {
            // 本院转出
            1 => $getTransferListRes = TransferLogic::GetTransferOutHospitalList($searchParams,
                                                                                 getRequestReservedParameters()),

            // 本院转入
            2 => $getTransferListRes = TransferLogic::GetTransferInHospitalList($searchParams,
                                                                                getRequestReservedParameters()),
        };


        if ($getTransferListRes->isFail())
        {
            return outputJsonError($getTransferListRes->getCode(),
                                   $getTransferListRes->getMessage(),
                                   ['error' => $getTransferListRes->getMessage()]);
        }

        return outputJsonResult($getTransferListRes->getData());
    }

    /**
     * 拒绝接受转诊
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function RejectAccept(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(),
                                    [
                                        'transferCode' => ['required', 'string'],
                                        'reason'       => ['required', 'string'],
                                    ],
                                    [],
                                    [
                                        'transferCode' => '转诊编号',
                                        'reason'       => '拒绝原因',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $transferCode = $validate->validated()['transferCode'];
        $rejectReason = $validate->validated()['reason'] ?? '';

        // 转诊单信息
        $getTransferRes = TransfersModel::getData(where: ['transfer_code' => $transferCode]);
        $getTransferRes = $getTransferRes ? current($getTransferRes) : [];
        if (empty($getTransferRes))
        {
            return outputJsonError(40301, '转诊单不存在', ['error' => '转诊单不存在']);
        }

        $getRejectTransferRes = TransferLogic::RejectAcceptTransfer($getTransferRes['id'],
                                                                    $rejectReason,
                                                                    getRequestReservedParameters());
        if ($getRejectTransferRes->isFail())
        {
            return outputJsonError($getRejectTransferRes->getCode(),
                                   $getRejectTransferRes->getMessage(),
                                   ['error' => $getRejectTransferRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
