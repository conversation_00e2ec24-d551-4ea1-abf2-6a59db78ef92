<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Enums\PageEnum;
use App\Http\Controllers\Controller;
use App\Logics\V1\PetLogic;
use App\Logics\V1\CaseLogic;
use App\Logics\V1\RecipeLogic;
use App\Models\UsersModel;
use App\Models\RecipeModel;
use App\Models\CasesModel;
use App\Models\RecipeSnapshotModel;
use App\Models\RecipeTemplateModel;

/**
 * Class RecipeController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class RecipeController extends Controller
{
    /**
     * 搜索处方药品
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function SearchRecipeItems(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keyword' => ['required', 'string'],
                                         'petUid'  => ['required', 'string'],
                                     ],
                                     [
                                         'keyword.required' => '搜索关键词，必选参数错误',
                                         'petUid.required'  => '宠物UID，必选参数错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 获取宠物信息
        $petUid    = $searchParams['petUid'];
        $getPetRes = PetLogic::GetValidPetByIdOrUid(petUid: $petUid);
        if ($getPetRes->isFail())
        {
            return outputJsonError($getPetRes->getCode(),
                                   $getPetRes->getMessage(),
                                   ['error' => $getPetRes->getMessage()]);
        }

        $getSearchItemsRes = RecipeLogic::GetSearchItems($searchParams['keyword'],
                                                         $getPetRes->getData('id'),
                                                         $publicParams);
        if ($getSearchItemsRes->isFail())
        {
            return outputJsonError($getSearchItemsRes->getCode(),
                                   $getSearchItemsRes->getMessage(),
                                   ['error' => $getSearchItemsRes->getMessage()]);
        }

        return outputJsonResult($getSearchItemsRes->getData());
    }

    /**
     * 添加处方
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function AddRecipe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode'    => ['required', 'string'],
                                         'items'       => ['required', 'array'],
                                         'templateUid' => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [
                                         'caseCode.required' => '病历编码，必选参数错误',
                                         'caseCode.string'   => '病历编码，参数类型错误',
                                         'items.required'    => '处方药品，必选参数错误',
                                         'items.array'       => '处方药品，参数类型错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 业务参数
        $recipeParams = $validator->validated();
        $caseCode     = $recipeParams['caseCode'];
        $recipeItems  = $recipeParams['items'];
        $templateUid  = $recipeParams['templateUid'] ?? '';
        $publicParams = getRequestReservedParameters();

        // 获取病历信息
        $getCaseRes = CasesModel::getData(['id'],
                                          ['case_code' => $caseCode, 'hospital_id' => getPublicParamsHospitalId()]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '病历不存在', ['error' => '病历不存在']);
        }

        // 使用了处方模版，检查处方模版是否存在
        $recipeTemplateId = 0;
        if (!empty($templateUid))
        {
            $getTemplateRes = RecipeTemplateModel::getOneByUid($templateUid);
            if (empty($getTemplateRes))
            {
                return outputJsonError(39000, '处方模版不存在', ['error' => '处方模版不存在']);
            }

            $recipeTemplateId = $getTemplateRes['id'];
        }

        $addRecipeParams = ['recipeTemplateId' => $recipeTemplateId, 'items' => $recipeItems];
        $addRecipeRes    = RecipeLogic::AddRecipe($getCaseRes['id'], $addRecipeParams, $publicParams);
        if ($addRecipeRes->isFail())
        {
            return outputJsonError($addRecipeRes->getCode(),
                                   $addRecipeRes->getMessage(),
                                   ['error' => $addRecipeRes->getMessage()]);
        }

        return outputJsonResult($addRecipeRes->getData());
    }

    /**
     * 验证是否可编辑处方
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function VerifyEditRecipe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                     ],
                                     [
                                         'recipeCode.required' => '处方编码，必选参数错误',
                                         'recipeCode.string'   => '处方编码，参数类型错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取处方信息
        $recipeCode   = $validator->validated()['recipeCode'];
        $getRecipeRes = RecipeModel::getData(['id', 'case_id'],
                                             [
                                                 'recipe_code' => $recipeCode,
                                                 'hospital_id' => getPublicParamsHospitalId()
                                             ]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '处方不存在', ['error' => '处方不存在']);
        }

        $getVerifyEditRes = RecipeLogic::VerifyEditRecipe($getRecipeRes['case_id'],
                                                          $getRecipeRes['id'],
                                                          getRequestReservedParameters());
        if ($getVerifyEditRes->isFail())
        {
            return outputJsonError($getVerifyEditRes->getCode(),
                                   $getVerifyEditRes->getMessage(),
                                   ['error' => $getVerifyEditRes->getMessage()]);
        }

        return outputJsonResult($getVerifyEditRes->getData());
    }

    /**
     * 编辑处方
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function EditRecipe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                         'items'      => ['required', 'array'],
                                     ],
                                     [
                                         'recipeCode.required' => '处方编码，必选参数错误',
                                         'recipeCode.string'   => '处方编码，参数类型错误',
                                         'items.required'      => '处方药品，必选参数错误',
                                         'items.array'         => '处方药品，参数类型错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 业务参数
        $recipeParams = $validator->validated();
        $recipeCode   = $recipeParams['recipeCode'];
        $recipeItems  = $recipeParams['items'];
        $publicParams = getRequestReservedParameters();

        // 获取处方信息
        $getRecipeRes = RecipeModel::getData(['id', 'case_id'],
                                             [
                                                 'recipe_code' => $recipeCode,
                                                 'hospital_id' => getPublicParamsHospitalId()
                                             ]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '处方不存在', ['error' => '处方不存在']);
        }

        $addRecipeRes = RecipeLogic::EditRecipe($getRecipeRes['case_id'],
                                                $getRecipeRes['id'],
                                                $recipeItems,
                                                $publicParams);
        if ($addRecipeRes->isFail())
        {
            return outputJsonError($addRecipeRes->getCode(),
                                   $addRecipeRes->getMessage(),
                                   ['error' => $addRecipeRes->getMessage()]);
        }

        return outputJsonResult($addRecipeRes->getData());
    }

    /**
     * 验证是否可删除处方
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function VerifyDeleteRecipe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                     ],
                                     [
                                         'recipeCode.required' => '处方编码，必选参数错误',
                                         'recipeCode.string'   => '处方编码，参数类型错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取处方信息
        $recipeCode   = $validator->validated()['recipeCode'];
        $getRecipeRes = RecipeModel::getData(['id', 'case_id'],
                                             [
                                                 'recipe_code' => $recipeCode,
                                                 'hospital_id' => getPublicParamsHospitalId()
                                             ]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '处方不存在', ['error' => '处方不存在']);
        }

        $getVerifyDeleteRes = RecipeLogic::VerifyDeleteRecipe($getRecipeRes['case_id'],
                                                              $getRecipeRes['id'],
                                                              getRequestReservedParameters());
        if ($getVerifyDeleteRes->isFail())
        {
            return outputJsonError($getVerifyDeleteRes->getCode(),
                                   $getVerifyDeleteRes->getMessage(),
                                   ['error' => $getVerifyDeleteRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 删除处方
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function DeleteRecipe(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                     ],
                                     [
                                         'recipeCode.required' => '处方编码，必选参数错误',
                                         'recipeCode.string'   => '处方编码，参数类型错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取处方信息
        $recipeCode   = $validator->validated()['recipeCode'];
        $publicParams = getRequestReservedParameters();
        $getRecipeRes = RecipeModel::getData(['id', 'case_id'],
                                             [
                                                 'recipe_code' => $recipeCode,
                                                 'hospital_id' => getPublicParamsHospitalId()
                                             ]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '处方不存在', ['error' => '处方不存在']);
        }

        $getDeleteRes = RecipeLogic::DeleteRecipe($getRecipeRes['id'], $publicParams);
        if ($getDeleteRes->isFail())
        {
            return outputJsonError($getDeleteRes->getCode(),
                                   $getDeleteRes->getMessage(),
                                   ['error' => $getDeleteRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取病历处方
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetCaseRecipesList(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode' => ['required', 'string'],
                                     ],
                                     [
                                         'caseCode.required' => '病历编码，必选参数错误',
                                         'caseCode.string'   => '病历编码，参数类型错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取病历信息
        $caseCode   = $validator->validated()['caseCode'];
        $getCaseRes = CasesModel::getData(['id'],
                                          ['case_code' => $caseCode, 'hospital_id' => getPublicParamsHospitalId()]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '病历不存在', ['error' => '病历不存在']);
        }

        $caseId            = $getCaseRes['id'];
        $publicParams      = getRequestReservedParameters();
        $getCaseRecipesRes = RecipeLogic::GetRecipeList($caseId, $publicParams);
        if ($getCaseRecipesRes->isFail())
        {
            return outputJsonError($getCaseRecipesRes->getCode(),
                                   $getCaseRecipesRes->getMessage(),
                                   ['error' => $getCaseRecipesRes->getMessage()]);
        }

        return outputJsonResult($getCaseRecipesRes->getData());
    }

    /**
     * 获取处方详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetRecipeDetail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                     ],
                                     [
                                         'recipeCode.required' => '处方编码，必选参数错误',
                                         'recipeCode.string'   => '处方编码，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取处方信息
        $recipeCode   = $validator->validated()['recipeCode'];
        $publicParams = getRequestReservedParameters();
        $getRecipeRes = RecipeModel::getData(['id'],
                                             [
                                                 'recipe_code' => $recipeCode,
                                                 'hospital_id' => getPublicParamsHospitalId()
                                             ]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '处方不存在', ['error' => '处方不存在']);
        }

        $getRecipeDetailRes = RecipeLogic::GetRecipeDetail($getRecipeRes['id'], $publicParams);
        if ($getRecipeDetailRes->isFail())
        {
            return outputJsonError($getRecipeDetailRes->getCode(),
                                   $getRecipeDetailRes->getMessage(),
                                   ['error' => $getRecipeDetailRes->getMessage()]);
        }

        return outputJsonResult($getRecipeDetailRes->getData());
    }

    /**
     * 设置无需跟诊
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function SkipFollowUp(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                     ],
                                     [
                                         'recipeCode.required' => '处方编码，必选参数错误',
                                         'recipeCode.string'   => '处方编码，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 开始检测参数
        $validatedData = $validator->validated();
        $recipeCode    = $validatedData['recipeCode'];

        // 获取处方信息
        $getRecipeRes = RecipeModel::getData(['id'],
                                             [
                                                 'recipe_code' => $recipeCode,
                                                 'hospital_id' => getPublicParamsHospitalId()
                                             ]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '处方不存在', ['error' => '处方不存在']);
        }

        $getSkipFollowUpRes = RecipeLogic::RecipeSkipFollowUp($getRecipeRes['id'], getRequestReservedParameters());
        if ($getSkipFollowUpRes->isFail())
        {
            return outputJsonError($getSkipFollowUpRes->getCode(),
                                   $getSkipFollowUpRes->getMessage(),
                                   ['error' => $getSkipFollowUpRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 处方跟诊
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException|Throwable
     * @noinspection PhpUnused
     */
    public function FollowUpAssistant(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode'   => ['required', 'string'],
                                         'assistant1'   => ['sometimes', 'nullable', 'string'],
                                         'assistant2'   => ['sometimes', 'nullable', 'string'],
                                         'assistant3'   => ['sometimes', 'nullable', 'string'],
                                         'syncFollowUp' => ['sometimes', 'nullable', 'boolean']
                                     ],
                                     [
                                         'recipeCode.required' => '处方编码，必选参数错误',
                                         'recipeCode.string'   => '处方编码，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 开始检测参数
        $validatedData     = $validator->validated();
        $recipeCode        = $validatedData['recipeCode'];
        $oneAssistantUid   = $validatedData['assistant1'] ?? '';
        $twoAssistantUid   = $validatedData['assistant2'] ?? '';
        $threeAssistantUid = $validatedData['assistant3'] ?? '';
        $syncFollowUp      = $validatedData['syncFollowUp'] ?? false;

        // 跟诊级助理别对应关系
        $followAssistantIds = [
            1 => $oneAssistantUid,
            2 => $twoAssistantUid,
            3 => $threeAssistantUid,
        ];

        // 获取处方信息
        $getRecipeRes = RecipeModel::getData(['id'],
                                             [
                                                 'recipe_code' => $recipeCode,
                                                 'hospital_id' => getPublicParamsHospitalId()
                                             ]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '处方不存在', ['error' => '处方不存在']);
        }

        // 获取跟诊助理基本信息
        $validAssistantIds = array_filter($followAssistantIds);
        if (!empty($validAssistantIds))
        {
            $getAssistantRes = UsersModel::getData(whereIn: ['uid' => array_filter($validAssistantIds)], keyBy: 'uid');
            if (empty($getAssistantRes))
            {
                return outputJsonError(40002, '跟诊助理不存在', ['error' => '跟诊助理不存在']);
            }
        }

        $doctorIds      = [
            1 => $getAssistantRes[$oneAssistantUid]['id'] ?? 0,
            2 => $getAssistantRes[$twoAssistantUid]['id'] ?? 0,
            3 => $getAssistantRes[$threeAssistantUid]['id'] ?? 0,
        ];
        $getFollowUpRes = RecipeLogic::RecipeFollowUpAssistant($getRecipeRes['id'],
                                                               $doctorIds,
                                                               getRequestReservedParameters(),
                                                               $syncFollowUp);
        if ($getFollowUpRes->isFail())
        {
            return outputJsonError($getFollowUpRes->getCode(),
                                   $getFollowUpRes->getMessage(),

                                   ['error' => $getFollowUpRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取宠物历史处方
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetHistoryRecipes(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'petUid' => ['required', 'string'],
                                         'page'   => ['sometimes', 'required', 'integer'],
                                         'count'  => ['sometimes', 'required', 'integer'],
                                     ],
                                     [
                                         'petUid.required' => '宠物UID，必选参数错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $petUid    = $validator->validated()['petUid'];
        $iPage     = $validator->validated()['page'] ?? PageEnum::DefaultPageIndex->value;
        $iPageSize = $validator->validated()['count'] ?? PageEnum::DefaultPageSize->value;

        // 获取宠物信息
        $getPetRes = PetLogic::GetValidPetByIdOrUid(petUid: $petUid);
        if ($getPetRes->isFail())
        {
            return outputJsonError($getPetRes->getCode(),
                                   $getPetRes->getMessage(),
                                   ['error' => $getPetRes->getMessage()]);
        }

        $petId = $getPetRes->getData('id');
        if (empty($petId))
        {
            return outputJsonError(32000, '宠物不存在', ['error' => '宠物不存在']);
        }

        $getHistoryRecipesRes = RecipeLogic::GetHistoryRecipesByPetId($petId,
                                                                      getRequestReservedParameters(),
                                                                      $iPage,
                                                                      $iPageSize);
        if ($getHistoryRecipesRes->isFail())
        {
            return outputJsonError($getHistoryRecipesRes->getCode(),
                                   $getHistoryRecipesRes->getMessage(),
                                   ['error' => $getHistoryRecipesRes->getMessage()]);
        }

        return outputJsonResult($getHistoryRecipesRes->getData());
    }

    /**
     * 获取历史处方详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetHistoryRecipeDetail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                     ],
                                     [
                                         'recipeCode.required' => '处方编码，必选参数错误',
                                         'recipeCode.string'   => '处方编码，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取处方信息
        $recipeCode   = $validator->validated()['recipeCode'];
        $getRecipeRes = RecipeSnapshotModel::getData(['recipe_id'], ['recipe_code' => $recipeCode]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '处方不存在', ['error' => '处方不存在']);
        }

        // 获取处方详情
        $getRecipeDetailRes = RecipeLogic::GetHistoryRecipeDetail($getRecipeRes['recipe_id'],
                                                                  getRequestReservedParameters());
        if ($getRecipeDetailRes->isFail())
        {
            return outputJsonError($getRecipeDetailRes->getCode(),
                                   $getRecipeDetailRes->getMessage(),
                                   ['error' => $getRecipeDetailRes->getMessage()]);
        }

        return outputJsonResult($getRecipeDetailRes->getData());
    }
}
