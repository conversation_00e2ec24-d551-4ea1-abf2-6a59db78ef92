<?php

namespace App\Http\Controllers\V1;

use Exception;
use Throwable;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Requests\Form\MemberRequest;
use App\Http\Controllers\Controller;
use App\Logics\V1\MemberLogic;
use App\Models\MemberModel;
use App\Enums\MemberProfileTypeEnum;

/**
 * 会员相关
 * Class MemberController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class MemberController extends Controller
{
    /**
     * 会员列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Lists(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(), [
            'page'      => ['sometimes', 'nullable', 'integer'],
            'count'     => ['sometimes', 'nullable', 'integer'],
            'phone'     => ['sometimes', 'nullable', 'size:11'],
            'keyword'   => ['sometimes', 'nullable', 'string'],
            'startDate' => ['sometimes', 'nullable', 'date'],
            'endDate'   => ['sometimes', 'nullable', 'date'],
        ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $searchParams     = $validate->validated();
        $publicParams     = getRequestReservedParameters();
        $getMemberListRes = MemberLogic::SearchMemberList($searchParams, $publicParams);
        if ($getMemberListRes->isFail())
        {
            return outputJsonError($getMemberListRes->getCode(),
                                   $getMemberListRes->getMessage(),
                                   ['error' => $getMemberListRes->getMessage()]);
        }

        return outputJsonResult($getMemberListRes->getData());
    }

    /**
     * 新增会员
     *
     * @param MemberRequest $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     */
    public function Add(MemberRequest $request): JsonResponse
    {
        // 验证参数有效性
        $validator = $request->getValidator();
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 添加会员
        $addMemberParams = $validator->validated();
        $addMemberRes    = MemberLogic::AddMember($addMemberParams, getRequestReservedParameters());
        if ($addMemberRes->isFail())
        {
            return outputJsonError($addMemberRes->getCode(),
                                   $addMemberRes->getMessage(),
                                   ['error' => $addMemberRes->getMessage()]);
        }

        return outputJsonResult($addMemberRes->getData());
    }

    /**
     * 修改会员信息
     *
     * @param MemberRequest $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function Edit(MemberRequest $request): JsonResponse
    {
        // 宠物ID参数
        $memberUid = $request->post('memberUid', '');
        if (empty($memberUid))
        {
            return outputJsonError(400, '用户UID，必选参数错误', ['error' => '用户UID必选参数错误']);
        }

        // 验证参数有效性
        $validator = $request->getValidator();
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取会员信息
        $getMemberRes = MemberModel::getOneByUid($memberUid);
        if (empty($getMemberRes))
        {
            return outputJsonError(30002, '会员不存在', ['error' => '用户不存在']);
        }

        // 编辑会员
        $publicParams     = getRequestReservedParameters();
        $editMemberParams = $validator->validated();
        $editMemberRes    = MemberLogic::EditMember($getMemberRes['id'], $editMemberParams, $publicParams);
        if ($editMemberRes->isFail())
        {
            return outputJsonError($editMemberRes->getCode(),
                                   $editMemberRes->getMessage(),
                                   ['error' => $editMemberRes->getMessage()]);
        }

        return outputJsonResult($editMemberRes->getData());
    }

    /**
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     *
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make($request->post(),
                                     ['memberUid' => ['required', 'string', 'between:8,64']],
                                     [
                                         'memberUid.required' => '用户UID，必选',
                                         'memberUid.string'   => '用户UID，参数类型错误',
                                         'memberUid.between'  => '用户UID，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取会员信息
        $memberUid    = $validator->validated()['memberUid'];
        $getMemberRes = MemberModel::getOneByUid($memberUid);
        if (empty($getMemberRes))
        {
            return outputJsonError(30002, '会员不存在', ['error' => '用户不存在']);
        }

        // 获取会员全部信息
        $getMemberDetailFullInfo = MemberLogic::GetMemberDetailFullInfo($getMemberRes['id'],
                                                                        getPublicParamsHospitalId());
        if ($getMemberDetailFullInfo->isFail())
        {
            return outputJsonError($getMemberDetailFullInfo->getCode(),
                                   $getMemberDetailFullInfo->getMessage(),
                                   ['error' => $getMemberDetailFullInfo->getMessage()]);
        }

        return outputJsonResult($getMemberDetailFullInfo->getData());
    }

    /**
     * 获取会员微画像值
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetMicroProfile(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make($request->post(), [
            'memberUid' => ['required', 'string', 'between:8,64'],
        ],                           [
                                         'memberUid.required' => '用户UID，参数必选',
                                         'memberUid.string'   => '用户UID，参数类型错误',
                                         'memberUid.between'  => '用户UID，参数长度错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $getMicroProfileParams = $validator->validated();
        $memberUid             = $getMicroProfileParams['memberUid'];
        $getMemberRes          = MemberModel::getOneByUid($memberUid);
        if (empty($getMemberRes))
        {
            return outputJsonError(30002, '会员不存在', ['error' => '用户不存在']);
        }

        return outputJsonResult([
                                    'uid'         => $getMemberRes->uid,
                                    'Consume'     => $getMemberRes->consume_level,
                                    'Personality' => $getMemberRes->personality_level,
                                ]);
    }

    /**
     * 编辑用户画像
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function EditMicroProfile(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make($request->post(),
                                     [
                                         'memberUid'   => ['required', 'string', 'between:8,64'],
                                         'type'        => [
                                             'required',
                                             'string',
                                             Rule::in(MemberProfileTypeEnum::names())
                                         ],
                                         'levelNumber' => ['required', 'integer', 'min:0', 'max:100'],
                                     ],
                                     [
                                         'memberUid.required' => '用户UID，参数必选',
                                         'memberUid.string'   => '用户UID，参数类型错误',
                                         'memberUid.between'  => '用户UID，参数长度错误',
                                         'type.required'      => '画像类型，参数必选',
                                         'type.string'        => '画像类型，参数类型错误',
                                         'type.in'            => '画像类型，参数值错误',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $editProfileParams = $validator->validated();
        $memberUid         = $editProfileParams['memberUid'];
        $getMemberRes      = MemberModel::getOneByUid($memberUid);
        if (empty($getMemberRes))
        {
            return outputJsonError(30002, '会员不存在', ['error' => '用户不存在']);
        }

        $editProfileRes = MemberLogic::EditMicroProfile($getMemberRes['id'], $editProfileParams);
        if ($editProfileRes->isFail())
        {
            return outputJsonError($editProfileRes->getCode(),
                                   $editProfileRes->getMessage(),
                                   ['error' => $editProfileRes->getMessage()]);
        }

        return outputJsonResult($editProfileRes->getData());
    }
}
