<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Logics\V1\OutpatientLogic;
use App\Models\OutpatientModel;

/**
 * 门诊相关
 * Class OutpatientController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class OutpatientController extends Controller
{
    /**
     * 获取门诊总数
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetOutpatientTotal(): JsonResponse
    {
        $hospitalId       = getPublicParamsHospitalId();
        $doctorId         = getPublicParamsHospitalUserId();
        $getTotalCountRes = OutpatientLogic::GetOutpatientTotal($hospitalId, $doctorId);
        if ($getTotalCountRes->isFail())
        {
            return outputJsonError($getTotalCountRes->getCode(),
                                   $getTotalCountRes->getMessage(),
                                   ['error' => $getTotalCountRes->getMessage()]);
        }

        return outputJsonResult($getTotalCountRes->getData());
    }

    /**
     * 获取门诊列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Lists(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(),
                                    [
                                        'listType' => ['required', 'int', Rule::in([1, 2])],
                                    ],
                                    [
                                        'listType.required' => '列表类型，必选参数错误',
                                        'listType.int'      => '列表类型，参数类型错误',
                                        'listType.in'       => '列表类型，参数值错误',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $hospitalId = getPublicParamsHospitalId();
        $doctorId   = getPublicParamsHospitalUserId();

        // 获取门诊列表，1:候诊列表（就诊中、未就诊选择当前医生或者未指定医生）；2:暂停列表
        $listType = $validate->validated()['listType'];
        if ($listType == 1)
        {
            $getListRes = OutpatientLogic::GetUnderwayList($hospitalId, $doctorId);
        }
        else
        {
            $getListRes = OutpatientLogic::GetSuspendList($hospitalId, $doctorId);
        }

        if ($getListRes->isFail())
        {
            return outputJsonError($getListRes->getCode(),
                                   $getListRes->getMessage(),
                                   ['error' => $getListRes->getMessage()]);
        }

        return outputJsonResult($getListRes->getData());
    }

    /**
     * 门诊状态变更
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function ChangeStatus(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(),
                                    [
                                        'outpatientCode' => ['required', 'string'],
                                        'status'         => ['required', 'int', 'between:1,4'],
                                        'endOutpatient'  => ['required_if:status,4', 'array'],
                                    ],
                                    [
                                        'outpatientCode.required'   => '门诊编码，必选参数错误',
                                        'outpatientCode.string'     => '门诊编码，参数类型错误',
                                        'status.required'           => '门诊状态，必选参数错误',
                                        'status.int'                => '门诊状态，参数类型错误',
                                        'status.between'            => '门诊状态，选择无效',
                                        'endOutpatient.required_if' => '结束诊断时，结束诊断参数必选'
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $publicParams        = getRequestReservedParameters();
        $outpatientCode      = $validate->validated()['outpatientCode'];
        $outpatientStatus    = (int) $validate->validated()['status'];
        $endOutpatientParams = $request->has('endOutpatient') ? $request->post('endOutpatient') : [];

        // 获取门诊数据
        $getOutpatientRes = OutpatientModel::getOutpatientByCode($outpatientCode);
        if (empty($getOutpatientRes))
        {
            return outputJsonError(36000, '门诊不存在', ['error' => '门诊不存在']);
        }

        match ($outpatientStatus)
        {
            // 开始诊断
            1 => $dealRes = OutpatientLogic::StartOutpatient($getOutpatientRes['id'], $publicParams),

            // 暂停诊断
            2 => $dealRes = OutpatientLogic::SuspendOutpatient($getOutpatientRes['id'], $publicParams),

            // 开启暂停的诊断
            3 => $dealRes = OutpatientLogic::RestartOutpatient($getOutpatientRes['id'], $publicParams),

            // 结束诊断
            4 => $dealRes = OutpatientLogic::EndOutpatient($getOutpatientRes['id'],
                                                           $endOutpatientParams,
                                                           $publicParams),
        };

        if ($dealRes->isFail())
        {
            return outputJsonError($dealRes->getCode(),
                                   $dealRes->getMessage(),
                                   ['error' => $dealRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取医生正在诊断的门诊
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetDoctorInTreatmentOutpatient(): JsonResponse
    {
        $hospitalId = getPublicParamsHospitalId();
        $doctorId   = getPublicParamsHospitalUserId();

        $getDoctorInTreatmentOutpatientRes = OutpatientLogic::GetDoctorInTreatmentOutpatient($hospitalId, $doctorId);
        if ($getDoctorInTreatmentOutpatientRes->isFail())
        {
            return outputJsonError($getDoctorInTreatmentOutpatientRes->getCode(),
                                   $getDoctorInTreatmentOutpatientRes->getMessage(),
                                   ['error' => $getDoctorInTreatmentOutpatientRes->getMessage()]);
        }

        return outputJsonResult($getDoctorInTreatmentOutpatientRes->getData());
    }

    /**
     * 检查是否可以结束诊断
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function CheckEndOutpatient(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'outpatientCode' => ['required', 'string'],
                                    ],
                                    [
                                        'outpatientCode.required' => '门诊编码，必选参数错误',
                                        'outpatientCode.string'   => '门诊编码，参数类型错误',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $publicParams   = getRequestReservedParameters();
        $outpatientCode = $validate->validated()['outpatientCode'];

        // 获取门诊数据
        $getOutpatientRes = OutpatientModel::getOutpatientByCode($outpatientCode);
        if (empty($getOutpatientRes))
        {
            return outputJsonError(36000, '门诊不存在', ['error' => '门诊不存在']);
        }

        $checkRes = OutpatientLogic::CheckEndOutpatient($getOutpatientRes['id'], $publicParams);
        if ($checkRes->isFail())
        {
            return outputJsonError($checkRes->getCode(),
                                   $checkRes->getMessage(),
                                   ['error' => $checkRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
