<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Logics\V1\PurchaseLogic;
use App\Logics\V1\StockTransferLogic;
use App\Models\PurchaseOrderModel;

/**
 * 仓储-调拨单
 * Class StockTransferController
 * @package App\Http\Controllers\V1
 */
class StockTransferController extends Controller
{

    /**
     * 获取调拨单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'  => ['sometimes', 'nullable', 'string'],
                                         'startDate' => ['sometimes', 'nullable', 'date'],
                                         'endDate'   => ['sometimes', 'nullable', 'date'],
                                         'page'      => ['sometimes', 'nullable', 'integer'],
                                         'count'     => ['sometimes', 'nullable', 'integer'],
                                     ],
                                     [],
                                     [
                                         'keywords'  => '搜索关键词',
                                         'startDate' => '开始日期',
                                         'endDate'   => '结束日期',
                                         'page'      => '页码',
                                         'count'     => '每页条数',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getPurchaseListRes = StockTransferLogic::GetStockTransferLists($searchParams, $publicParams);
        if ($getPurchaseListRes->isFail())
        {
            return outputJsonError($getPurchaseListRes->getCode(),
                                   $getPurchaseListRes->getMessage(),
                                   ['error' => $getPurchaseListRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseListRes->getData());
    }

    /**
     * 获取调拨单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        $purchaseCode = $request->post('purchaseCode');
        if (empty($purchaseCode))
        {
            return outputJsonError(400, '调拨单编码不能为空', ['error' => '调拨单编码不能为空']);
        }

        // 获取调拨单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['purchase_code' => $purchaseCode]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return outputJsonError(42010, '调拨单不存在', ['error' => '调拨单不存在']);
        }

        $getPurchaseDetailRes = StockTransferLogic::GetTransferOrderDetail($getPurchaseOrderRes['id'], getRequestReservedParameters());
        if ($getPurchaseDetailRes->isFail())
        {
            return outputJsonError($getPurchaseDetailRes->getCode(),
                                   $getPurchaseDetailRes->getMessage(),
                                   ['error' => $getPurchaseDetailRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseDetailRes->getData());
    }

    /**
     * 删除调拨单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Delete(Request $request): JsonResponse
    {
        $purchaseCode = $request->post('purchaseCode');
        if (empty($purchaseCode))
        {
            return outputJsonError(400, '调拨单编码不能为空', ['error' => '调拨单编码不能为空']);
        }

        // 获取调拨单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['purchase_code' => $purchaseCode]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return outputJsonError(42010, '调拨单不存在', ['error' => '调拨单不存在']);
        }

        $deletePurchaseRes = PurchaseLogic::DeletePurchaseOrder($getPurchaseOrderRes['id'], getRequestReservedParameters());
        if ($deletePurchaseRes->isFail())
        {
            return outputJsonError($deletePurchaseRes->getCode(),
                                   $deletePurchaseRes->getMessage(),
                                   ['error' => $deletePurchaseRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
