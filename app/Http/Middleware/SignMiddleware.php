<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

/**
 * API请求签名验证中间件
 */
class SignMiddleware
{

    //系统预留参数，不得在接口请求参数中出现
    //**修改前，请确认没有其他位置获取以下的参数**
    const array API_RESERVED_REQUEST_PARAMETERS = [
        'version'           => null,
        'timestamp'         => null,
        'nonce'             => null,
        'signature'         => null,
        'payload'           => null,
        'token'             => null,
        '_hospitalUid'      => null,
        '_hospitalId'       => null,
        '_hospitalOrgId'    => null,
        '_hospitalOrgUid'   => null,
        '_hospitalBrandId'  => null,
        '_hospitalBrandUid' => null,
        '_userUid'          => null,
        '_userId'           => null,
        '_hospitalUserId'   => null,
        '_hospitalUserUid'  => null,
    ];

    /**
     * 处理传入请求。
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return JsonResponse|Response
     */
    public function handle(Request $request, Closure $next): JsonResponse|Response
    {
        if (!$request->hasHeader('Authorization'))
        {
            return outputJsonError(500, '', ['error' => 'Missing required request headers']);
        }

        //验证必传参数
        $validator = Validator::make($request->post(), [
            'version'   => 'required|regex:/^\d{1,2}.\d{1,2}.\d{1,2}.\d{1,4}$/',
            'timestamp' => 'required|integer|digits:10',
            'nonce'     => 'required|string|between:10,32',
            'signature' => 'required|string|size:64',
            'payload'   => 'required|json',
        ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   '',
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $token     = $request->bearerToken();
        $nonce     = $request->post('nonce', '');
        $timestamp = $request->post('timestamp');
        $signature = $request->post('signature', '');
        $payload   = $request->post('payload', '');

        //验证请求时间差
        $timeEquation = env('SYSTEM_TIME_MAX_DIFFERENCE', 600);
        if (abs(time() - intval($timestamp)) > $timeEquation)
        {
            return outputJsonError(402, '', ['error' => 'Timestamp has difference']);
        }

        if ($payload != null)
        {
            $payloadArray = json_decode($payload, true);

            //系统预留参数，不得在接口请求参数中出现
            if (!empty(array_intersect_key($payloadArray, self::API_RESERVED_REQUEST_PARAMETERS)))
            {
                return outputJsonError(500, '', ['error' => 'Hit api reserved request params']);
            }

            $request->merge($payloadArray);
        }

        $ourSignature = hash_hmac('sha256', $payload . $timestamp, $nonce . env('SYSTEM_API_SECRET_KEY'));
        if ($signature !== $ourSignature)
        {
            return outputJsonError(401,
                                   '',
                                   [
                                       'client'     => $signature,
                                       'server'     => $ourSignature,
                                       'signString' => $nonce . env('SYSTEM_API_SECRET_KEY', ''),
                                   ]);
        }

        return $next($request);
    }
}
